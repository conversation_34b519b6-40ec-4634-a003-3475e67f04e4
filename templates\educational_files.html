{% extends "base.html" %}

{% block head %}
<style>
/* تنسيقات خاصة بصفحة الملفات التعليمية للشاشات الكبيرة */
@media (min-width: 769px) {
    /* تنسيق نموذج التصفية ليكون في سطر واحد */
    #filter-form {
        display: flex;
        flex-wrap: nowrap;
        align-items: flex-end;
    }

    #filter-form .col-md-3 {
        padding-right: 10px;
        padding-left: 10px;
        flex: 1;
    }

    #filter-form .form-label {
        margin-bottom: 8px;
    }

    /* تنسيق زر التصفية ليكون بنفس ارتفاع حقول الاختيار */
    #filter-button {
        height: 38px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* تنسيقات خاصة بصفحة الملفات التعليمية للهواتف المحمولة */
@media (max-width: 768px) {
    /* تنسيقات عامة للصفحة */
    .container-fluid {
        padding: 10px !important;
    }

    .card {
        margin-bottom: 15px !important;
        border-radius: 10px !important;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1) !important;
    }

    .card-header {
        padding: 12px 15px !important;
    }

    /* تنسيق عنوان البطاقة مثل صفحة جدول التوقيت */
    .card-header h4,
    .card-header h5 {
        font-size: 1.15rem !important;
        font-weight: 600 !important;
        margin-bottom: 0 !important;
    }

    .card-body {
        padding: 15px !important;
    }

    /* تحسين نموذج رفع الملفات */
    #upload-file-form .row > div {
        margin-bottom: 15px !important;
    }

    #upload-file-form .form-label {
        font-size: 0.9rem !important;
        font-weight: 500 !important;
        margin-bottom: 5px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        color: #495057 !important;
    }

    #upload-file-form .form-control {
        font-size: 0.95rem !important;
        height: 40px !important;
        border-radius: 6px !important;
    }

    /* تصغير حجم الخط في القوائم المنسدلة */
    #upload-file-form .form-select {
        font-size: 0.85rem !important;
        height: 40px !important;
        border-radius: 6px !important;
    }

    #file-button {
        width: 100% !important;
        font-size: 0.9rem !important;
        padding: 10px 15px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        border-radius: 6px !important;
        margin-bottom: 5px !important;
        text-align: center !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    }

    /* تحسين عرض معلومات الملف المختار */
    #selected-file-info {
        margin-top: 8px !important;
    }

    #selected-file-info > div {
        margin-bottom: 5px !important;
        border-radius: 6px !important;
    }

    #upload-button {
        width: 100% !important;
        max-width: 200px !important;
        margin: 0 auto !important;
        font-size: 0.9rem !important;
        clear: both !important;
        display: block !important;
    }

    /* تحسين قسم التصفية */
    #filter-form .row > div {
        margin-bottom: 15px !important;
    }

    #filter-form .form-label {
        font-size: 0.9rem !important;
        font-weight: 500 !important;
        margin-bottom: 5px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        color: #495057 !important;
    }

    #filter-form .form-select {
        font-size: 0.85rem !important;
        height: 40px !important;
        border-radius: 6px !important;
    }

    #filter-button {
        width: 100% !important;
        font-size: 0.95rem !important;
        padding: 10px 15px !important;
        clear: both !important;
        display: block !important;
        border-radius: 6px !important;
        font-weight: 500 !important;
    }

    /* تحسين عنوان قسم الملفات */
    #files-header {
        font-size: 1.15rem !important;
        font-weight: 600 !important;
    }

    #files-count {
        font-size: 0.9rem !important;
        padding: 5px 10px !important;
        font-weight: 500 !important;
    }

    /* تنسيق الجدول للهواتف المحمولة */
    .table-responsive {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
        white-space: nowrap !important;
        margin: 0 -15px !important; /* توسيع الجدول خارج حدود البطاقة */
        padding: 0 15px !important;
    }

    /* تحديد عرض ثابت للأعمدة في الجدول */
    .table th, .table td {
        min-width: 100px !important;
        max-width: 200px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        padding: 10px 8px !important;
        font-size: 0.9rem !important;
    }

    /* تنسيق رأس الجدول */
    .table th {
        font-weight: 600 !important;
        color: #495057 !important;
    }

    /* تحديد عرض خاص لبعض الأعمدة */
    .table th:nth-child(1), .table td:nth-child(1) { /* رقم */
        min-width: 40px !important;
        max-width: 40px !important;
    }

    .table th:nth-child(2), .table td:nth-child(2) { /* اسم الملف */
        min-width: 150px !important;
        max-width: 200px !important;
    }

    .table th:nth-child(3), .table td:nth-child(3) { /* المستوى */
        min-width: 120px !important;
    }

    .table th:nth-child(4), .table td:nth-child(4) { /* الفصل */
        min-width: 100px !important;
    }

    .table th:nth-child(5), .table td:nth-child(5) { /* النوع */
        min-width: 80px !important;
    }

    .table th:nth-child(6), .table td:nth-child(6) { /* الوصف */
        min-width: 120px !important;
    }

    .table th:nth-child(7), .table td:nth-child(7) { /* الحجم */
        min-width: 80px !important;
    }

    .table th:nth-child(8), .table td:nth-child(8) { /* الرافع */
        min-width: 100px !important;
    }

    .table th:nth-child(9), .table td:nth-child(9) { /* تاريخ الرفع */
        min-width: 120px !important;
    }

    .table th:nth-child(10), .table td:nth-child(10) { /* التحميلات */
        min-width: 80px !important;
    }

    .table th:nth-child(11), .table td:nth-child(11) { /* الإجراءات */
        min-width: 100px !important;
    }

    /* تحسين عرض الأزرار في الجدول */
    .table .btn-sm {
        padding: 4px 8px !important;
        font-size: 0.8rem !important;
    }

    /* تثبيت العمود الأول (الرقم) */
    .table-fixed-first-column th:first-child,
    .table-fixed-first-column td:first-child {
        position: sticky !important;
        left: 0 !important;
        z-index: 1 !important;
        background-color: #fff !important;
    }

    /* تثبيت رأس الجدول */
    .table-fixed-header thead th {
        position: sticky !important;
        top: 0 !important;
        z-index: 2 !important;
        background-color: #f8f9fa !important;
    }

    /* تثبيت العمود الأول ورأس الجدول معًا */
    .table-fixed-header thead th:first-child {
        z-index: 3 !important;
    }

    /* تحسين عرض الشارات */
    .badge {
        font-size: 0.75rem !important;
        padding: 3px 6px !important;
    }



    /* تأثير بصري عند التمرير */
    .table-responsive {
        position: relative;
        transition: all 0.3s ease;
    }

    .table-responsive.scrolled::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 15px;
        background: linear-gradient(to right, rgba(0,0,0,0.05), transparent);
        z-index: 4;
        pointer-events: none;
    }

    .table-responsive.scrolled::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
        width: 15px;
        background: linear-gradient(to left, rgba(0,0,0,0.05), transparent);
        z-index: 4;
        pointer-events: none;
    }
}

/* تنسيقات إضافية للهواتف الصغيرة جداً */
@media (max-width: 576px) {
    .container-fluid {
        padding: 8px !important;
    }

    .card-header {
        padding: 10px 12px !important;
    }

    .card-body {
        padding: 12px !important;
    }

    /* تعديل عرض زر اختيار الملف ليكون في الأعلى لوحده */
    #upload-file-form .col-md-3:first-child {
        width: 100% !important;
        float: none !important;
        padding: 0 5px !important;
        margin-bottom: 15px !important;
        clear: both !important;
    }

    /* إعادة ترتيب الحقول لتكون مثنى مثنى بالترتيب الصحيح */
    /* المستوى والفصل الدراسي في صف واحد */
    #upload-file-form .col-md-2:nth-child(2),  /* المستوى */
    #upload-file-form .col-md-2:nth-child(3) { /* الفصل الدراسي */
        width: 50% !important;
        float: right !important;
        padding: 0 5px !important;
        margin-bottom: 15px !important;
        clear: none !important;
    }

    /* نوع الملف ووصف الملف في صف واحد */
    #upload-file-form .col-md-2:nth-child(4),  /* نوع الملف */
    #upload-file-form .col-md-3:nth-child(5) { /* وصف الملف */
        width: 50% !important;
        float: right !important;
        padding: 0 5px !important;
        margin-bottom: 15px !important;
        clear: none !important;
    }

    /* تأكيد بدء صف جديد */
    #upload-file-form .col-md-2:nth-child(2) {
        clear: right !important;
    }

    #upload-file-form .col-md-2:nth-child(4) {
        clear: right !important;
    }

    /* إصلاح مشكلة العرض في الهواتف */
    #upload-file-form .row {
        display: block !important;
        margin-right: -5px !important;
        margin-left: -5px !important;
    }

    /* إزالة margin-bottom الافتراضي من Bootstrap */
    #upload-file-form .mb-3 {
        margin-bottom: 0 !important;
    }

    /* تعديل عرض الأعمدة في نموذج التصفية - مثنى مثنى */
    /* إعادة تنسيق نموذج التصفية بالكامل */
    #filter-form {
        display: block !important;
    }

    /* المستوى والفصل الدراسي في صف واحد */
    #filter-form .col-md-3:nth-child(1),
    #filter-form .col-md-3:nth-child(2) {
        width: 50% !important;
        float: right !important;
        padding: 0 5px !important;
        margin-bottom: 15px !important;
        clear: none !important;
    }

    /* نوع الملف وزر التصفية في صف واحد */
    #filter-form .col-md-3:nth-child(3),
    #filter-form .col-md-3:nth-child(4) {
        width: 50% !important;
        float: right !important;
        padding: 0 5px !important;
        clear: none !important;
    }

    /* تأكيد بدء صف جديد */
    #filter-form .col-md-3:nth-child(1) {
        clear: right !important;
    }

    #filter-form .col-md-3:nth-child(3) {
        clear: right !important;
    }

    /* إزالة margin-bottom الافتراضي من Bootstrap */
    #filter-form .mb-3 {
        margin-bottom: 0 !important;
    }

    /* تعديل عرض زر التصفية */
    #filter-button {
        margin-top: 0 !important;
        height: 40px !important; /* نفس ارتفاع حقول الاختيار */
    }

    /* تعديل عرض حقل نوع الملف في التصفية */
    #filter-form .col-md-3:nth-child(3) .form-label {
        margin-bottom: 8px !important;
    }

    /* إضافة clearfix بعد كل صفين */
    #upload-file-form .row::after,
    #filter-form::after {
        content: "";
        display: table;
        clear: both;
    }

    /* تحسين عرض الأزرار في الهواتف */
    #upload-button {
        margin-top: 15px !important;
        clear: both !important;
        display: block !important;
        width: 100% !important;
        max-width: 200px !important;
        margin-right: auto !important;
        margin-left: auto !important;
    }

    /* جعل الأزرار الثلاثة متجاورة أفقياً ومتساوية في الحجم */
    .table .btn-group {
        display: flex !important;
        justify-content: space-between !important;
        width: 100% !important;
    }

    .table .btn-group .btn {
        flex: 1 !important;
        margin: 0 2px !important;
        text-align: center !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* إعادة لون زر التحميل إلى الأخضر */
    .table .btn-group .btn-outline-success {
        color: #28a745 !important;
        border-color: #28a745 !important;
    }

    .table .btn-group .btn-outline-success:hover {
        background-color: #28a745 !important;
        color: white !important;
    }

    /* إعادة لون زر الحذف إلى الأحمر */
    .table .btn-group .btn-outline-danger {
        color: #dc3545 !important;
        border-color: #dc3545 !important;
    }

    .table .btn-group .btn-outline-danger:hover {
        background-color: #dc3545 !important;
        color: white !important;
    }

    /* تصغير حجم الخط في الجدول */
    .table th, .table td {
        font-size: 0.85rem !important;
        padding: 8px 6px !important;
    }

    /* تحسين عرض الأزرار في الجدول */
    .table .btn-sm {
        padding: 5px 8px !important;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- حذف العنوان الرئيسي -->

    <!-- قسم رفع ملف تعليمي جديد -->
    <div class="card shadow-sm mb-4 border-0 bg-white rounded-3">
        <div class="card-header bg-gradient bg-success text-white d-flex justify-content-between align-items-center rounded-top">
            <h4 class="mb-0"><i class="fas fa-cloud-upload-alt me-2"></i>رفع ملف تعليمي جديد</h4>
        </div>
        <div class="card-body">
            <form id="upload-file-form" enctype="multipart/form-data" novalidate>
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="row gx-3">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="file" class="form-label text-muted fw-bold">اختر الملف</label>
                            <div class="input-group mb-0">
                                <input type="file" class="form-control" id="file" name="file" required onchange="showSelectedFile(this)" style="display: none;" multiple>
                                <button type="button" class="btn btn-outline-primary w-100 text-start" id="file-button" onclick="document.getElementById('file').click()" style="width: 100%;">
                                    <i class="fas fa-file-upload me-1"></i> اختيار ملف
                                </button>
                            </div>
                            <div id="selected-file-info" class="mb-0 mt-2"></div>
                            <div id="file-error-feedback" class=""></div>
                            <div id="file-type-info" class="text-danger mt-2" style="font-size: 0.75rem; display: none;">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                صيغة الملف غير صالحة
                            </div>
                            <small class="text-muted d-block" style="font-size: 0.75rem;">الملفات المسموح بها: PDF, DOC, DOCX, XLS, XLSX</small>
                            <small class="text-muted d-block" style="font-size: 0.75rem;">الحد الأقصى لحجم الملف: 10 ميجابايت</small>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="mb-3">
                            <label for="level" class="form-label text-muted fw-bold">المستوى</label>
                            <select class="form-select shadow-none" id="level" name="level" required style="font-size: 0.9rem; width: 100%;">
                                <option value="السنة الأولى متوسط">السنة الأولى متوسط</option>
                                <option value="السنة الثانية متوسط">السنة الثانية متوسط</option>
                                <option value="السنة الثالثة متوسط">السنة الثالثة متوسط</option>
                                <option value="السنة الرابعة متوسط">السنة الرابعة متوسط</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="mb-3">
                            <label for="semester" class="form-label text-muted fw-bold">الفصل الدراسي</label>
                            <select class="form-select shadow-none" id="semester" name="semester" required style="font-size: 0.9rem; width: 100%;">
                                <option value="1">الفصل الأول</option>
                                <option value="2">الفصل الثاني</option>
                                <option value="3">الفصل الثالث</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="mb-3">
                            <label for="file_type" class="form-label text-muted fw-bold">نوع الملف</label>
                            <select class="form-select shadow-none" id="file_type" name="file_type" required style="font-size: 0.9rem; width: 100%;">
                                <option value="مذكرات">مذكرات</option>
                                <option value="فروض">فروض</option>
                                <option value="اختبارات">اختبارات</option>
                                <option value="ملفات أخرى">ملفات أخرى</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="description" class="form-label text-muted fw-bold">وصف الملف</label>
                            <input type="text" class="form-control shadow-none" id="description" name="description" placeholder="اختياري" style="font-size: 0.9rem; width: 100%;">
                        </div>
                    </div>
                </div>
                <div class="text-center mt-1">
                    <button type="button" class="btn btn-success px-4 py-2" id="upload-button">
                        <i class="fas fa-cloud-upload-alt me-1"></i> رفع الملف
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- عوامل التصفية -->
    <div class="card shadow-sm mb-4 border-0 bg-white rounded-3">
        <div class="card-header bg-light d-flex justify-content-between align-items-center rounded-top">
            <h5 class="mb-0 text-primary"><i class="fas fa-filter me-2"></i>تصفية الملفات</h5>
        </div>
        <div class="card-body">
            <form id="filter-form">
                <div class="col-md-3">
                    <label for="filter_level" class="form-label text-muted fw-bold">المستوى</label>
                    <select class="form-select shadow-none" id="filter_level" name="level">
                        <option value="all" {% if current_level == 'all' %}selected{% endif %}>جميع المستويات</option>
                        <option value="السنة الأولى متوسط" {% if current_level == 'السنة الأولى متوسط' %}selected{% endif %}>السنة الأولى متوسط</option>
                        <option value="السنة الثانية متوسط" {% if current_level == 'السنة الثانية متوسط' %}selected{% endif %}>السنة الثانية متوسط</option>
                        <option value="السنة الثالثة متوسط" {% if current_level == 'السنة الثالثة متوسط' %}selected{% endif %}>السنة الثالثة متوسط</option>
                        <option value="السنة الرابعة متوسط" {% if current_level == 'السنة الرابعة متوسط' %}selected{% endif %}>السنة الرابعة متوسط</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="filter_semester" class="form-label text-muted fw-bold">الفصل الدراسي</label>
                    <select class="form-select shadow-none" id="filter_semester" name="semester">
                        <option value="all" {% if current_semester == 'all' %}selected{% endif %}>جميع الفصول</option>
                        <option value="1" {% if current_semester == 1 %}selected{% endif %}>الفصل الأول</option>
                        <option value="2" {% if current_semester == 2 %}selected{% endif %}>الفصل الثاني</option>
                        <option value="3" {% if current_semester == 3 %}selected{% endif %}>الفصل الثالث</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="filter_file_type" class="form-label text-muted fw-bold">نوع الملف</label>
                    <select class="form-select shadow-none" id="filter_file_type" name="file_type">
                        <option value="all" {% if current_file_type == 'all' %}selected{% endif %}>جميع الملفات</option>
                        <option value="مذكرات" {% if current_file_type == 'مذكرات' %}selected{% endif %}>مذكرات</option>
                        <option value="فروض" {% if current_file_type == 'فروض' %}selected{% endif %}>فروض</option>
                        <option value="اختبارات" {% if current_file_type == 'اختبارات' %}selected{% endif %}>اختبارات</option>
                        <option value="ملفات أخرى" {% if current_file_type == 'ملفات أخرى' %}selected{% endif %}>ملفات أخرى</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label text-muted fw-bold">تصفية</label>
                    <button type="button" id="filter-button" class="btn btn-primary w-100">
                        <i class="fas fa-search me-1"></i> تصفية
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- عرض الملفات -->
    <div class="card shadow-sm border-0 bg-white rounded-3" id="files-container">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center rounded-top">
            <h5 class="mb-0 fs-5 fw-bold" id="files-header">
                <i class="fas fa-folder-open me-2"></i>
                {% if current_level != 'all' %}
                    {{ current_level }}
                {% else %}
                    جميع المستويات
                {% endif %}
                -
                {% if current_semester == 'all' %}
                    جميع الفصول
                {% elif current_semester == 1 %}
                    الفصل الأول
                {% elif current_semester == 2 %}
                    الفصل الثاني
                {% else %}
                    الفصل الثالث
                {% endif %}
                {% if current_file_type != 'all' %}
                    - {{ current_file_type }}
                {% endif %}
            </h5>
            <span class="badge bg-light text-primary rounded-pill fs-6 px-3 py-2" id="files-count">{{ files|length }} ملف</span>
        </div>
        <div class="card-body p-0" id="files-list">
            {% if files %}

                <div class="table-responsive">
                    <table class="table table-hover mb-0 table-fixed-header table-fixed-first-column">
                        <thead class="table-light">
                            <tr>
                                <th scope="col" class="text-muted">#</th>
                                <th scope="col" class="text-muted">اسم الملف</th>
                                <th scope="col" class="text-muted">المستوى</th>
                                <th scope="col" class="text-muted">الفصل</th>
                                <th scope="col" class="text-muted">النوع</th>
                                <th scope="col" class="text-muted">الوصف</th>
                                <th scope="col" class="text-muted">الحجم</th>
                                <th scope="col" class="text-muted">الرافع</th>
                                <th scope="col" class="text-muted">تاريخ الرفع</th>
                                <th scope="col" class="text-muted">التحميلات</th>
                                <th scope="col" class="text-muted">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="files-tbody">
                            {% for file in files %}
                            <tr id="file-row-{{ file.id }}" class="align-middle">
                                <td>{{ loop.index }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% set ext = file.original_filename.split('.')[-1].lower() %}
                                        {% if ext in ['pdf'] %}
                                            <i class="fas fa-file-pdf text-danger me-2"></i>
                                        {% elif ext in ['doc', 'docx'] %}
                                            <i class="fas fa-file-word text-primary me-2"></i>
                                        {% elif ext in ['xls', 'xlsx'] %}
                                            <i class="fas fa-file-excel text-success me-2"></i>
                                        {% elif ext in ['ppt', 'pptx'] %}
                                            <i class="fas fa-file-powerpoint text-warning me-2"></i>
                                        {% elif ext in ['jpg', 'jpeg', 'png', 'gif'] %}
                                            <i class="fas fa-file-image text-info me-2"></i>
                                        {% else %}
                                            <i class="fas fa-file text-secondary me-2"></i>
                                        {% endif %}
                                        {{ file.original_filename }}
                                    </div>
                                </td>
                                <td>{{ file.level }}</td>
                                <td>{% if file.semester == 1 %}الفصل الأول{% elif file.semester == 2 %}الفصل الثاني{% else %}الفصل الثالث{% endif %}</td>
                                <td><span class="badge bg-secondary rounded-pill">{{ file.file_type }}</span></td>
                                <td>{{ file.description or "لا يوجد وصف" }}</td>
                                <td>{{ (file.file_size / 1024)|round(1) }} كيلوبايت</td>
                                <td>{{ file.uploader.teacher_name }}</td>
                                <td>{{ file.upload_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td><span class="badge bg-info rounded-pill">{{ file.download_count }}</span></td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('download_educational_file', file_id=file.id) }}" class="btn btn-sm btn-outline-success rounded-pill me-1" title="تحميل الملف">
                                            <i class="fas fa-download"></i>
                                        </a>

                                        {% if current_user.is_admin or current_user.id == file.user_id %}
                                        <button type="button" class="btn btn-sm btn-outline-danger rounded-pill delete-file"
                                                data-id="{{ file.id }}" title="حذف الملف">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info text-center m-3 rounded-3 shadow-sm">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد ملفات متاحة ضمن معايير البحث الحالية.
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// إضافة دالة عرض الرسائل
function showAlert(message, type = 'success') {
    // إنشاء عنصر التنبيه
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show shadow-sm`;
    alertDiv.role = 'alert';

    // إضافة أيقونة مناسبة للرسالة
    let icon = 'check-circle';
    if (type === 'danger') icon = 'exclamation-circle';
    else if (type === 'warning') icon = 'exclamation-triangle';
    else if (type === 'info') icon = 'info-circle';

    // إضافة المحتوى للتنبيه
    alertDiv.innerHTML = `
        <i class="fas fa-${icon} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
    `;

    // تحديد المكان المناسب لإضافة التنبيه
    const container = document.querySelector('.container-fluid');
    if (container) {
        // إضافة التنبيه في أعلى الصفحة
        container.insertBefore(alertDiv, container.firstChild);

        // إخفاء التنبيه تلقائيًا بعد 5 ثوانٍ
        setTimeout(() => {
            alertDiv.classList.remove('show');
            setTimeout(() => alertDiv.remove(), 300);
        }, 5000);
    }
}

// دالة لعرض الملفات المختارة
function showSelectedFile(input) {
    const fileInfoDiv = document.getElementById('selected-file-info');
    const fileErrorDiv = document.getElementById('file-error-feedback');
    const fileTypeInfo = document.getElementById('file-type-info');

    // Limpiar mensajes de error previos
    fileErrorDiv.innerHTML = '';
    fileErrorDiv.className = '';

    // إخفاء تنبيه نوع الملف بشكل افتراضي
    fileTypeInfo.style.display = 'none';

    if (input.files && input.files.length > 0) {
        fileInfoDiv.innerHTML = '';

        // Validar cada archivo seleccionado
        let hasInvalidFile = false;
        let hasOversizedFile = false;
        let validFiles = [];

        for(let i = 0; i < input.files.length; i++) {
            const file = input.files[i];
            const fileName = file.name;
            const fileSize = file.size / (1024 * 1024); // tamaño en MB
            const ext = fileName.split('.').pop().toLowerCase();

            // Validar la extensión
            if (!['pdf', 'doc', 'docx', 'xls', 'xlsx'].includes(ext)) {
                hasInvalidFile = true;
                continue;
            }

            // Validar el tamaño
            if (fileSize > 10) {
                hasOversizedFile = true;
                continue;
            }

            validFiles.push(file);

            // تحديد نوع الملف وأيقونته
            let fileIcon = '<i class="fas fa-file text-secondary me-1" style="font-size: 0.85rem;"></i>';

            if (ext === 'pdf') {
                fileIcon = '<i class="fas fa-file-pdf text-danger me-1" style="font-size: 0.85rem;"></i>';
            } else if (['doc', 'docx'].includes(ext)) {
                fileIcon = '<i class="fas fa-file-word text-primary me-1" style="font-size: 0.85rem;"></i>';
            } else if (['xls', 'xlsx'].includes(ext)) {
                fileIcon = '<i class="fas fa-file-excel text-success me-1" style="font-size: 0.85rem;"></i>';
            }

            const fileElement = document.createElement('div');
            fileElement.className = 'd-flex align-items-center p-2 border rounded-3 bg-light mb-2 shadow-sm';
            fileElement.style.fontSize = '0.85rem';

            // إنشاء هوية فريدة لكل ملف
            const fileId = `file-${Date.now()}-${i}`;
            fileElement.id = fileId;

            fileElement.innerHTML = `
                ${fileIcon}
                <span class="me-1 text-dark">${fileName}</span>
                <span class="text-muted small ms-1">(${(fileSize).toFixed(2)} MB)</span>
                <button type="button" class="btn btn-link text-danger p-0 me-auto" style="font-size: 0.85rem;" onclick="clearFileInput()">
                    <i class="fas fa-times-circle"></i>
                </button>
            `;

            fileInfoDiv.appendChild(fileElement);
        }

        // Mostrar mensajes de error si es necesario
        if (hasInvalidFile) {
            fileTypeInfo.style.display = 'block';
            return false;
        }

        if (hasOversizedFile) {
            fileErrorDiv.innerHTML += '<div class="text-danger mt-1"><i class="fas fa-exclamation-circle me-1"></i>يجب أن لا يتجاوز حجم الملف 10 ميجابايت.</div>';
            fileErrorDiv.className = 'invalid-feedback d-block';
        }

        // Si no hay archivos válidos, limpiar la entrada de archivo
        if (validFiles.length === 0) {
            clearFileInput();
        }
    } else {
        fileInfoDiv.innerHTML = '';
    }
}

// حل جديد لحذف الملفات - سيعمل في كافة المتصفحات
function clearFileInput() {
    const input = document.getElementById('file');
    input.value = '';
    document.getElementById('selected-file-info').innerHTML = '';
    document.getElementById('file-type-info').style.display = 'none';
}

function clearAllFiles() {
    const input = document.getElementById('file');
    input.value = '';
    document.getElementById('selected-file-info').innerHTML = '';
    document.getElementById('file-type-info').style.display = 'none';
}

// إضافة معالج أحداث لنموذج رفع الملفات
document.addEventListener('DOMContentLoaded', function() {
    const uploadForm = document.getElementById('upload-file-form');
    const uploadButton = document.getElementById('upload-button');
    const fileInput = document.getElementById('file');

    // إضافة معالج أحداث لزر التصفية
    const filterButton = document.getElementById('filter-button');
    if (filterButton) {
        filterButton.addEventListener('click', function() {
            filterFiles();
        });
    }

    if (uploadButton) {
        uploadButton.addEventListener('click', function() {
            // التحقق من وجود ملف
            if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
                showAlert('الرجاء اختيار ملف واحد على الأقل للرفع', 'danger');
                return false;
            }

            // Validar los archivos seleccionados
            let hasInvalidFile = false;
            let hasOversizedFile = false;

            for(let i = 0; i < fileInput.files.length; i++) {
                const file = fileInput.files[i];
                const fileName = file.name;
                const fileSize = file.size / (1024 * 1024); // tamaño en MB
                const ext = fileName.split('.').pop().toLowerCase();

                // Validar la extensión
                if (!['pdf', 'doc', 'docx', 'xls', 'xlsx'].includes(ext)) {
                    hasInvalidFile = true;
                }

                // Validar el tamaño
                if (fileSize > 10) {
                    hasOversizedFile = true;
                }
            }

            if (hasInvalidFile) {
                showAlert('صيغة الملف غير صالحة', 'danger');
                return false;
            }

            if (hasOversizedFile) {
                showAlert('يجب أن لا يتجاوز حجم الملف 10 ميجابايت.', 'danger');
                return false;
            }

            console.log("ملفات مختارة:", fileInput.files.length);

            // تغيير حالة الزر إلى "جاري الرفع"
            uploadButton.disabled = true;
            uploadButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري الرفع...';

            // إنشاء كائن FormData
            const formData = new FormData();

            // إضافة عناصر النموذج يدوياً
            formData.append('csrf_token', document.querySelector('input[name="csrf_token"]').value);
            formData.append('level', document.getElementById('level').value);
            formData.append('semester', document.getElementById('semester').value);
            formData.append('file_type', document.getElementById('file_type').value);
            formData.append('description', document.getElementById('description').value);

            // إضافة الملفات المختارة
            for(let i = 0; i < fileInput.files.length; i++) {
                formData.append('file', fileInput.files[i]);
                console.log("تمت إضافة الملف:", fileInput.files[i].name);
            }

            // إرسال الطلب باستخدام AJAX
            fetch('/upload_educational_file', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log("استجابة الخادم:", response.status);
                return response.json();
            })
            .then(data => {
                // إعادة تمكين الزر
                uploadButton.disabled = false;

                console.log("بيانات الاستجابة:", data);

                if (data.success) {
                    // عرض رسالة النجاح على الزر نفسه
                    uploadButton.innerHTML = '<i class="fas fa-check-circle me-1"></i> تم الرفع بنجاح';
                    uploadButton.classList.remove('btn-success');
                    uploadButton.classList.add('btn-outline-success');

                    // إعادة ضبط النموذج
                    uploadForm.reset();
                    document.getElementById('selected-file-info').innerHTML = '';
                    document.getElementById('file-type-info').style.display = 'none';

                    // تحديث قائمة الملفات
                    filterFiles();

                    // إعادة الزر إلى حالته الأصلية بعد 2 ثانية
                    setTimeout(() => {
                        uploadButton.innerHTML = '<i class="fas fa-cloud-upload-alt me-1"></i> رفع الملف';
                        uploadButton.classList.remove('btn-outline-success');
                        uploadButton.classList.add('btn-success');
                    }, 2000);
                } else {
                    // إعادة الزر لحالته الأصلية
                    uploadButton.innerHTML = '<i class="fas fa-cloud-upload-alt me-1"></i> رفع الملف';

                    // عرض رسائل الخطأ
                    let errorMessage = data.message;
                    if (data.error_messages && data.error_messages.length > 0) {
                        errorMessage += ': ' + data.error_messages.join(', ');
                    }
                    showAlert(errorMessage, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                uploadButton.disabled = false;
                uploadButton.innerHTML = '<i class="fas fa-cloud-upload-alt me-1"></i> رفع الملف';
                showAlert('حدث خطأ أثناء رفع الملف، يرجى المحاولة مرة أخرى', 'danger');
            });
        });
    }

    // اعتبر أن الفصل الدراسي في نموذج الرفع يجب أن يتوافق مع الفصل المختار في التصفية
    const filterSemester = document.getElementById('filter_semester');
    const formSemester = document.getElementById('semester');

    if (filterSemester && formSemester) {
        // تعيين قيمة نموذج الرفع ليتوافق مع قيمة التصفية الحالية
        if (filterSemester.value !== 'all') {
            formSemester.value = filterSemester.value;
        } else {
            formSemester.value = '1'; // الخيار الافتراضي لنموذج الرفع عندما تكون التصفية "جميع الفصول"
        }
    }
});

// معالجة تصفية الملفات بدون تحديث الصفحة
function filterFiles() {
    const level = document.getElementById('filter_level').value;
    const semester = document.getElementById('filter_semester').value;
    const fileType = document.getElementById('filter_file_type').value;

    // إظهار مؤشر التحميل
    document.getElementById('files-list').innerHTML = `
        <div class="text-center p-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2">جاري تحميل الملفات...</p>
        </div>
    `;

    // استدعاء AJAX لتحديث قائمة الملفات
    fetch(`/get_filtered_files?level=${encodeURIComponent(level)}&semester=${semester}&file_type=${encodeURIComponent(fileType)}`)
        .then(response => response.json())
        .then(data => {
            updateFilesUI(data);
        })
        .catch(error => {
            console.error('Error fetching files:', error);
            document.getElementById('files-list').innerHTML = `
                <div class="alert alert-danger text-center">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    حدث خطأ أثناء تحميل الملفات، يرجى المحاولة مرة أخرى.
                </div>
            `;
        });
}

// تحديث واجهة المستخدم بالملفات المفلترة
function updateFilesUI(data) {
    // تحديث عنوان قسم الملفات
    let headerText = data.level_text;
    headerText += ` - ${data.semester_text}`;

    if (data.file_type !== 'all') {
        headerText += ` - ${data.file_type}`;
    }

    document.getElementById('files-header').innerHTML = `<i class="fas fa-folder-open me-2"></i>${headerText}`;
    document.getElementById('files-count').textContent = `${data.files.length} ملف`;

    // تحديث قائمة الملفات
    if (data.files.length > 0) {
        let tableHTML = `
            <div class="table-responsive">
                <table class="table table-hover mb-0 table-fixed-header table-fixed-first-column">
                    <thead class="table-light">
                        <tr>
                            <th scope="col" class="text-muted">#</th>
                            <th scope="col" class="text-muted">اسم الملف</th>
                            <th scope="col" class="text-muted">المستوى</th>
                            <th scope="col" class="text-muted">الفصل</th>
                            <th scope="col" class="text-muted">النوع</th>
                            <th scope="col" class="text-muted">الوصف</th>
                            <th scope="col" class="text-muted">الحجم</th>
                            <th scope="col" class="text-muted">الرافع</th>
                            <th scope="col" class="text-muted">تاريخ الرفع</th>
                            <th scope="col" class="text-muted">التحميلات</th>
                            <th scope="col" class="text-muted">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="files-tbody">
        `;

        data.files.forEach((file, index) => {
            let semesterText = 'الفصل الأول';
            if (file.semester === 2) semesterText = 'الفصل الثاني';
            else if (file.semester === 3) semesterText = 'الفصل الثالث';

            let extension = file.original_filename.split('.').pop().toLowerCase();
            let fileIcon = '<i class="fas fa-file text-secondary me-2"></i>';

            if (extension === 'pdf') {
                fileIcon = '<i class="fas fa-file-pdf text-danger me-2"></i>';
            } else if (['doc', 'docx'].includes(extension)) {
                fileIcon = '<i class="fas fa-file-word text-primary me-2"></i>';
            } else if (['xls', 'xlsx'].includes(extension)) {
                fileIcon = '<i class="fas fa-file-excel text-success me-2"></i>';
            }

            tableHTML += `
                <tr id="file-row-${file.id}" class="align-middle">
                    <td>${index + 1}</td>
                    <td>
                        <div class="d-flex align-items-center">
                            ${fileIcon}
                            ${file.original_filename}
                        </div>
                    </td>
                    <td>${file.level}</td>
                    <td>${semesterText}</td>
                    <td><span class="badge bg-secondary rounded-pill">${file.file_type}</span></td>
                    <td>${file.description || "لا يوجد وصف"}</td>
                    <td>${(file.file_size / 1024).toFixed(1)} كيلوبايت</td>
                    <td>${file.uploader_name}</td>
                    <td>${file.upload_date}</td>
                    <td><span class="badge bg-info rounded-pill">${file.download_count}</span></td>
                    <td>
                        <div class="btn-group">
                            <a href="/download_educational_file/${file.id}" class="btn btn-sm btn-outline-success rounded-pill me-1" title="تحميل الملف">
                                <i class="fas fa-download"></i>
                            </a>

                            ${file.can_delete ? `
                                <button type="button" class="btn btn-sm btn-outline-danger rounded-pill delete-file"
                                        data-id="${file.id}" title="حذف الملف">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            ` : ''}
                        </div>
                    </td>
                </tr>
            `;
        });

        tableHTML += `
                    </tbody>
                </table>
            </div>
        `;

        document.getElementById('files-list').innerHTML = tableHTML;

        // إعادة إضافة معالجات أحداث لأزرار الحذف
        document.querySelectorAll('.delete-file').forEach(button => {
            button.addEventListener('click', handleDeleteFile);
        });
    } else {
        document.getElementById('files-list').innerHTML = `
            <div class="alert alert-info text-center m-3 rounded-3 shadow-sm">
                <i class="fas fa-info-circle me-2"></i>
                لا توجد ملفات متاحة ضمن معايير البحث الحالية.
            </div>
        `;
    }

    // تحديث URL الصفحة دون إعادة تحميلها
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('level', data.level);
    urlParams.set('semester', data.semester);
    urlParams.set('file_type', data.file_type);

    const newUrl = window.location.pathname + '?' + urlParams.toString();
    history.pushState(null, '', newUrl);

    // تحديث قيم النموذج في قسم الرفع
    if (document.getElementById('semester')) {
        document.getElementById('semester').value = data.semester === 'all' ? '1' : data.semester;
    }

    if (document.getElementById('level')) {
        document.getElementById('level').value = data.level === 'all' ? 'السنة الأولى متوسط' : data.level;
    }
}

// حذف ملف بدون تحديث الصفحة
function handleDeleteFile(event) {
    const fileId = this.getAttribute('data-id');
    const csrfToken = document.querySelector('input[name="csrf_token"]').value;

    // حذف الملف مباشرة بدون تأكيد
    fetch(`/delete_educational_file/${fileId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            csrf_token: csrfToken
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // حذف الصف من الجدول
            const row = document.getElementById(`file-row-${fileId}`);
            if (row) {
                row.remove();
            }

            // تحديث عداد الملفات
            const countElement = document.getElementById('files-count');
            const currentCount = parseInt(countElement.textContent.split(' ')[0]);
            const newCount = currentCount - 1;
            countElement.textContent = `${newCount} ملف`;

            // إعادة ترقيم الصفوف المتبقية
            const tbody = document.getElementById('files-tbody');
            if (tbody) {
                const rows = tbody.querySelectorAll('tr');
                rows.forEach((row, index) => {
                    row.cells[0].textContent = index + 1;
                });
            }

            // إذا لم يبق أي ملفات، عرض رسالة بدلاً من الجدول
            if (newCount === 0) {
                document.getElementById('files-list').innerHTML = `
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle me-2"></i>
                        لا توجد ملفات متاحة ضمن معايير البحث الحالية.
                    </div>
                `;
            }
        } else {
            // عرض رسالة خطأ
            showAlert(data.message || 'حدث خطأ أثناء حذف الملف', 'danger');
        }
    })
    .catch(error => {
        console.error('Error deleting file:', error);
        showAlert('حدث خطأ أثناء حذف الملف', 'danger');
    });
}

// إضافة معالجات أحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // ... الكود السابق ...

    // إضافة معالجات أحداث للتصفية
    const filterButton = document.getElementById('filter-button');
    if (filterButton) {
        filterButton.addEventListener('click', filterFiles);
    }

    // تغيير سلوك قوائم التصفية لتحديث الصفحة تلقائياً
    const filterSelects = ['filter_level', 'filter_semester', 'filter_file_type'];
    filterSelects.forEach(id => {
        const select = document.getElementById(id);
        if (select) {
            select.addEventListener('change', filterFiles);
        }
    });

    // إضافة معالجات أحداث لأزرار حذف الملفات
    document.querySelectorAll('.delete-file').forEach(button => {
        button.addEventListener('click', handleDeleteFile);
    });

    // تحسينات خاصة بالهواتف المحمولة
    if (window.innerWidth <= 768) {

        // تحسين تجربة التمرير في الجدول
        const tableResponsive = document.querySelector('.table-responsive');
        if (tableResponsive) {
            // إضافة تأثير بصري عند التمرير
            tableResponsive.addEventListener('scroll', function() {
                if (this.scrollLeft > 0) {
                    this.classList.add('scrolled');
                } else {
                    this.classList.remove('scrolled');
                }
            });
        }

        // تحسين عرض نموذج رفع الملفات
        const uploadForm = document.getElementById('upload-file-form');
        if (uploadForm) {
            const formColumns = uploadForm.querySelectorAll('.col-md-2, .col-md-3');
            formColumns.forEach(col => {
                col.classList.add('mb-3');
            });
        }

        // تحسين عرض نموذج التصفية
        const filterForm = document.getElementById('filter-form');
        if (filterForm) {
            const formColumns = filterForm.querySelectorAll('.col-md-3');
            formColumns.forEach(col => {
                col.classList.add('mb-2');
            });
        }
    }
});
</script>

{% endblock %}