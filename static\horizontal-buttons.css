/*
 * ملف CSS خاص بتنسيق الأزرار الثلاثة
 * يضمن أن تكون الأزرار أفقية في وضع الهاتف
 */

/* تنسيق أساسي للأزرار */
.vertical-buttons-container {
    position: absolute;
    z-index: 1000;
    display: flex;
    gap: 10px;
}

/* تنسيق للشاشات الكبيرة - أفقي */
@media (min-width: 769px) {
    .vertical-buttons-container {
        top: 20px;
        left: 20px;
        flex-direction: row; /* تغيير من عمودي إلى أفقي */
        gap: 15px; /* زيادة المسافة بين الأزرار */
    }

    .vertical-buttons-container .btn {
        width: 50px;
        height: 50px;
    }
}

/* تنسيق للشاشات الصغيرة - أفقي */
@media (max-width: 768px) {
    .vertical-buttons-container {
        top: 15px !important; /* تعديل المسافة من الأعلى لتتناسب مع الزر الأخضر */
        left: 10px !important;
        gap: 10px !important;
        display: flex !important;
        flex-direction: row !important;
        position: absolute !important;
        z-index: 1050 !important;
    }

    .vertical-buttons-container .btn {
        width: 35px !important;
        height: 35px !important;
        font-size: 0.8rem !important;
        padding: 6px !important; /* تعديل المسافة الداخلية للأزرار */
    }
}

/* تأكيد إضافي على أن الأزرار أفقية في وضع الهاتف */
@media (max-width: 768px) {
    .vertical-buttons-container {
        flex-direction: row !important;
    }
}
