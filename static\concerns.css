/* تنسيقات صفحة انشغالات الأساتذة */
:root {
    --primary-color: #1976d2;
    --primary-dark: #0d47a1;
    --primary-light: #e3f2fd;
    --secondary-color: #2c3e50;
    --accent-color: #28a745;
    --accent-dark: #1e7e34;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --text-color: #333;
    --border-radius: 10px;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    --transition: all 0.3s ease;
}

.concerns-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* تصميم رأس الصفحة */
.page-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border-radius: var(--border-radius);
    padding: 20px 15px;
    text-align: center;
    margin: 0 auto 25px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.15);
    width: 100%;
    max-width: 950px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.page-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
    z-index: 1;
}

.page-header h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 8px;
    position: relative;
    z-index: 2;
}

.page-description {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.5;
    position: relative;
    z-index: 2;
}

.page-subtitle {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 10px;
}

/* تنسيقات زر إضافة انشغال */
.add-concern-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    background: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
    color: white;
    border: none;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 2px 6px rgba(40, 167, 69, 0.2);
}

/* زر مع نص */
.add-concern-btn-text {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.85rem;
}

/* زر دائري بدون نص */
.add-concern-btn:not(.add-concern-btn-text) {
    padding: 10px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 1rem;
}

.add-concern-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(40, 167, 69, 0.3);
}

.add-concern-btn:active {
    transform: translateY(0);
}

/* أيقونة الزر النصي */
.add-concern-btn-text i {
    margin-left: 6px;
    font-size: 0.9rem;
}

/* أيقونة الزر الدائري */
.add-concern-btn:not(.add-concern-btn-text) i {
    margin: 0;
    font-size: 1.1rem;
}

/* تنسيقات الروابط في الردود ومحتوى الانشغال */
.reply-content a, .concern-details-content a, .concern-content a {
    color: var(--primary-color);
    text-decoration: none;
    border-bottom: 1px dashed var(--primary-color);
    transition: all 0.2s ease;
    word-break: break-word;
}

.reply-content a:hover, .concern-details-content a:hover, .concern-content a:hover {
    color: var(--primary-dark);
    border-bottom: 1px solid var(--primary-dark);
}

/* تنسيقات بطاقات الانشغالات */
.concerns-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 18px;
}

.concern-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    transition: var(--transition);
    border: none;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.concern-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
}

.concern-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(to bottom, var(--primary-color), var(--primary-dark));
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.concern-header {
    padding: 12px 15px;
    background-color: var(--primary-light);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.concern-title {
    font-size: 1.05rem;
    font-weight: 700;
    color: var(--primary-dark);
    margin-bottom: 6px;
    line-height: 1.4;
}

.concern-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: #666;
}

.concern-teacher {
    display: flex;
    align-items: center;
    background-color: rgba(25, 118, 210, 0.1);
    padding: 3px 6px;
    border-radius: 12px;
    font-size: 0.8rem;
}

.concern-teacher i {
    margin-left: 3px;
    color: var(--primary-color);
    font-size: 0.8rem;
}

.concern-date {
    display: flex;
    align-items: center;
    background-color: rgba(40, 167, 69, 0.1);
    padding: 3px 6px;
    border-radius: 12px;
    font-size: 0.8rem;
}

.concern-date i {
    margin-left: 3px;
    color: var(--accent-color);
    font-size: 0.8rem;
}

.concern-body {
    padding: 12px 15px;
    flex-grow: 1;
    background-color: white;
}

.concern-content {
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--text-color);
    white-space: pre-line;
}

.concern-footer {
    padding: 8px 15px;
    background-color: var(--light-color);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    font-size: 0.8rem;
    color: #666;
    display: flex;
    align-items: center;
}

.concern-footer i {
    margin-left: 6px;
    color: var(--primary-color);
}

/* تنسيقات للرسالة عند عدم وجود انشغالات */
.no-concerns {
    text-align: center;
    padding: 40px 20px;
    background-color: white;
    border-radius: var(--border-radius);
    margin-top: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

.no-concerns .add-concern-btn {
    margin-top: 15px;
    margin-bottom: 0;
}

.no-concerns::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, var(--primary-color), var(--accent-color));
}

.no-concerns i {
    font-size: 3rem;
    color: #e0e0e0;
    margin-bottom: 15px;
    display: block;
}

.no-concerns h3 {
    color: var(--secondary-color);
    margin-bottom: 10px;
    font-weight: 600;
    font-size: 1.2rem;
}

.no-concerns p {
    color: #666;
    max-width: 450px;
    margin: 0 auto;
    line-height: 1.5;
    font-size: 0.9rem;
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 992px) {
    .concerns-list {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .concerns-container {
        padding: 0;
    }

    .concerns-list {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .page-header {
        padding: 20px 15px;
        margin: 0 auto 20px;
        width: 95%;
        max-width: 95%;
        border-radius: var(--border-radius);
        box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .page-header h1 {
        font-size: 1.6rem;
        margin-bottom: 6px;
    }

    .page-description {
        font-size: 0.85rem;
    }

    .page-subtitle {
        font-size: 0.9rem;
        padding: 0 5px;
        margin-top: 8px;
        font-weight: 500;
        white-space: nowrap;
        overflow: visible;
        width: 100%;
        text-align: center;
        display: block;
    }

    /* تنسيقات التبويبات للهاتف */
    .nav-tabs {
        display: flex;
        justify-content: center;
        border: none;
        margin-bottom: 0;
        padding: 0;
        width: 100%;
        gap: 5px;
    }

    .tab-content {
        margin-top: 10px;
    }

    .nav-tabs .nav-item {
        flex: 0 0 auto;
        margin: 0;
    }

    .nav-tabs .nav-link {
        padding: 6px 2px !important;
        font-size: 0.75rem;
        text-align: center;
        border: none;
        border-radius: 20px;
        background-color: #f0f0f0;
        color: #666;
        transition: all 0.3s ease;
        white-space: nowrap;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        display: inline-block;
        width: auto;
        min-width: 0;
    }

    .nav-tabs .nav-link.active {
        background-color: var(--primary-color);
        color: white;
        box-shadow: 0 2px 5px rgba(25, 118, 210, 0.3);
    }

    .nav-tabs .nav-link:hover:not(.active) {
        background-color: #e0e0e0;
    }

    .nav-tabs .nav-link i {
        font-size: 0.75rem;
        margin-left: 3px !important;
        margin-right: 0 !important;
    }

    .nav-tabs .nav-link .badge {
        font-size: 0.65rem;
        padding: 2px 4px;
        margin-right: 0;
        margin-left: 2px;
    }

    /* تنسيقات عامة للزر في وضع الهاتف */
    .add-concern-btn {
        margin-bottom: 15px;
    }

    /* زر مع نص في وضع الهاتف */
    .add-concern-btn-text {
        padding: 6px 12px;
        font-size: 0.8rem;
    }

    /* زر دائري بدون نص في وضع الهاتف */
    .add-concern-btn:not(.add-concern-btn-text) {
        width: 36px;
        height: 36px;
        padding: 8px;
        font-size: 0.9rem;
    }

    .concern-meta {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 8px;
    }

    .concern-teacher, .concern-date {
        width: auto;
    }

    .concern-card {
        box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
    }

    .concern-header {
        padding: 10px 12px;
    }

    .concern-body {
        padding: 10px 12px;
    }

    .concern-footer {
        padding: 6px 12px;
    }
}

@media (max-width: 576px) {
    .page-header {
        width: 92%;
        max-width: 92%;
        border-radius: 15px;
    }

    .page-header h1 {
        font-size: 1.4rem;
    }

    .page-subtitle {
        font-size: 0.85rem;
        margin-top: 5px;
    }

    /* تنسيقات إضافية للتبويبات في الشاشات الصغيرة جدًا */
    .nav-tabs {
        margin-bottom: 0;
    }

    .tab-content {
        margin-top: 10px;
    }

    .nav-tabs .nav-item {
        margin: 0;
    }

    .nav-tabs .nav-link {
        padding: 5px 2px !important;
        font-size: 0.7rem;
        border-radius: 15px;
        display: inline-block;
    }

    .nav-tabs .nav-link i {
        font-size: 0.65rem !important;
        display: none; /* إخفاء الأيقونات في الشاشات الصغيرة جدًا */
    }

    .nav-tabs .nav-link .badge {
        font-size: 0.6rem;
        padding: 1px 3px;
    }

    .concern-title {
        font-size: 1rem;
    }
}

/* تنسيقات للشاشات الصغيرة جدًا */
@media (max-width: 375px) {
    .nav-tabs {
        gap: 3px;
        margin-bottom: 0;
    }

    .tab-content {
        margin-top: 10px;
    }

    .nav-tabs .nav-link {
        padding: 4px 2px !important;
        font-size: 0.65rem;
        display: inline-block;
        width: auto;
    }

    .nav-tabs .nav-link .badge {
        font-size: 0.55rem;
        padding: 1px 2px;
    }

    .concern-content {
        font-size: 0.85rem;
    }

    .no-concerns {
        padding: 30px 15px;
    }

    .no-concerns i {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }

    .no-concerns h3 {
        font-size: 1.1rem;
    }

    .no-concerns p {
        font-size: 0.85rem;
    }
}

/* ===== تنسيقات صفحة إدارة الانشغالات ===== */
.admin-concerns-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0;
}

.admin-concerns-container .page-header {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
}

/* تنسيقات التبويبات */
.nav-tabs {
    border: none;
    margin-bottom: 0;
    display: flex;
    justify-content: center;
    background-color: white;
    border-radius: var(--border-radius);
    padding: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.nav-tabs .nav-item {
    margin: 0 3px;
}

.nav-tabs .nav-link {
    border: none;
    color: var(--dark-color);
    font-weight: 600;
    padding: 8px 15px;
    border-radius: 20px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    z-index: 1;
    font-size: 0.85rem;
    white-space: nowrap;
}

.nav-tabs .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.1), rgba(13, 71, 161, 0.1));
    opacity: 0;
    transition: var(--transition);
    z-index: -1;
}

.nav-tabs .nav-link:hover::before {
    opacity: 1;
}

.nav-tabs .nav-link.active {
    color: white;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    box-shadow: 0 3px 8px rgba(25, 118, 210, 0.25);
}

.nav-tabs .nav-link .badge {
    margin-right: 3px;
    transition: var(--transition);
    font-size: 0.7rem;
    padding: 3px 6px;
}

.tab-content {
    padding: 0;
    margin-top: 10px;
}

/* تنسيقات بطاقات الانشغالات في صفحة الإدارة */
.admin-concerns-container .tab-pane {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0;
    margin-top: 0;
    padding-top: 0;
}

.admin-concerns-container .concern-card {
    margin-bottom: 0;
    margin-top: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.admin-concerns-container .concern-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 3px;
    height: 100%;
}

#pending .concern-card::before {
    background: linear-gradient(to bottom, var(--warning-color), var(--warning-dark));
}

#approved .concern-card::before {
    background: linear-gradient(to bottom, var(--accent-color), var(--accent-dark));
}

#rejected .concern-card::before {
    background: linear-gradient(to bottom, var(--danger-color), var(--danger-dark));
}

.admin-concerns-container .concern-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--light-color);
    padding: 10px 15px;
}

.admin-concerns-container .concern-title {
    margin: 0;
    color: var(--secondary-color);
    font-size: 1rem;
    font-weight: 600;
}

.concern-status {
    padding: 4px 8px;
    border-radius: 20px;
    font-size: 0.7rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
}

.concern-status i {
    margin-left: 3px;
    font-size: 0.75rem;
}

.status-pending {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.status-approved {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    color: white;
}

.status-rejected {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.admin-concerns-container .concern-meta {
    padding: 10px 15px;
    background-color: rgba(248, 249, 250, 0.7);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-concerns-container .concern-teacher {
    padding: 3px 8px;
    font-size: 0.8rem;
}

.admin-concerns-container .concern-teacher i {
    margin-left: 5px;
    font-size: 0.8rem;
}

.admin-concerns-container .concern-date {
    padding: 3px 8px;
    font-size: 0.8rem;
}

.admin-concerns-container .concern-date i {
    margin-left: 5px;
    font-size: 0.8rem;
}

.admin-concerns-container .concern-body {
    padding: 12px 15px;
}

.admin-concerns-container .concern-content {
    margin-bottom: 15px;
    padding: 0;
    font-size: 0.9rem;
}

.admin-concerns-container .concern-workplace {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 10px;
}

.admin-concerns-container .concern-footer {
    padding: 10px 15px;
}

/* تنسيقات أزرار الموافقة والرفض */
.concern-actions {
    display: flex;
    justify-content: flex-start; /* تغيير من flex-end إلى flex-start للمحاذاة لليمين */
    gap: 10px;
    padding: 0 15px 15px;
    margin-top: -5px;
    direction: ltr; /* استخدام direction: ltr لعكس ترتيب الأزرار */
}

#pending .btn-approve, #pending .btn-reject {
    display: flex;
    align-items: center;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.8rem;
    transition: var(--transition);
}

#pending .btn-approve {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    box-shadow: 0 2px 6px rgba(46, 204, 113, 0.2);
}

#pending .btn-approve:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(46, 204, 113, 0.25);
    background: linear-gradient(135deg, #27ae60, #219653);
}

#pending .btn-reject {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    box-shadow: 0 2px 6px rgba(243, 156, 18, 0.2);
}

#pending .btn-reject:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(243, 156, 18, 0.25);
    background: linear-gradient(135deg, #e67e22, #d35400);
}

#approved .btn-approve {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    box-shadow: 0 2px 6px rgba(46, 204, 113, 0.2);
}

#approved .btn-approve:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(46, 204, 113, 0.25);
    background: linear-gradient(135deg, #27ae60, #219653);
}

#rejected .btn-reject {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    box-shadow: 0 2px 6px rgba(231, 76, 60, 0.2);
}

#rejected .btn-reject:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(231, 76, 60, 0.25);
    background: linear-gradient(135deg, #c0392b, #a93226);
}

.btn-approve i, .btn-reject i, .btn-delete i {
    margin-right: 0; /* إزالة الهامش اليمين */
    margin-left: 8px; /* إضافة هامش يسار للفصل بين الأيقونة والعبارة */
    font-size: 0.8rem;
}

.btn-approve:disabled, .btn-reject:disabled, .btn-delete:disabled {
    opacity: 0.8;
    cursor: default;
    transform: none !important;
    box-shadow: none !important;
}

/* تنسيقات زر الحذف */
.btn-delete {
    display: flex;
    align-items: center;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.8rem;
    transition: var(--transition);
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    box-shadow: 0 2px 6px rgba(231, 76, 60, 0.2);
}

.btn-delete:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(231, 76, 60, 0.25);
    background: linear-gradient(135deg, #c0392b, #a93226);
}

/* تنسيقات التعليقات */
.concern-comment {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    margin-top: 12px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.04);
    position: relative;
}

.concern-comment::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 3px;
    height: 100%;
    border-radius: 0 8px 8px 0;
}

#approved .concern-comment::before {
    background: linear-gradient(to bottom, #2ecc71, #27ae60);
}

#rejected .concern-comment::before {
    background: linear-gradient(to bottom, #e74c3c, #c0392b);
}

.comment-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 0.8rem;
    color: #666;
    border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
    padding-bottom: 8px;
}

.comment-header div {
    display: flex;
    align-items: center;
}

.comment-header i {
    margin-left: 5px;
    font-size: 0.8rem;
}

.comment-content {
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--text-color);
}

/* تنسيقات للرسالة عند عدم وجود انشغالات في صفحة الإدارة */
.admin-concerns-container .no-concerns {
    padding: 30px 15px;
    margin-top: 0;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.04);
}

#pending .no-concerns::before {
    background: linear-gradient(to right, #f39c12, #e67e22);
    height: 3px;
}

#approved .no-concerns::before {
    background: linear-gradient(to right, #2ecc71, #27ae60);
    height: 3px;
}

#rejected .no-concerns::before {
    background: linear-gradient(to right, #e74c3c, #c0392b);
    height: 3px;
}

.admin-concerns-container .no-concerns i {
    font-size: 2.5rem;
    margin-bottom: 10px;
    color: #ddd;
}

.admin-concerns-container .no-concerns h3 {
    font-size: 1.1rem;
    margin-bottom: 8px;
}

.admin-concerns-container .no-concerns p {
    font-size: 0.85rem;
    max-width: 400px;
    margin: 0 auto;
}

/* تنسيقات للشاشات الصغيرة في صفحة الإدارة */
@media (max-width: 992px) {
    .admin-concerns-container {
        padding: 15px 10px;
    }

    .nav-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        padding: 3px;
        margin-bottom: 15px;
    }

    .nav-tabs .nav-link {
        white-space: nowrap;
        padding: 6px 12px;
        font-size: 0.8rem;
    }

    .nav-tabs .nav-item {
        margin: 0 2px;
    }

    .tab-content {
        padding: 0;
    }
}

@media (max-width: 768px) {
    .admin-concerns-container .page-header {
        padding: 15px 10px;
        margin-bottom: 15px;
    }

    .admin-concerns-container .page-header h1 {
        font-size: 1.4rem;
    }

    .admin-concerns-container .concern-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        padding: 8px 12px;
    }

    .admin-concerns-container .concern-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
        padding: 8px 12px;
    }

    .admin-concerns-container .concern-body {
        padding: 10px 12px;
    }

    .admin-concerns-container .concern-actions {
        flex-direction: row;
        gap: 8px;
        padding: 5px 12px 12px;
    }

    .btn-approve, .btn-reject {
        flex: 1;
        justify-content: center;
    }

    .concern-comment {
        padding: 12px;
        margin-top: 10px;
    }
}

@media (max-width: 576px) {
    .admin-concerns-container .page-header h1 {
        font-size: 1.3rem;
    }

    .nav-tabs .nav-link {
        padding: 5px 10px;
        font-size: 0.75rem;
    }

    .nav-tabs .nav-link .badge {
        font-size: 0.65rem;
        padding: 2px 4px;
    }

    .admin-concerns-container .concern-title {
        font-size: 0.95rem;
    }

    .admin-concerns-container .concern-content {
        font-size: 0.85rem;
    }

    .admin-concerns-container .concern-workplace {
        font-size: 0.8rem;
    }

    .btn-approve, .btn-reject {
        padding: 5px 10px;
        font-size: 0.75rem;
    }

    .btn-approve i, .btn-reject i {
        font-size: 0.75rem;
    }
}
