{% extends 'base.html' %}

{% block title %}انشغالات الأساتذة{% endblock %}

{% block head %}
<link href="{{ url_for('static', filename='concerns.css') }}" rel="stylesheet">
<!-- إضافة رمز CSRF للحماية من هجمات التزوير -->
<meta name="csrf-token" content="{{ csrf_token() }}">
<style>
    /* تنسيقات النموذج */
    .modal-content {
        border-radius: 8px;
        border: none;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        overflow: hidden;
    }

    .modal-header {
        background: linear-gradient(135deg, #1976d2, #0d47a1);
        color: white;
        border: none;
        padding: 12px 15px;
    }

    .modal-title {
        font-weight: 600;
        font-size: 1rem;
    }

    .modal-body {
        padding: 15px;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 4px;
        font-size: 0.9rem;
    }

    .form-control {
        border-radius: 6px;
        padding: 8px 10px;
        border: 1px solid #e0e0e0;
        transition: all 0.3s;
        font-size: 0.9rem;
    }

    .form-control:focus {
        border-color: #1976d2;
        box-shadow: 0 0 0 0.15rem rgba(25, 118, 210, 0.15);
    }

    .alert-info {
        background-color: #e3f2fd;
        border-color: rgba(25, 118, 210, 0.3);
        color: #0d47a1;
        border-radius: 6px;
        padding: 10px;
        font-size: 0.85rem;
    }

    .alert-info i {
        color: #1976d2;
        margin-left: 5px;
    }

    .modal-footer {
        padding: 10px 15px;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }

    .btn-primary {
        background: linear-gradient(135deg, #1976d2, #0d47a1);
        border: none;
        padding: 6px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.85rem;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 8px rgba(25, 118, 210, 0.2);
    }

    .btn-secondary {
        background-color: #f8f9fa;
        color: #495057;
        border: none;
        padding: 6px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.85rem;
        transition: all 0.3s;
    }

    .btn-secondary:hover {
        background-color: #e9ecef;
    }

    /* تنسيقات مودال النجاح */
    #successModal .modal-header {
        background: linear-gradient(135deg, #28a745, #1e7e34);
        padding: 10px 15px;
    }

    #successModal .modal-body {
        text-align: center;
        padding: 20px 15px;
    }

    #successModal .modal-body p {
        font-size: 0.9rem;
        margin-bottom: 0;
    }

    #successModal .btn-success {
        background: linear-gradient(135deg, #28a745, #1e7e34);
        border: none;
        padding: 6px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.85rem;
        transition: all 0.3s;
    }

    #successModal .btn-success:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 8px rgba(40, 167, 69, 0.2);
    }

    /* تنسيقات بطاقة الانشغال */
    .concern-card {
        cursor: pointer;
        transition: transform 0.2s, box-shadow 0.2s;
    }

    .concern-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .concern-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .concern-replies-count {
        font-size: 0.8rem;
        color: #666;
        display: flex;
        align-items: center;
    }

    .concern-replies-count i {
        margin-left: 5px;
        color: var(--primary-color);
    }

    .replies-count {
        font-weight: bold;
        margin: 0 3px;
    }

    /* تنسيقات نموذج تفاصيل الانشغال */
    .concern-details-container {
        background-color: #fff;
    }

    .concern-details-header {
        background-color: var(--primary-light);
    }

    .concern-details-meta {
        font-size: 0.85rem;
        color: #666;
    }

    .concern-details-teacher i,
    .concern-details-date i,
    .concern-details-workplace i {
        color: var(--primary-color);
        margin-left: 5px;
    }

    .concern-details-content {
        background-color: #fff;
        line-height: 1.6;
    }

    /* تنسيقات قسم الردود */
    .concern-replies-section {
        background-color: #f8f9fa;
    }

    .replies-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .reply-item {
        background-color: #fff;
        border-radius: 6px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        margin-bottom: 10px;
        overflow: hidden;
    }

    .reply-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px 10px;
        background-color: rgba(25, 118, 210, 0.05);
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .reply-user {
        display: flex;
        align-items: center;
    }

    .reply-user-name {
        font-weight: 600;
        font-size: 0.8rem;
        margin-left: 4px;
    }

    .reply-admin-badge {
        background-color: var(--primary-color);
        color: white;
        font-size: 0.65rem;
        padding: 1px 4px;
        border-radius: 3px;
        margin-right: 4px;
    }

    .reply-date {
        font-size: 0.7rem;
        color: #666;
    }

    .reply-content {
        padding: 8px 10px;
        font-size: 0.8rem;
        line-height: 1.4;
    }

    .reply-actions {
        display: flex;
        justify-content: flex-end;
        padding: 0 10px 6px;
        gap: 4px;
    }

    .reply-edit-btn,
    .reply-delete-btn {
        background: none;
        border: none;
        font-size: 0.7rem;
        color: #666;
        cursor: pointer;
        padding: 2px 4px;
        border-radius: 3px;
        transition: all 0.2s;
    }

    .reply-edit-btn:hover {
        background-color: rgba(25, 118, 210, 0.1);
        color: var(--primary-color);
    }

    .reply-delete-btn:hover {
        background-color: rgba(231, 76, 60, 0.1);
        color: #e74c3c;
    }

    .reply-edit-btn i,
    .reply-delete-btn i {
        margin-left: 2px;
        font-size: 0.65rem;
    }

    /* تنسيقات للشاشات الصغيرة */
    @media (max-width: 768px) {
        .concern-details-meta {
            flex-direction: column;
            align-items: flex-start;
        }

        .concern-details-meta > div {
            margin-bottom: 5px;
        }

        .reply-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .reply-date {
            margin-top: 5px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    <div class="concerns-container">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <h1>انشغالات الأساتذة</h1>
            <p class="page-description page-subtitle">
                مشاركة انشغالات الأساتذة ومناقشتها بشكل بناء
            </p>
        </div>

        <!-- قسم المقدمة -->
        <div class="text-center mb-3">
            {% if not concerns %}
            <p class="page-description mb-3">
                هذه الصفحة تعرض انشغالات الأساتذة التي تمت الموافقة عليها من قبل الإدارة.
                تهدف هذه المنصة إلى تحسين العملية التعليمية من خلال مشاركة الأفكار والانشغالات بشكل منظم.
            </p>
            {% endif %}

            <!-- زر إضافة انشغال جديد -->
            <button class="add-concern-btn add-concern-btn-text" data-bs-toggle="modal" data-bs-target="#addConcernModal">
                <i class="fas fa-plus-circle"></i> إضافة انشغال جديد
            </button>
        </div>

        <!-- قائمة الانشغالات -->
        <div class="concerns-list">
            {% if concerns %}
                {% for concern in concerns %}
                    <div class="concern-card" data-concern-id="{{ concern.id }}" onclick="openConcernDetails({{ concern.id }})">
                        <div class="concern-header">
                            <h3 class="concern-title">{{ concern.title }}</h3>
                            <div class="concern-meta">
                                <div class="concern-teacher">
                                    <i class="fas fa-user"></i>
                                    <span>{{ concern.teacher.teacher_name }}</span>
                                </div>
                                <div class="concern-date">
                                    <i class="far fa-calendar-alt"></i>
                                    <span>{{ concern.created_at.strftime('%Y-%m-%d') }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="concern-body">
                            <div class="concern-content">{{ concern.content }}</div>
                        </div>
                        <div class="concern-footer">
                            <div>
                                <i class="fas fa-building"></i>
                                <span>{{ concern.teacher.workplace }}</span>
                            </div>
                            <div class="concern-replies-count">
                                <i class="fas fa-comments"></i>
                                <span class="replies-count" id="replies-count-{{ concern.id }}">0</span>
                                <span class="replies-text">ردود</span>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="no-concerns col-12">
                    <i class="far fa-comment-dots"></i>
                    <h3>لا توجد انشغالات حالياً</h3>
                    <p>لم يتم إضافة أي انشغالات بعد أو لم تتم الموافقة على أي انشغالات من قبل الإدارة. كن أول من يشارك انشغالاته بالنقر على زر "إضافة انشغال جديد".</p>
                    <button class="add-concern-btn" data-bs-toggle="modal" data-bs-target="#addConcernModal" title="إضافة انشغال جديد">
                        <i class="fas fa-plus-circle"></i>
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- نموذج إضافة انشغال جديد -->
<div class="modal fade" id="addConcernModal" tabindex="-1" aria-labelledby="addConcernModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addConcernModalLabel">
                    <i class="fas fa-plus-circle me-1" style="font-size: 0.85rem;"></i> إضافة انشغال جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="concernForm">
                    <div class="mb-3">
                        <label for="title" class="form-label">
                            <i class="fas fa-heading me-1" style="color: var(--primary-color); font-size: 0.8rem;"></i>
                            عنوان الانشغال
                        </label>
                        <input type="text" class="form-control" id="title" name="title" placeholder="أدخل عنوانًا واضحًا ومختصرًا للانشغال" required>
                    </div>
                    <div class="mb-3">
                        <label for="content" class="form-label">
                            <i class="fas fa-align-left me-1" style="color: var(--primary-color); font-size: 0.8rem;"></i>
                            محتوى الانشغال
                        </label>
                        <textarea class="form-control" id="content" name="content" rows="5" placeholder="اشرح انشغالك بالتفصيل هنا..." required></textarea>
                    </div>
                    <div class="alert alert-info">
                        <div class="d-flex">
                            <div class="me-2">
                                <i class="fas fa-info-circle" style="font-size: 1.2rem;"></i>
                            </div>
                            <div>
                                <h6 class="alert-heading mb-1">ملاحظة هامة</h6>
                                <p class="mb-0 small">سيتم مراجعة الانشغال من قبل الإدارة قبل نشره على الصفحة. يرجى التأكد من وضوح المحتوى وأهميته.</p>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1" style="font-size: 0.8rem;"></i> إلغاء
                </button>
                <button type="button" class="btn btn-primary" id="submitConcern">
                    <i class="fas fa-paper-plane me-1" style="font-size: 0.8rem;"></i> إرسال
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تفاصيل الانشغال -->
<div class="modal fade" id="concernDetailsModal" tabindex="-1" aria-labelledby="concernDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title" id="concernDetailsModalLabel" style="font-size: 0.95rem;">
                    <i class="fas fa-comment-alt me-1" style="font-size: 0.8rem;"></i> تفاصيل الانشغال
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body p-0">
                <div class="concern-details-container">
                    <!-- تفاصيل الانشغال -->
                    <div class="concern-details-header p-2">
                        <h4 id="concern-details-title" class="mb-2" style="font-size: 1.1rem;"></h4>
                        <div class="concern-details-meta d-flex justify-content-between align-items-center" style="font-size: 0.8rem;">
                            <div class="d-flex align-items-center">
                                <div class="concern-details-teacher me-3">
                                    <i class="fas fa-user"></i>
                                    <span id="concern-details-teacher"></span>
                                </div>
                                <div class="concern-details-date">
                                    <i class="far fa-calendar-alt"></i>
                                    <span id="concern-details-date"></span>
                                </div>
                            </div>
                            <div class="concern-details-workplace">
                                <i class="fas fa-building"></i>
                                <span id="concern-details-workplace"></span>
                            </div>
                        </div>
                    </div>
                    <div class="concern-details-content p-2 border-top border-bottom">
                        <p id="concern-details-content" style="font-size: 0.9rem;"></p>
                    </div>

                    <!-- قسم الردود -->
                    <div class="concern-replies-section p-2">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0" style="font-size: 0.9rem;">
                                <i class="fas fa-comments me-1" style="font-size: 0.8rem;"></i> الردود
                                <span class="badge bg-primary rounded-pill ms-2" id="concern-details-replies-count" style="font-size: 0.7rem;">0</span>
                            </h6>
                            <button class="btn btn-sm btn-primary py-1 px-2" id="add-reply-btn" style="font-size: 0.8rem;">
                                <i class="fas fa-plus-circle me-1" style="font-size: 0.7rem;"></i> إضافة رد
                            </button>
                        </div>

                        <!-- نموذج إضافة رد (مخفي بشكل افتراضي) -->
                        <div id="reply-form-container" class="mb-2" style="display: none;">
                            <form id="replyForm" class="border rounded p-2 bg-light">
                                <div class="mb-2">
                                    <label for="replyContent" class="form-label" style="font-size: 0.85rem;">الرد</label>
                                    <textarea class="form-control" id="replyContent" rows="2" placeholder="اكتب ردك هنا..." required style="font-size: 0.85rem;"></textarea>
                                </div>
                                <div class="d-flex justify-content-end">
                                    <button type="button" class="btn btn-secondary btn-sm me-2 py-1 px-2" id="cancel-reply-btn" style="font-size: 0.75rem;">
                                        <i class="fas fa-times me-1" style="font-size: 0.7rem;"></i> إلغاء
                                    </button>
                                    <button type="button" class="btn btn-primary btn-sm py-1 px-2" id="submit-reply-btn" style="font-size: 0.75rem;">
                                        <i class="fas fa-paper-plane me-1" style="font-size: 0.7rem;"></i> إرسال
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- قائمة الردود -->
                        <div id="replies-list" class="replies-list" style="max-height: 300px;">
                            <!-- سيتم إضافة الردود هنا ديناميكيًا -->
                            <div class="text-center py-3 text-muted" id="no-replies-message">
                                <i class="far fa-comments mb-2" style="font-size: 1.5rem;"></i>
                                <p style="font-size: 0.85rem;">لا توجد ردود حتى الآن. كن أول من يضيف رد!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تعديل الرد -->
<div class="modal fade" id="editReplyModal" tabindex="-1" aria-labelledby="editReplyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title" id="editReplyModalLabel" style="font-size: 0.9rem;">
                    <i class="fas fa-edit me-1" style="font-size: 0.75rem;"></i> تعديل الرد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body py-2">
                <form id="editReplyForm">
                    <input type="hidden" id="editReplyId">
                    <div class="mb-2">
                        <label for="editReplyContent" class="form-label" style="font-size: 0.85rem;">الرد</label>
                        <textarea class="form-control" id="editReplyContent" rows="2" required style="font-size: 0.85rem;"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-secondary btn-sm py-1 px-2" data-bs-dismiss="modal" style="font-size: 0.75rem;">
                    <i class="fas fa-times me-1" style="font-size: 0.7rem;"></i> إلغاء
                </button>
                <button type="button" class="btn btn-primary btn-sm py-1 px-2" id="saveEditReply" style="font-size: 0.75rem;">
                    <i class="fas fa-save me-1" style="font-size: 0.7rem;"></i> حفظ
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تأكيد حذف الرد -->
<div class="modal fade" id="deleteReplyModal" tabindex="-1" aria-labelledby="deleteReplyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title" id="deleteReplyModalLabel" style="font-size: 0.9rem;">
                    <i class="fas fa-trash-alt me-1" style="font-size: 0.75rem;"></i> حذف الرد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body py-2">
                <input type="hidden" id="deleteReplyId">
                <p style="font-size: 0.85rem;">هل أنت متأكد من رغبتك في حذف هذا الرد؟</p>
                <p class="text-danger mb-0" style="font-size: 0.8rem;"><strong>تحذير:</strong> لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-secondary btn-sm py-1 px-2" data-bs-dismiss="modal" style="font-size: 0.75rem;">
                    <i class="fas fa-times me-1" style="font-size: 0.7rem;"></i> إلغاء
                </button>
                <button type="button" class="btn btn-danger btn-sm py-1 px-2" id="confirmDeleteReply" style="font-size: 0.75rem;">
                    <i class="fas fa-trash-alt me-1" style="font-size: 0.7rem;"></i> حذف
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نموذج رسالة النجاح -->
<div class="modal fade" id="successModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-1" style="font-size: 0.85rem;"></i> تم الإرسال بنجاح
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body text-center py-3">
                <div class="mb-2">
                    <i class="fas fa-paper-plane text-success mb-2" style="font-size: 2.5rem;"></i>
                    <h5 class="mb-2">تم إرسال انشغالك بنجاح!</h5>
                    <p class="mb-0 small">سيتم مراجعة الانشغال من قبل الإدارة وستظهر على الصفحة بعد الموافقة عليه.</p>
                    <p class="text-muted mt-1 small">شكراً لمساهمتك في تحسين العملية التعليمية.</p>
                </div>
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-success" data-bs-dismiss="modal">
                    <i class="fas fa-check me-1" style="font-size: 0.8rem;"></i> حسناً
                </button>
            </div>
        </div>
    </div>
</div>

<!-- سكريبت للتعامل مع إرسال النموذج والردود -->
<script>
    /**
     * هذا السكريبت يتعامل مع إرسال نموذج الانشغال والردود
     * تم تحديثه لإضافة رمز CSRF لحماية النموذج من هجمات التزوير عبر المواقع (CSRF)
     * يقوم بالتحقق من صحة البيانات قبل إرسالها إلى الخادم
     *
     * تحديثات:
     * 1. إضافة رمز CSRF إلى FormData للتوافق مع حماية CSRF في الخادم
     * 2. إضافة رأس X-CSRFToken للتوافق مع حماية CSRF في الخادم
     * 3. إضافة رأس X-Requested-With للإشارة إلى أن الطلب هو AJAX
     * 4. التحقق من نوع المحتوى في الاستجابة لمعالجة حالات الرد بـ HTML بدلاً من JSON
     * 5. معالجة حالة انتهاء الجلسة وإعادة تحميل الصفحة
     * 6. إضافة وظائف للتعامل مع الردود (إضافة/تعديل/حذف)
     * 7. إضافة دالة لتحويل الروابط في النص إلى روابط قابلة للنقر
     */

    // دالة لتحويل الروابط في النص إلى روابط قابلة للنقر
    function linkifyText(text) {
        if (!text) return '';

        // تعريف التعبيرات المنتظمة للروابط
        const urlRegex = {
            // للروابط التي تبدأ بـ http:// أو https://
            protocol: /(\b(https?):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gim,

            // للروابط التي تبدأ بـ www.
            www: /(^|[^\/])(www\.[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gim,

            // للروابط التي تبدأ بـ domain.tld
            domain: /(^|\s)([a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]\.[a-zA-Z]{2,}(\.[a-zA-Z]{2,})?(\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])?)/gim
        };

        // نسخة من النص الأصلي للعمل عليها
        let result = text;

        // استبدال الروابط التي تبدأ بـ http:// أو https://
        result = result.replace(urlRegex.protocol, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>');

        // استبدال الروابط التي تبدأ بـ www.
        result = result.replace(urlRegex.www, '$1<a href="http://$2" target="_blank" rel="noopener noreferrer">$2</a>');

        // استبدال الروابط التي تبدأ بـ domain.tld
        result = result.replace(urlRegex.domain, function(match, p1, p2) {
            // تجنب استبدال الروابط التي تم استبدالها بالفعل
            if (match.indexOf('<a href="') !== -1) {
                return match;
            }
            // تجنب استبدال الروابط التي تبدأ بـ www. (تم استبدالها في الخطوة السابقة)
            if (p2.startsWith('www.')) {
                return match;
            }
            return p1 + '<a href="http://' + p2 + '" target="_blank" rel="noopener noreferrer">' + p2 + '</a>';
        });

        return result;
    }

    // متغيرات عامة
    let currentConcernId = null;
    let concernDetailsModal, editReplyModal, deleteReplyModal;
    document.addEventListener('DOMContentLoaded', function() {
        const concernForm = document.getElementById('concernForm');
        const submitButton = document.getElementById('submitConcern');
        const addConcernModal = new bootstrap.Modal(document.getElementById('addConcernModal'));
        const successModal = new bootstrap.Modal(document.getElementById('successModal'));

        // تحويل الروابط في بطاقات الانشغالات إلى روابط قابلة للنقر
        document.querySelectorAll('.concern-content').forEach(contentElement => {
            // تجنب تحويل الروابط في بطاقات الانشغالات التي تحتوي على معالج النقر
            if (contentElement.closest('.concern-card[data-concern-id]')) {
                const originalContent = contentElement.textContent;
                contentElement.innerHTML = linkifyText(originalContent);

                // منع انتشار حدث النقر على الروابط إلى بطاقة الانشغال
                contentElement.querySelectorAll('a').forEach(link => {
                    link.addEventListener('click', function(event) {
                        event.stopPropagation();
                    });
                });
            }
        });

        // تهيئة النماذج
        concernDetailsModal = new bootstrap.Modal(document.getElementById('concernDetailsModal'));
        editReplyModal = new bootstrap.Modal(document.getElementById('editReplyModal'));
        deleteReplyModal = new bootstrap.Modal(document.getElementById('deleteReplyModal'));

        // تهيئة أزرار الردود
        const addReplyBtn = document.getElementById('add-reply-btn');
        const cancelReplyBtn = document.getElementById('cancel-reply-btn');
        const submitReplyBtn = document.getElementById('submit-reply-btn');
        const saveEditReplyBtn = document.getElementById('saveEditReply');
        const confirmDeleteReplyBtn = document.getElementById('confirmDeleteReply');

        // إضافة مستمعي الأحداث
        addReplyBtn.addEventListener('click', showReplyForm);
        cancelReplyBtn.addEventListener('click', hideReplyForm);
        submitReplyBtn.addEventListener('click', submitReply);
        saveEditReplyBtn.addEventListener('click', saveEditedReply);
        confirmDeleteReplyBtn.addEventListener('click', deleteReply);

        // تحميل عدد الردود لكل انشغال
        loadRepliesCounts();

        // طباعة معلومات تشخيصية
        console.log('تم تحميل صفحة الانشغالات');
        console.log('نموذج الانشغال:', concernForm);
        console.log('زر الإرسال:', submitButton);

        submitButton.addEventListener('click', function(event) {
            console.log('تم النقر على زر الإرسال');

            // التحقق من صحة النموذج
            if (!concernForm.checkValidity()) {
                console.log('النموذج غير صالح');
                concernForm.reportValidity();
                return;
            }

            // منع السلوك الافتراضي للنموذج
            event.preventDefault();

            // جمع بيانات النموذج
            const title = document.getElementById('title').value.trim();
            const content = document.getElementById('content').value.trim();

            console.log('بيانات الانشغال:', { title, content });

            // التحقق من البيانات مرة أخرى
            if (!title || !content) {
                console.error('البيانات غير مكتملة');
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            const formData = new FormData();
            formData.append('title', title);
            formData.append('content', content);

            // الحصول على رمز CSRF من وسم meta
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            if (!csrfToken) {
                console.error('لم يتم العثور على رمز CSRF');
                alert('حدث خطأ في إعداد الأمان. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
                return;
            }

            // إضافة رمز CSRF إلى النموذج
            // رغم أن المسار /add_concern معفى من حماية CSRF، إلا أننا نضيف الرمز للتوافق
            formData.append('csrf_token', csrfToken);

            // إرسال البيانات إلى الخادم
            console.log('جاري إرسال البيانات إلى الخادم...');

            fetch('/add_concern', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin',
                cache: 'no-cache',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest', // إضافة هذا الرأس لتحديد أن الطلب هو AJAX
                    'X-CSRFToken': csrfToken // إضافة رمز CSRF في الرؤوس
                }
            })
            .then(response => {
                console.log('تم استلام الرد:', response);

                // التحقق من نوع المحتوى
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('text/html')) {
                    // إذا كان الرد HTML، فهذا يعني أن الجلسة انتهت أو هناك مشكلة في المصادقة
                    console.error('تم استلام صفحة HTML بدلاً من JSON. قد تكون الجلسة منتهية.');

                    // إعادة تحميل الصفحة لتجديد الجلسة
                    alert('انتهت جلستك. سيتم إعادة تحميل الصفحة.');
                    window.location.reload();
                    throw new Error('انتهت الجلسة');
                }

                if (!response.ok) {
                    throw new Error('فشل الاتصال بالخادم: ' + response.status);
                }

                return response.json();
            })
            .then(data => {
                console.log('بيانات الرد:', data);
                if (data.status === 'success') {
                    console.log('تم إرسال الانشغال بنجاح');

                    // إغلاق نموذج الإضافة
                    addConcernModal.hide();

                    // إظهار رسالة النجاح
                    successModal.show();

                    // إعادة تعيين النموذج
                    concernForm.reset();
                } else {
                    console.error('خطأ في إرسال الانشغال:', data.message);
                    alert(data.message || 'حدث خطأ أثناء إرسال الانشغال');
                }
            })
            .catch(error => {
                console.error('خطأ في إرسال الانشغال:', error);
                // تجنب عرض رسالة الخطأ إذا كانت بسبب انتهاء الجلسة (تم التعامل معها بالفعل)
                if (error.message !== 'انتهت الجلسة') {
                    alert('حدث خطأ أثناء إرسال الانشغال: ' + error.message);
                }
            });
        });

        // دالة لتحميل عدد الردود لكل انشغال
        function loadRepliesCounts() {
            // الحصول على جميع بطاقات الانشغالات
            const concernCards = document.querySelectorAll('.concern-card');

            // لكل بطاقة، قم بتحميل عدد الردود
            concernCards.forEach(card => {
                const concernId = card.getAttribute('data-concern-id');
                const repliesCountElement = document.getElementById(`replies-count-${concernId}`);

                if (concernId && repliesCountElement) {
                    fetch(`/api/get_concern_replies/${concernId}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('فشل الاتصال بالخادم');
                            }
                            return response.json();
                        })
                        .then(data => {
                            if (data.status === 'success') {
                                // تحديث عدد الردود
                                repliesCountElement.textContent = data.replies.length;
                            }
                        })
                        .catch(error => {
                            console.error('خطأ في تحميل عدد الردود:', error);
                        });
                }
            });
        }
    });

    // دالة لفتح تفاصيل الانشغال
    function openConcernDetails(concernId) {
        currentConcernId = concernId;

        // الحصول على بيانات الانشغال
        const concernCard = document.querySelector(`.concern-card[data-concern-id="${concernId}"]`);
        const title = concernCard.querySelector('.concern-title').textContent;
        const content = concernCard.querySelector('.concern-content').textContent;
        const teacher = concernCard.querySelector('.concern-teacher span').textContent;
        const date = concernCard.querySelector('.concern-date span').textContent;
        const workplace = concernCard.querySelector('.concern-footer span').textContent;

        // تعبئة بيانات الانشغال في النموذج
        document.getElementById('concern-details-title').textContent = title;
        // تحويل الروابط في محتوى الانشغال إلى روابط قابلة للنقر
        document.getElementById('concern-details-content').innerHTML = linkifyText(content);
        document.getElementById('concern-details-teacher').textContent = teacher;
        document.getElementById('concern-details-date').textContent = date;
        document.getElementById('concern-details-workplace').textContent = workplace;

        // تحميل الردود
        loadReplies(concernId);

        // إظهار النموذج
        concernDetailsModal.show();
    }

    // دالة لتحميل الردود
    function loadReplies(concernId) {
        // إظهار رسالة التحميل
        document.getElementById('replies-list').innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل الردود...</p>
            </div>
        `;

        // جلب الردود من الخادم
        fetch(`/api/get_concern_replies/${concernId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('فشل الاتصال بالخادم');
                }
                return response.json();
            })
            .then(data => {
                if (data.status === 'success') {
                    // تحديث عدد الردود
                    document.getElementById('concern-details-replies-count').textContent = data.replies.length;

                    // إذا لم تكن هناك ردود، أظهر رسالة
                    if (data.replies.length === 0) {
                        document.getElementById('replies-list').innerHTML = `
                            <div class="text-center py-4 text-muted" id="no-replies-message">
                                <i class="far fa-comments mb-2" style="font-size: 2rem;"></i>
                                <p>لا توجد ردود حتى الآن. كن أول من يضيف رد!</p>
                            </div>
                        `;
                        return;
                    }

                    // إنشاء عناصر الردود
                    let repliesHTML = '';
                    data.replies.forEach(reply => {
                        repliesHTML += createReplyHTML(reply);
                    });

                    // تحديث قائمة الردود
                    document.getElementById('replies-list').innerHTML = repliesHTML;

                    // إضافة مستمعي الأحداث لأزرار التعديل والحذف
                    addReplyButtonsListeners();
                } else {
                    // إظهار رسالة الخطأ
                    document.getElementById('replies-list').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            ${data.message || 'حدث خطأ أثناء تحميل الردود'}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل الردود:', error);
                document.getElementById('replies-list').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        حدث خطأ أثناء تحميل الردود: ${error.message}
                    </div>
                `;
            });
    }



    // دالة لإنشاء HTML للرد
    function createReplyHTML(reply) {
        const isOwner = reply.is_owner;
        const isAdmin = reply.user.is_admin;
        const adminBadge = isAdmin ? `<span class="reply-admin-badge">مشرف</span>` : '';
        const actionsHTML = isOwner ? `
            <div class="reply-actions">
                <button class="reply-edit-btn" data-reply-id="${reply.id}">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="reply-delete-btn" data-reply-id="${reply.id}">
                    <i class="fas fa-trash-alt"></i> حذف
                </button>
            </div>
        ` : '';

        // تحويل الروابط في محتوى الرد إلى روابط قابلة للنقر
        const linkedContent = linkifyText(reply.content);

        return `
            <div class="reply-item" id="reply-${reply.id}">
                <div class="reply-header">
                    <div class="reply-user">
                        <i class="fas fa-user-circle" style="font-size: 1rem; color: var(--primary-color);"></i>
                        <span class="reply-user-name">${reply.user.name}</span>
                        ${adminBadge}
                    </div>
                    <div class="reply-date">
                        <i class="far fa-clock me-1" style="font-size: 0.65rem;"></i>
                        ${reply.updated_at ? `تم التعديل: ${reply.updated_at}` : reply.created_at}
                    </div>
                </div>
                <div class="reply-content">${linkedContent}</div>
                ${actionsHTML}
            </div>
        `;
    }

    // دالة لإضافة مستمعي الأحداث لأزرار التعديل والحذف
    function addReplyButtonsListeners() {
        // أزرار التعديل
        document.querySelectorAll('.reply-edit-btn').forEach(button => {
            button.addEventListener('click', function() {
                const replyId = this.getAttribute('data-reply-id');
                openEditReplyModal(replyId);
            });
        });

        // أزرار الحذف
        document.querySelectorAll('.reply-delete-btn').forEach(button => {
            button.addEventListener('click', function() {
                const replyId = this.getAttribute('data-reply-id');
                openDeleteReplyModal(replyId);
            });
        });
    }

    // دالة لإظهار نموذج إضافة رد
    function showReplyForm() {
        document.getElementById('reply-form-container').style.display = 'block';
        document.getElementById('replyContent').focus();
    }

    // دالة لإخفاء نموذج إضافة رد
    function hideReplyForm() {
        document.getElementById('reply-form-container').style.display = 'none';
        document.getElementById('replyContent').value = '';
    }

    // دالة لإرسال رد جديد
    function submitReply() {
        const content = document.getElementById('replyContent').value.trim();

        // التحقق من المحتوى
        if (!content) {
            alert('يرجى إدخال محتوى الرد');
            return;
        }

        // الحصول على رمز CSRF
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        if (!csrfToken) {
            console.error('لم يتم العثور على رمز CSRF');
            alert('حدث خطأ في إعداد الأمان. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
            return;
        }

        // إنشاء بيانات النموذج
        const formData = new FormData();
        formData.append('content', content);
        formData.append('csrf_token', csrfToken);

        // إرسال البيانات إلى الخادم
        fetch(`/api/add_concern_reply/${currentConcernId}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': csrfToken
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('فشل الاتصال بالخادم');
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success') {
                // إخفاء نموذج إضافة رد
                hideReplyForm();

                // إضافة الرد الجديد إلى القائمة
                const repliesList = document.getElementById('replies-list');
                const noRepliesMessage = document.getElementById('no-replies-message');

                // إزالة رسالة "لا توجد ردود" إذا كانت موجودة
                if (noRepliesMessage) {
                    noRepliesMessage.remove();
                }

                // إضافة الرد الجديد
                const replyHTML = createReplyHTML(data.reply);
                repliesList.insertAdjacentHTML('beforeend', replyHTML);

                // تحديث عدد الردود
                const repliesCount = document.getElementById('concern-details-replies-count');
                repliesCount.textContent = parseInt(repliesCount.textContent) + 1;

                // تحديث عدد الردود في بطاقة الانشغال
                const cardRepliesCount = document.getElementById(`replies-count-${currentConcernId}`);
                if (cardRepliesCount) {
                    cardRepliesCount.textContent = parseInt(cardRepliesCount.textContent) + 1;
                }

                // إضافة مستمعي الأحداث للأزرار الجديدة
                addReplyButtonsListeners();
            } else {
                alert(data.message || 'حدث خطأ أثناء إضافة الرد');
            }
        })
        .catch(error => {
            console.error('خطأ في إضافة الرد:', error);
            alert('حدث خطأ أثناء إضافة الرد: ' + error.message);
        });
    }

    // دالة لفتح نموذج تعديل الرد
    function openEditReplyModal(replyId) {
        // الحصول على محتوى الرد
        const replyContent = document.querySelector(`#reply-${replyId} .reply-content`).textContent;

        // تعبئة النموذج
        document.getElementById('editReplyId').value = replyId;
        document.getElementById('editReplyContent').value = replyContent;

        // إظهار النموذج
        editReplyModal.show();
    }

    // دالة لحفظ الرد المعدل
    function saveEditedReply() {
        const replyId = document.getElementById('editReplyId').value;
        const content = document.getElementById('editReplyContent').value.trim();

        // التحقق من المحتوى
        if (!content) {
            alert('يرجى إدخال محتوى الرد');
            return;
        }

        // الحصول على رمز CSRF
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        if (!csrfToken) {
            console.error('لم يتم العثور على رمز CSRF');
            alert('حدث خطأ في إعداد الأمان. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
            return;
        }

        // إنشاء بيانات النموذج
        const formData = new FormData();
        formData.append('content', content);
        formData.append('csrf_token', csrfToken);

        // إرسال البيانات إلى الخادم
        fetch(`/api/edit_concern_reply/${replyId}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': csrfToken
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('فشل الاتصال بالخادم');
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success') {
                // إغلاق النموذج
                editReplyModal.hide();

                // تحديث محتوى الرد مع تحويل الروابط إلى روابط قابلة للنقر
                const replyContentElement = document.querySelector(`#reply-${replyId} .reply-content`);
                replyContentElement.innerHTML = linkifyText(content);

                // تحديث تاريخ التعديل
                const replyDateElement = document.querySelector(`#reply-${replyId} .reply-date`);
                replyDateElement.innerHTML = `<i class="far fa-clock me-1"></i> تم التعديل: ${data.reply.updated_at}`;
            } else {
                alert(data.message || 'حدث خطأ أثناء تعديل الرد');
            }
        })
        .catch(error => {
            console.error('خطأ في تعديل الرد:', error);
            alert('حدث خطأ أثناء تعديل الرد: ' + error.message);
        });
    }

    // دالة لفتح نموذج حذف الرد
    function openDeleteReplyModal(replyId) {
        document.getElementById('deleteReplyId').value = replyId;
        deleteReplyModal.show();
    }

    // دالة لحذف الرد
    function deleteReply() {
        const replyId = document.getElementById('deleteReplyId').value;

        // الحصول على رمز CSRF
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        if (!csrfToken) {
            console.error('لم يتم العثور على رمز CSRF');
            alert('حدث خطأ في إعداد الأمان. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
            return;
        }

        // إنشاء بيانات النموذج
        const formData = new FormData();
        formData.append('csrf_token', csrfToken);

        // إرسال البيانات إلى الخادم
        fetch(`/api/delete_concern_reply/${replyId}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': csrfToken
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('فشل الاتصال بالخادم');
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success') {
                // إغلاق النموذج
                deleteReplyModal.hide();

                // إزالة الرد من القائمة
                const replyElement = document.getElementById(`reply-${replyId}`);
                replyElement.remove();

                // تحديث عدد الردود
                const repliesCount = document.getElementById('concern-details-replies-count');
                const newCount = parseInt(repliesCount.textContent) - 1;
                repliesCount.textContent = newCount;

                // تحديث عدد الردود في بطاقة الانشغال
                const cardRepliesCount = document.getElementById(`replies-count-${currentConcernId}`);
                if (cardRepliesCount) {
                    cardRepliesCount.textContent = newCount;
                }

                // إذا لم تعد هناك ردود، أظهر رسالة
                if (newCount === 0) {
                    document.getElementById('replies-list').innerHTML = `
                        <div class="text-center py-4 text-muted" id="no-replies-message">
                            <i class="far fa-comments mb-2" style="font-size: 2rem;"></i>
                            <p>لا توجد ردود حتى الآن. كن أول من يضيف رد!</p>
                        </div>
                    `;
                }
            } else {
                alert(data.message || 'حدث خطأ أثناء حذف الرد');
            }
        })
        .catch(error => {
            console.error('خطأ في حذف الرد:', error);
            alert('حدث خطأ أثناء حذف الرد: ' + error.message);
        });
    }
</script>
{% endblock %}
