/* Navigation Tabs Styling */
.nav-tabs {
    background-color: #007bff !important;
    padding: 10px 10px 0;
    border-radius: 5px 5px 0 0;
    display: flex;
    margin-bottom: 20px;
}

.nav-tabs .nav-item {
    margin-right: 5px;
}

.nav-tabs .nav-link {
    color: white !important;
    border: none !important;
    border-radius: 5px 5px 0 0;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

.nav-tabs .nav-link.active {
    background-color: white !important;
    color: #007bff !important;
    font-weight: bold;
}

/* إضافة تأثيرات إضافية */
.nav-tabs .nav-link:focus {
    outline: none;
    box-shadow: none;
}

/* تحسين المظهر على الشاشات الصغيرة */
@media (max-width: 768px) {
    .nav-tabs {
        flex-wrap: wrap;
    }
    
    .nav-tabs .nav-item {
        margin-bottom: 5px;
    }
} 