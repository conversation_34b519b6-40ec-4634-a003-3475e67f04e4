from app import app, db, User
from werkzeug.security import generate_password_hash
import traceback

# بيانات الأدمن
admin_email = '<EMAIL>'
admin_password = 'Sgorge**2008'

def reset_admin():
    try:
        with app.app_context():
            print("البحث عن حساب الأدمن...")

            # البحث عن المستخدم بالبريد الإلكتروني
            admin = User.query.filter_by(email=admin_email).first()

            if admin:
                print(f"تم العثور على المستخدم: {admin.email}")
                print(f"حالة الأدمن: is_admin={admin.is_admin}, admin_type={admin.admin_type}")

                # تحديث بيانات الأدمن
                admin.is_admin = True
                admin.admin_type = 'مدير'
                admin.teacher_name = "مدير النظام"
                admin.workplace = "إدارة النظام"

                # تحديث كلمة المرور مباشرة باستخدام generate_password_hash
                admin.password_hash = generate_password_hash(admin_password, method='sha256')

                db.session.commit()
                print("تم تحديث بيانات الأدمن بنجاح")
            else:
                print(f"لم يتم العثور على مستخدم بالبريد الإلكتروني: {admin_email}")
                print("إنشاء حساب أدمن جديد...")

                # إنشاء مستخدم أدمن جديد
                new_admin = User(
                    teacher_name="مدير النظام",
                    email=admin_email,
                    workplace="إدارة النظام",
                    phone_number="0000000000",
                    is_admin=True,
                    admin_type="مدير"
                )

                # تعيين كلمة المرور مباشرة
                new_admin.password_hash = generate_password_hash(admin_password, method='sha256')

                # إضافة المستخدم الجديد إلى قاعدة البيانات
                db.session.add(new_admin)
                db.session.commit()

                print("تم إنشاء حساب أدمن جديد بنجاح")

            # التحقق من وجود الأدمن بعد التحديث
            admin_check = User.query.filter_by(email=admin_email).first()
            if admin_check:
                print(f"التحقق: تم العثور على الأدمن بالبريد الإلكتروني: {admin_check.email}")
                print(f"حالة الأدمن: is_admin={admin_check.is_admin}, admin_type={admin_check.admin_type}")

                # التحقق من كلمة المرور
                from werkzeug.security import check_password_hash
                password_check = check_password_hash(admin_check.password_hash, admin_password)
                print(f"التحقق من كلمة المرور: {password_check}")
            else:
                print("خطأ: لم يتم العثور على الأدمن بعد التحديث!")

    except Exception as e:
        print(f"حدث خطأ: {str(e)}")
        print(traceback.format_exc())

if __name__ == "__main__":
    reset_admin()
