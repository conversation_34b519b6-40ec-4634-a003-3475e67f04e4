from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify, abort, send_file, make_response, after_this_request

from flask_sqlalchemy import SQLAlchemy

from flask_login import Login<PERSON>anager, UserMixin, login_user, login_required, logout_user, current_user, AnonymousUserMixin

from flask_wtf.csrf import CSRFProtect, generate_csrf

from werkzeug.security import generate_password_hash, check_password_hash

from werkzeug.utils import secure_filename

from werkzeug.datastructures import FileStorage

import os

from datetime import datetime, timedelta

import cloudinary

import cloudinary.uploader

from pywebpush import webpush, WebPushException

import json

import smtplib

from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from flask_mail import Mail, Message as FlaskMailMessage

import base64

import pickle

import requests

from io import BytesIO

import threading

import mimetypes

import tempfile

import urllib.parse

from functools import wraps

import secrets

import time

import re

import random

import string

import uuid

import traceback

from email.message import EmailMessage

import io

import PIL

from PIL import Image, ImageDraw, ImageFont, ImageColor

from sqlalchemy import inspect, text

import logging



# إنشاء تطبيق Flask

app = Flask(__name__)

# إعداد حماية CSRF
csrf = CSRFProtect(app)

# إضافة دالة لتوفير رمز CSRF في قوالب Jinja
@app.context_processor
def inject_csrf_token():
    # استخدام دالة generate_csrf من Flask-WTF
    return dict(csrf_token=generate_csrf)

# استثناء بعض المسارات من حماية CSRF
csrf.exempt('/add_concern')
csrf.exempt('/login')
csrf.exempt('/admin/review_concern/<int:concern_id>')
csrf.exempt('/admin/delete_concern/<int:concern_id>')
csrf.exempt('/api/add_concern_reply/<int:concern_id>')
csrf.exempt('/api/edit_concern_reply/<int:reply_id>')
csrf.exempt('/api/delete_concern_reply/<int:reply_id>')
# استثناء مسارات API الخاصة بشريط الترحيب
csrf.exempt('/api/get_greeting')
csrf.exempt('/api/update_greeting')
csrf.exempt('/api/update_greeting_visibility')



# تحميل الإعدادات من ملف config.py

app.config.from_pyfile('config.py')



# تكوين التطبيق

app.config['SECRET_KEY'] = app.config['SECRET_KEY']

app.config['SQLALCHEMY_DATABASE_URI'] = app.config['SQLALCHEMY_DATABASE_URI']

app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = app.config['SQLALCHEMY_TRACK_MODIFICATIONS']



# إعداد التسجيل (Logging)

logging.basicConfig(

    level=logging.DEBUG,

    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',

    handlers=[

        logging.StreamHandler()

    ]

)

logger = logging.getLogger(__name__)



# إعدادات الجلسة

app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=7)  # تعيين مدة الجلسة لأسبوع

app.config['SESSION_COOKIE_SECURE'] = False  # السماح بالكوكيز على HTTP

app.config['SESSION_COOKIE_HTTPONLY'] = True

app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'

# إعدادات إضافية للجلسة لحل مشكلة تسجيل الدخول
app.config['SESSION_TYPE'] = 'filesystem'
app.config['SESSION_USE_SIGNER'] = True
app.config['SESSION_PERMANENT'] = True

# تعيين اسم ملف تعريف الارتباط للجلسة
app.config['SESSION_COOKIE_NAME'] = 'fada_session'



# إعداد مجلد التحميل

UPLOAD_FOLDER = os.path.join(app.root_path, 'uploads')

if not os.path.exists(UPLOAD_FOLDER):

    os.makedirs(UPLOAD_FOLDER)

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER



# إعداد مجلد مؤقت للملفات التعليمية

TEMP_FOLDER = os.path.join(app.root_path, 'temp')

if not os.path.exists(TEMP_FOLDER):

    os.makedirs(TEMP_FOLDER)

app.config['TEMP_FOLDER'] = TEMP_FOLDER



app.config['MAX_CONTENT_LENGTH'] = 10 * 1024 * 1024  # 10MB max file size



# تحديد الامتدادات المسموح بها

MESSAGE_ALLOWED_EXTENSIONS = {
    # مستندات
    'pdf', 'doc', 'docx', 'xls', 'xlsx'
}

# الامتدادات المسموح بها للملفات التعليمية
ALLOWED_EXTENSIONS = {
    'pdf', 'doc', 'docx', 'xls', 'xlsx'
}

def allowed_file(filename, for_messages=False):
    """التحقق من أن الملف له امتداد مسموح به

    Args:
        filename (str): اسم الملف للتحقق منه
        for_messages (bool, optional): إذا كان True، يتحقق فقط من الامتدادات المسموح بها للرسائل

    Returns:
        bool: True إذا كان الملف مسموح به، False خلاف ذلك
    """
    if not filename or '.' not in filename:
        return False

    extension = filename.rsplit('.', 1)[1].lower()

    if for_messages:
        # للتحقق من امتدادات الملفات المسموح بها للرسائل فقط
        return extension in MESSAGE_ALLOWED_EXTENSIONS
    else:
        # للتحقق العام
        return extension in ALLOWED_EXTENSIONS

# تحديد الحد الأقصى لحجم الملف بالبايت (10 ميجابايت)
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

def check_file_size(file, max_size=MAX_FILE_SIZE):
    """التحقق من حجم الملف

    Args:
        file: ملف من request.files
        max_size: الحد الأقصى لحجم الملف بالبايت (افتراضيًا 10 ميجابايت)

    Returns:
        bool: True إذا كان حجم الملف أقل من الحد الأقصى، False خلاف ذلك
    """
    file.seek(0, os.SEEK_END)
    file_size = file.tell()
    file.seek(0)  # إعادة المؤشر إلى البداية
    return file_size <= max_size



# إعداد Cloudinary

cloudinary.config(

    cloud_name=app.config['CLOUDINARY_CLOUD_NAME'],

    api_key=app.config['CLOUDINARY_API_KEY'],

    api_secret=app.config['CLOUDINARY_API_SECRET']

)



# إعداد قاعدة البيانات

db = SQLAlchemy(app)



# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
login_manager.login_message_category = 'info'
login_manager.refresh_view = 'login'
login_manager.needs_refresh_message = 'يرجى إعادة تسجيل الدخول للمتابعة'
login_manager.needs_refresh_message_category = 'info'

# إعداد Flask-Mail
mail = Mail(app)
app.config['MAIL_SERVER'] = app.config.get('MAIL_SERVER', 'smtp.gmail.com')
app.config['MAIL_PORT'] = app.config.get('MAIL_PORT', 465)
app.config['MAIL_USE_TLS'] = app.config.get('MAIL_USE_TLS', False)
app.config['MAIL_USE_SSL'] = app.config.get('MAIL_USE_SSL', True)
app.config['MAIL_USERNAME'] = app.config.get('MAIL_USERNAME', '')
app.config['MAIL_PASSWORD'] = app.config.get('MAIL_PASSWORD', '')
app.config['MAIL_DEFAULT_SENDER'] = app.config.get('MAIL_DEFAULT_SENDER', app.config.get('MAIL_USERNAME', ''))



@login_manager.unauthorized_handler

def unauthorized():

    # التحقق من طلبات AJAX باستخدام X-Requested-With header

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':

        return jsonify({'status': 'error', 'message': 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'}), 401

    flash('يرجى تسجيل الدخول للوصول إلى هذه الصفحة', 'info')

    return redirect(url_for('login', next=request.url))



def is_pdf_file(filename):

    """التحقق مما إذا كان الملف بصيغة PDF"""

    if not filename or '.' not in filename:

        return False

    ext = filename.rsplit('.', 1)[1].lower()

    return ext == 'pdf'



class User(UserMixin, db.Model):

    id = db.Column(db.Integer, primary_key=True)

    teacher_name = db.Column(db.String(100), unique=True, nullable=False)

    email = db.Column(db.String(120), unique=True, nullable=False)

    workplace = db.Column(db.String(200), nullable=False)

    phone_number = db.Column(db.String(20), nullable=False)

    password_hash = db.Column(db.String(200), nullable=False)

    is_admin = db.Column(db.Boolean, default=False)

    admin_type = db.Column(db.String(20), default=None)  # نوع الإشراف: مدير، مشرف أول، مشرف ثاني

    bio = db.Column(db.Text)  # السيرة الذاتية

    profile_picture = db.Column(db.String(255))  # صورة الملف الشخصي

    specialization = db.Column(db.String(100))  # التخصص

    years_of_experience = db.Column(db.Integer)  # سنوات الخبرة

    birth_date = db.Column(db.Date, nullable=True)  # تاريخ الميلاد

    marital_status = db.Column(db.Boolean, default=None, nullable=True)  # الحالة العائلية (متزوج=True، غير متزوج=False، غير محدد=None)

    position = db.Column(db.String(100), nullable=True)  # المهنة/الرتبة

    category = db.Column(db.Integer, nullable=True)  # الصنف

    grade = db.Column(db.Integer, nullable=True)  # الدرجة

    work_start_date = db.Column(db.Date, nullable=True)  # تاريخ بداية العمل

    # أعمدة لتتبع حالة المستخدم
    is_logged_in = db.Column(db.Boolean, default=False)  # هل قام المستخدم بتسجيل الدخول
    is_online = db.Column(db.Boolean, default=False)  # هل المستخدم متصل حاليًا
    is_logged_out = db.Column(db.Boolean, default=True)  # هل قام المستخدم بتسجيل الخروج
    last_seen = db.Column(db.DateTime, default=None, nullable=True)  # آخر وقت كان فيه المستخدم نشطًا



    def set_password(self, password):

        self.password_hash = generate_password_hash(password, method='sha256')



    def check_password(self, password):

        return check_password_hash(self.password_hash, password)



    def get_unread_messages_count(self):

        return Message.query.filter_by(

            recipient_id=self.id,

            is_read=False,

            deleted_by_recipient=False  # لا نحسب الرسائل المحذوفة

        ).count()



class Message(db.Model):

    id = db.Column(db.Integer, primary_key=True)

    sender_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    recipient_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    content = db.Column(db.Text, nullable=False)

    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    is_read = db.Column(db.Boolean, default=False)

    is_deleted = db.Column(db.Boolean, default=False)

    attachment = db.Column(db.String(500))

    attachment_name = db.Column(db.String(255))  # إضافة عمود لاسم الملف

    deleted_by_sender = db.Column(db.Boolean, default=False)

    deleted_by_recipient = db.Column(db.Boolean, default=False)

    # اترك الأعمدة الجديدة معلقة مؤقتاً حتى يتم تنفيذ الهجرة

    cloudinary_public_id = db.Column(db.String(500))

    cloudinary_resource_type = db.Column(db.String(20))



    sender = db.relationship('User', foreign_keys=[sender_id], backref=db.backref('sent_messages', cascade='all, delete-orphan'))

    recipient = db.relationship('User', foreign_keys=[recipient_id], backref=db.backref('received_messages', cascade='all, delete-orphan'))



class PushSubscription(db.Model):

    id = db.Column(db.Integer, primary_key=True)

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    subscription_json = db.Column(db.Text, nullable=False)

    user = db.relationship('User', backref=db.backref('push_subscriptions', cascade='all, delete-orphan'))



class EducationalFile(db.Model):

    id = db.Column(db.Integer, primary_key=True)

    filename = db.Column(db.String(255), nullable=False)  # اسم الملف على Cloudinary

    original_filename = db.Column(db.String(255), nullable=True)  # اسم الملف الأصلي

    file_type = db.Column(db.String(50), nullable=False)

    semester = db.Column(db.Integer, nullable=False)

    subject = db.Column(db.String(100), nullable=False)

    level = db.Column(db.String(100), nullable=False)

    mimetype = db.Column(db.String(100), nullable=True)

    file_size = db.Column(db.Integer, nullable=True)

    upload_date = db.Column(db.DateTime, default=datetime.now)

    description = db.Column(db.Text, nullable=True)

    cloudinary_url = db.Column(db.String(500))  # رابط الملف على Cloudinary

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    download_count = db.Column(db.Integer, default=0)  # عدد مرات التحميل

    cloudinary_resource_type = db.Column(db.String(20))  # نوع المورد على Cloudinary



    # العلاقة مع المستخدم الذي قام برفع الملف

    uploader = db.relationship('User', backref=db.backref('uploaded_files', cascade='all, delete-orphan'))



class Schedule(db.Model):

    id = db.Column(db.Integer, primary_key=True)

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    day = db.Column(db.Integer, nullable=False)  # 0=الأحد, 1=الاثنين, ... 4=الخميس

    hour = db.Column(db.Integer, nullable=False)  # 8, 9, 10, 11, 13, 14, 15

    class_name = db.Column(db.String(20))  # اسم القسم مثل "1م4"



    # العلاقة مع المستخدم - إضافة cascade لحذف تلقائي

    teacher = db.relationship('User', backref=db.backref('schedule_items', cascade='all, delete-orphan'))



    # تأكيد عدم تكرار نفس الوقت للمستخدم

    __table_args__ = (db.UniqueConstraint('user_id', 'day', 'hour', name='unique_schedule_time'),)


# نموذج لتخزين إعدادات الموقع
class SiteSettings(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text, nullable=True)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    @classmethod
    def get_value(cls, key, default=None):
        """الحصول على قيمة إعداد معين"""
        setting = cls.query.filter_by(key=key).first()
        if setting:
            return setting.value
        return default

    @classmethod
    def set_value(cls, key, value):
        """تعيين قيمة إعداد معين"""
        setting = cls.query.filter_by(key=key).first()
        if setting:
            setting.value = value
        else:
            setting = cls(key=key, value=value)
            db.session.add(setting)
        db.session.commit()
        return setting



# إضافة عمود admin_type إذا لم يكن موجودًا

def add_admin_type_column():

    with app.app_context():

        inspector = inspect(db.engine)

        if 'user' in inspector.get_table_names():

            columns = [c['name'] for c in inspector.get_columns('user')]

            if 'admin_type' not in columns:

                print("إضافة عمود admin_type إلى جدول User...")

                db.engine.execute(text('ALTER TABLE user ADD COLUMN admin_type VARCHAR(20)'))

                # تعيين المدير الافتراضي

                admin = User.query.filter_by(email='<EMAIL>').first()

                if admin:

                    admin.admin_type = 'مدير'

                    db.session.commit()

                print("تم إضافة عمود admin_type بنجاح.")



@login_manager.user_loader

def load_user(user_id):

    return User.query.get(int(user_id))



# فئة للمستخدم المجهول

class AnonymousUser(AnonymousUserMixin):

    @property
    def id(self):
        return None

    @property
    def teacher_name(self):
        return None

    @property
    def profile_picture(self):
        return None

    @property
    def position(self):
        return None

    @property
    def is_admin(self):
        return False

    @property
    def admin_type(self):
        return None

    def get_unread_messages_count(self):
        return 0

    def __html__(self):
        return "AnonymousUser"



# تعيين فئة المستخدم المجهول

login_manager.anonymous_user = AnonymousUser



def generate_random_password(length=8):

    """توليد كلمة مرور عشوائية"""

    import random

    import string

    # توليد كلمة مرور تتكون من أحرف وأرقام

    characters = string.ascii_letters + string.digits

    return ''.join(random.choice(characters) for i in range(length))



@app.route('/reset_password', methods=['POST'])

def reset_password():

    """إعادة تعيين كلمة المرور وإرسالها بالبريد الإلكتروني"""

    print("\n========== بدء طلب استعادة كلمة المرور ==========")

    print(f"طريقة الطلب: {request.method}")

    print(f"البيانات المرسلة: {request.form}")



    email = request.form.get('email')

    print(f"البريد الإلكتروني المطلوب: {email}")



    if not email:

        print("خطأ: لم يتم توفير البريد الإلكتروني")

        return jsonify({'status': 'error', 'message': 'يرجى إدخال البريد الإلكتروني'}), 400



    user = User.query.filter_by(email=email).first()

    if not user:

        print(f"تنبيه: لم يتم العثور على مستخدم بالبريد الإلكتروني: {email}")

        # نعود بنجاح حتى لو لم يوجد المستخدم لأسباب أمنية

        return jsonify({'status': 'success', 'message': 'إذا كان البريد الإلكتروني مسجل لدينا، فستصلك رسالة إعادة تعيين كلمة المرور'})



    # توليد كلمة مرور جديدة

    new_password = generate_random_password()

    print(f"تم توليد كلمة مرور جديدة للمستخدم: {user.teacher_name}")



    # تحديث كلمة المرور للمستخدم

    user.set_password(new_password)

    db.session.commit()

    print("تم تحديث كلمة المرور في قاعدة البيانات")



    # إرسال كلمة المرور الجديدة بالبريد الإلكتروني

    subject = "إعادة تعيين كلمة المرور - فضاء أساتذة العلوم الفيزيائية"

    # إنشاء محتوى البريد الإلكتروني بتنسيق HTML محسن

    body = f"""

    <!DOCTYPE html>

    <html dir="rtl" lang="ar">

    <head>

        <meta charset="UTF-8">

        <meta name="viewport" content="width=device-width, initial-scale=1.0">

        <title>إعادة تعيين كلمة المرور</title>

    </head>

    <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Arial, sans-serif; background-color: #f5f5f5;">

        <div style="max-width: 600px; margin: 20px auto; background-color: #ffffff; border-radius: 15px; overflow: hidden; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);">

            <!-- رأس الرسالة -->

            <div style="background: linear-gradient(135deg, #1976d2, #0d47a1); padding: 25px; text-align: center;">

                <h1 style="color: white; margin: 0; font-size: 24px; font-weight: bold;">فضاء أساتذة العلوم الفيزيائية</h1>

                <p style="color: rgba(255, 255, 255, 0.9); margin: 10px 0 0 0; font-size: 18px;">إعادة تعيين كلمة المرور</p>

            </div>

            <!-- محتوى الرسالة -->

            <div style="padding: 30px; text-align: right; direction: rtl; color: #333; font-size: 16px; line-height: 1.6;">

                <p style="font-size: 18px; margin-bottom: 20px;">السلام عليكم <span style="color: #1976d2; font-weight: bold;">{user.teacher_name}</span>،</p>

                <p style="font-size: 17px; margin-bottom: 20px;">تم إعادة تعيين كلمة المرور الخاصة بحسابك في منصة فضاء أساتذة العلوم الفيزيائية.</p>

                <p style="font-size: 17px; margin-bottom: 10px; font-weight: bold; color: #333;">كلمة المرور الجديدة هي:</p>

                <!-- إطار مزدوج لكلمة المرور -->

                <div style="text-align: center; margin: 25px 0;">

                    <div style="border: 2px dashed #1976d2; padding: 5px; border-radius: 10px; display: inline-block;">

                        <div style="border: 2px solid #1976d2; background-color: #f0f7ff; padding: 15px 30px; border-radius: 6px;">

                            <span style="font-size: 24px; font-weight: bold; color: #0d47a1; letter-spacing: 1px;">{new_password}</span>

                        </div>

                    </div>

                </div>

                <p style="font-size: 17px; margin-top: 20px; color: #e53935; font-weight: bold;">يرجى تغيير كلمة المرور هذه فور تسجيل الدخول لضمان أمان حسابك.</p>

                <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; color: #666;">

                    <p style="margin: 0; font-size: 16px;">مع تحيات،</p>

                    <p style="margin: 5px 0 0 0; font-size: 16px; color: #1976d2; font-weight: bold;">فريق منصة فضاء أساتذة العلوم الفيزيائية</p>

                </div>

            </div>

            <!-- تذييل الرسالة -->

            <div style="background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 14px; color: #666;">

                <p style="margin: 0;">هذه رسالة آلية، يرجى عدم الرد عليها.</p>

                <p style="margin: 5px 0 0 0;">© 2023 فضاء أساتذة العلوم الفيزيائية. جميع الحقوق محفوظة.</p>

            </div>

        </div>

    </body>

    </html>

    """



    # إرسال البريد الإلكتروني

    print("جاري محاولة إرسال البريد الإلكتروني...")

    success = send_email_notification(user.email, subject, body)



    if success:

        print("تم إرسال كلمة المرور الجديدة بنجاح")

        print("========== انتهاء طلب استعادة كلمة المرور ==========\n")

        return jsonify({'status': 'success', 'message': 'تم إرسال كلمة المرور الجديدة إلى بريدك الإلكتروني'})

    else:

        # إذا فشل إرسال البريد، نعيد كلمة المرور القديمة

        print("فشل في إرسال البريد الإلكتروني!")

        print("========== انتهاء طلب استعادة كلمة المرور مع خطأ ==========\n")

        return jsonify({'status': 'error', 'message': 'حدث خطأ أثناء إرسال البريد الإلكتروني، يرجى المحاولة لاحقًا'}), 500



def send_email_notification(recipient_email, subject, body):
    """إرسال بريد إلكتروني باستخدام SMTP مباشرة أو Flask-Mail"""

    logger.info(f"\n----- بدء محاولة إرسال البريد الإلكتروني -----")
    logger.info(f"المستلم: {recipient_email}")
    logger.info(f"الموضوع: {subject}")
    logger.info(f"إعدادات البريد: SERVER={app.config['MAIL_SERVER']}, PORT={app.config['MAIL_PORT']}")
    logger.info(f"اسم المستخدم: {app.config['MAIL_USERNAME']}")
    logger.info(f"كلمة المرور متوفرة: {'نعم' if app.config['MAIL_PASSWORD'] else 'لا'}")

    # محاولة إرسال البريد الإلكتروني باستخدام Flask-Mail أولاً
    try:
        logger.info("محاولة إرسال البريد الإلكتروني باستخدام Flask-Mail...")

        # إنشاء رسالة Flask-Mail
        msg = FlaskMailMessage(
            subject=subject,
            recipients=[recipient_email],
            html=body,
            sender=app.config['MAIL_USERNAME']
        )

        # إرسال البريد
        mail.send(msg)

        logger.info("تم إرسال البريد بنجاح باستخدام Flask-Mail!")
        print(f"تم إرسال بريد إلكتروني بنجاح إلى: {recipient_email} (باستخدام Flask-Mail)")

        return True

    except Exception as flask_mail_error:
        logger.warning(f"فشل إرسال البريد باستخدام Flask-Mail: {str(flask_mail_error)}")
        logger.warning("محاولة استخدام SMTP مباشرة كخطة بديلة...")

        # محاولة إرسال البريد باستخدام SMTP مباشرة كخطة بديلة
        try:
            # إنشاء رسالة
            msg = MIMEMultipart()
            msg['From'] = app.config['MAIL_USERNAME']
            msg['To'] = recipient_email
            msg['Subject'] = subject

            # إرفاق المحتوى كـ HTML
            msg.attach(MIMEText(body, 'html', 'utf-8'))

            # إنشاء اتصال SMTP
            logger.info("محاولة الاتصال بخادم SMTP...")

            # استخدام SMTP_SSL للاتصال الآمن
            server = smtplib.SMTP_SSL('smtp.gmail.com', 465)

            # تفعيل وضع التصحيح للحصول على معلومات أكثر تفصيلاً
            server.set_debuglevel(1)

            logger.info("تم الاتصال بخادم SMTP بنجاح")

            # تسجيل الدخول
            logger.info("محاولة تسجيل الدخول لحساب البريد الإلكتروني...")
            server.login(app.config['MAIL_USERNAME'], app.config['MAIL_PASSWORD'])
            logger.info("تم تسجيل الدخول بنجاح")

            # إرسال البريد
            logger.info("جاري إرسال البريد الإلكتروني...")
            server.send_message(msg)
            logger.info("تم إرسال البريد بنجاح!")

            # إغلاق الاتصال
            server.quit()
            logger.info("----- انتهت محاولة إرسال البريد بنجاح -----\n")

            # طباعة رسالة نجاح في وحدة التحكم أيضًا
            print(f"تم إرسال بريد إلكتروني بنجاح إلى: {recipient_email} (باستخدام SMTP مباشرة)")

            return True

        except smtplib.SMTPAuthenticationError as e:
            logger.error(f"\n----- خطأ في المصادقة مع خادم SMTP -----")
            logger.error(f"نوع الخطأ: SMTPAuthenticationError")
            logger.error(f"تفاصيل الخطأ: {str(e)}")
            logger.error("هذا يشير إلى أن اسم المستخدم أو كلمة المرور غير صحيحة.")
            logger.error("يرجى التحقق من إعدادات البريد الإلكتروني في ملف config.py")
            logger.error("تأكد من استخدام كلمة مرور التطبيق وليس كلمة مرور حساب Google")
            logger.error(traceback.format_exc())

            # طباعة رسالة خطأ في وحدة التحكم أيضًا
            print(f"خطأ في المصادقة مع خادم SMTP: {str(e)}")
            print("يرجى التحقق من اسم المستخدم وكلمة المرور في ملف config.py")

            return False

        except smtplib.SMTPException as e:
            logger.error(f"\n----- خطأ في بروتوكول SMTP -----")
            logger.error(f"نوع الخطأ: {type(e).__name__}")
            logger.error(f"تفاصيل الخطأ: {str(e)}")
            logger.error(traceback.format_exc())

            # طباعة رسالة خطأ في وحدة التحكم أيضًا
            print(f"خطأ في بروتوكول SMTP: {str(e)}")

            return False

        except Exception as e:
            logger.error(f"\n----- خطأ في إرسال البريد الإلكتروني -----")
            logger.error(f"نوع الخطأ: {type(e).__name__}")
            logger.error(f"تفاصيل الخطأ: {str(e)}")
            logger.error(f"خطأ Flask-Mail الأصلي: {str(flask_mail_error)}")
            logger.error(traceback.format_exc())

            # طباعة رسالة خطأ في وحدة التحكم أيضًا
            print(f"خطأ في إرسال البريد الإلكتروني: {str(e)}")

            return False



@app.route('/')

def index():

    return redirect(url_for('login'))


@app.route('/test_maintenance')
def test_maintenance():
    """مسار اختباري لصفحة الصيانة"""
    return render_template('maintenance.html')



@app.route('/register', methods=['GET', 'POST'])

def register():

    if request.method == 'POST':

        try:

            # Get form data

            teacher_name = request.form.get('teacher_name', '').strip()

            email = request.form.get('email', '').strip().lower()

            workplace = request.form.get('workplace', '').strip()

            phone_prefix = request.form.get('phone_prefix', '')

            phone_number = request.form.get('phone_number', '').strip()

            password = request.form.get('password', '')



            errors = {}

            # Validate data

            if not all([teacher_name, email, workplace, phone_prefix, phone_number, password]):

                flash('جميع الحقول مطلوبة', 'error')

                return render_template('register.html',

                    teacher_name=teacher_name,

                    email=email,

                    workplace=workplace,

                    phone_number=phone_number,

                    phone_prefix=phone_prefix

                )



            if not email.endswith('@gmail.com'):

                flash('يجب أن يكون البريد الإلكتروني من نوع Gmail', 'error')

                return render_template('register.html',

                    teacher_name=teacher_name,

                    email=email,

                    workplace=workplace,

                    phone_number=phone_number,

                    phone_prefix=phone_prefix

                )



            if len(phone_number) != 8:

                flash('يجب أن يتكون رقم الهاتف من 8 أرقام', 'error')

                return render_template('register.html',

                    teacher_name=teacher_name,

                    email=email,

                    workplace=workplace,

                    phone_number=phone_number,

                    phone_prefix=phone_prefix

                )



            # Check if user exists

            if User.query.filter_by(email=email).first():

                errors['email'] = True

                flash('البريد الإلكتروني مستخدم بالفعل', 'error')



            if User.query.filter_by(teacher_name=teacher_name).first():

                errors['teacher_name'] = True

                flash('اسم الأستاذ(ة) مستخدم بالفعل', 'error')



            if errors:

                return render_template('register.html',

                    teacher_name=teacher_name,

                    email=email,

                    workplace=workplace,

                    phone_number=phone_number,

                    phone_prefix=phone_prefix,

                    errors=errors

                )



            # Create new user

            new_user = User(

                teacher_name=teacher_name,

                email=email,

                workplace=workplace,

                phone_number=phone_prefix + phone_number,

                birth_date=None,

                marital_status=None,

                position=None,

                category=None,

                grade=None,

                years_of_experience=None,

                work_start_date=None

            )

            new_user.set_password(password)



            # Save to database

            db.session.add(new_user)

            db.session.commit()



            return redirect(url_for('login'))



        except Exception as e:

            db.session.rollback()

            print(f"Registration error: {str(e)}")

            flash('حدث خطأ أثناء التسجيل. الرجاء المحاولة مرة أخرى', 'error')

            return render_template('register.html',

                teacher_name=teacher_name,

                email=email,

                workplace=workplace,

                phone_number=phone_number,

                phone_prefix=phone_prefix

            )



    return render_template('register.html')



@app.route('/login', methods=['GET', 'POST'])
def login():
    """
    صفحة تسجيل الدخول
    """
    # طباعة معلومات الجلسة الحالية
    print("\n===== معلومات الجلسة قبل تسجيل الدخول =====")
    print(f"معرف الجلسة: {session.sid if hasattr(session, 'sid') else 'غير متوفر'}")
    print(f"محتويات الجلسة: {dict(session)}")

    # إضافة رقم عشوائي لمنع التخزين المؤقت
    random_param = str(uuid.uuid4())

    if request.method == 'POST':
        email = request.form.get('email', '').strip().lower()
        password = request.form.get('password', '').strip()
        captcha = request.form.get('captcha', '')
        session_captcha = session.get('captcha', '')

        # طباعة معلومات التشخيص
        print(f"\n===== محاولة تسجيل دخول =====")
        print(f"البريد الإلكتروني: {email}")
        print(f"رمز التحقق المدخل: {captcha}")
        print(f"رمز التحقق في الجلسة: {session_captcha}")

        # التحقق من رمز الكابتشا
        if not captcha or captcha != session_captcha:
            flash('رمز التحقق غير صحيح', 'error')
            return redirect(url_for('login', nocache=random_param))

        # البحث عن المستخدم
        user = User.query.filter_by(email=email).first()

        # طباعة معلومات التشخيص للمستخدم
        if user:
            print(f"\n===== معلومات المستخدم =====")
            print(f"معرف المستخدم: {user.id}")
            print(f"البريد الإلكتروني: {user.email}")
            print(f"هل هو أدمن: {user.is_admin}")
            print(f"نوع الأدمن: {user.admin_type}")
            print(f"اسم المستخدم: {user.teacher_name}")

            # التحقق من كلمة المرور
            is_valid = user.check_password(password)
            print(f"كلمة المرور صحيحة: {is_valid}")

            # طباعة هاش كلمة المرور للتشخيص
            print(f"هاش كلمة المرور: {user.password_hash}")
        else:
            print("\n===== لم يتم العثور على المستخدم =====")

        # التحقق من صحة بيانات المستخدم
        if user and user.check_password(password):
            # تنظيف الجلسة القديمة
            session.clear()

            # تسجيل الدخول
            login_user(user, remember=True)

            # تحديث حالة المستخدم إلى متصل
            user.is_logged_in = True
            user.is_online = True
            user.is_logged_out = False
            user.last_seen = datetime.now()
            db.session.commit()

            # إضافة معرف المستخدم للجلسة
            session['user_id'] = user.id
            session['_fresh'] = True
            session['login_time'] = str(datetime.now())  # إضافة وقت تسجيل الدخول

            # طباعة معلومات الجلسة بعد تسجيل الدخول
            print("\n===== معلومات الجلسة بعد تسجيل الدخول =====")
            print(f"معرف المستخدم في الجلسة: {session.get('user_id')}")
            print(f"محتويات الجلسة: {dict(session)}")

            # إنشاء استجابة مع معلمات لمنع التخزين المؤقت
            if user.is_admin:
                print(f"\n===== توجيه المستخدم إلى لوحة الإدارة =====")
                response = redirect(url_for('admin_panel', nocache=random_param))
            else:
                print(f"\n===== توجيه المستخدم إلى صفحة الترحيب =====")
                response = redirect(url_for('welcome', nocache=random_param))

            # إضافة ترويسات لمنع التخزين المؤقت
            response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'

            return response
        else:
            flash('البريد الإلكتروني أو كلمة المرور غير صحيحة', 'error')
            return redirect(url_for('login', nocache=random_param))

    # توليد رمز تحقق جديد (4 أرقام)
    captcha_code = str(random.randint(1000, 9999))
    session['captcha'] = captcha_code

    # طباعة معلومات الجلسة بعد توليد رمز التحقق
    print("\n===== معلومات الجلسة بعد توليد رمز التحقق =====")
    print(f"رمز التحقق: {captcha_code}")
    print(f"محتويات الجلسة: {dict(session)}")

    # إنشاء استجابة مع معلمات لمنع التخزين المؤقت
    response = make_response(render_template('login.html', captcha=captcha_code, nocache=random_param))

    # إضافة ترويسات لمنع التخزين المؤقت
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'

    return response



def formatDateForDisplay(date_str):

    if not date_str:

        return 'غير محدد'



    try:

        # تحويل سلسلة لتاريخ إلى كائن datetime

        date = datetime.strptime(date_str, '%Y-%m-%d')



        # قائمة بأسماء الأشهر العربية (ميلادي)

        arabic_months = ['جانفي', 'فيفري', 'مارس', 'أفريل', 'ماي', 'جوان', 'جويلية', 'أوت', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']



        # تنسيق التاريخ باستخدام أسماء الأشهر العربية

        day = date.day

        month = arabic_months[date.month - 1]  # -1 لأن الأشهر تبدأ من 0 في Python

        year = date.year



        return f"{day} {month} {year}"

    except Exception as e:

        print(f"خطأ في تنسيق التاريخ: {e}")

        return date_str



@app.route('/welcome')
@login_required
def welcome():
    if not current_user.is_authenticated:
        return redirect(url_for('login'))

    # إضافة رقم عشوائي لمنع التخزين المؤقت
    random_param = str(uuid.uuid4())

    # الحصول على حالة ظهور شريط الترحيب
    greeting_visible = SiteSettings.get_value('greeting_visible', 'true')
    greeting_text = SiteSettings.get_value('greeting_text', 'السلام عليكم و رحمة الله و بركاته')

    # تحويل القيمة النصية إلى قيمة منطقية
    is_greeting_visible = greeting_visible.lower() == 'true'

    app.logger.info(f"صفحة الترحيب: حالة ظهور الشريط = {greeting_visible}, هل الشريط ظاهر؟ {is_greeting_visible}")

    # إنشاء استجابة مع معلمات لمنع التخزين المؤقت
    response = make_response(render_template(
        'new_welcome.html',
        nocache=random_param,
        greeting_visible=is_greeting_visible,
        greeting_text=greeting_text
    ))

    # إضافة ترويسات لمنع التخزين المؤقت
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'

    return response

@app.route('/dashboard')
@login_required
def dashboard():
    if not current_user.is_authenticated:
        return redirect(url_for('login'))

    # إضافة دالة تنسيق التاريخ إلى سياق القالب
    return render_template('welcome.html', formatDateForDisplay=formatDateForDisplay)

@app.route('/admin')
@login_required
def admin_panel():
    # إضافة رقم عشوائي لمنع التخزين المؤقت
    random_param = str(uuid.uuid4())

    # التحقق من الصلاحيات: يجب أن يكون المستخدم مديرًا أو مشرفًا
    if not current_user.is_admin:
        flash('غير مصرح لك بالوصول إلى لوحة الإدارة', 'danger')
        return redirect(url_for('index'))

    # الحصول على جميع المستخدمين
    all_users = User.query.all()

    # فصل الأساتذة والمشرفين
    teachers = [user for user in all_users if not user.is_admin]
    supervisors = [user for user in all_users if user.is_admin and user.admin_type != 'مدير']

    # التحقق من أن المستخدم الحالي هو المدير الرئيسي
    # استخدم is_main_admin لضبط عرض أزرار إضافة/تعديل/حذف المشرفين
    is_main_admin = current_user.is_admin and current_user.admin_type == 'مدير'

    # الحصول على معلومات المدير
    admin_info = {}
    admin_user = User.query.filter_by(is_admin=True, admin_type='مدير').first()
    if admin_user:
        admin_info = {
            'id': admin_user.id,
            'name': admin_user.teacher_name,
            'email': admin_user.email,
            'phone': admin_user.phone_number,
            'workplace': admin_user.workplace
        }

    # الحصول على بيانات التدرج السنوي للسنة الأولى متوسط
    yearly_progress = {}
    for year in range(1, 5):  # للسنوات 1-4
        progress_items = YearlyProgress.query.filter_by(year=year).order_by(YearlyProgress.week).all()
        yearly_progress[year] = [
            {
                "week": item.week,
                "month": item.month,
                "firstLesson": item.first_lesson or "",
                "secondLesson": item.second_lesson or "",
                "isMerged": item.is_merged
            } for item in progress_items
        ]

    # تقديم قالب صفحة الإدارة مع البيانات المطلوبة
    response = make_response(render_template(
        'admin.html',
        teachers=teachers,
        supervisors=supervisors,
        admin_info=admin_info,
        yearly_progress=yearly_progress,
        users=all_users,  # إضافة متغير users الذي يحتوي على جميع المستخدمين
        is_main_admin=is_main_admin,  # إضافة متغير للتحقق من أن المستخدم هو المدير الرئيسي
        nocache=random_param  # إضافة معلمة عشوائية لمنع التخزين المؤقت
    ))

    # إضافة ترويسات لمنع التخزين المؤقت
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'

    return response



# تم تعطيل دالة csrf_protect المخصصة واستخدام آلية حماية CSRF المدمجة في Flask-WTF بدلاً من ذلك
# @app.before_request
# def csrf_protect():
#     """
#     التحقق من رمز CSRF في الطلبات
#     يتم استدعاء هذه الدالة قبل كل طلب
#     """
#     # تم تعطيل هذه الدالة واستخدام آلية حماية CSRF المدمجة في Flask-WTF بدلاً من ذلك
#     pass



# تم تعطيل دالة create_csrf_token المخصصة واستخدام آلية حماية CSRF المدمجة في Flask-WTF بدلاً من ذلك
# @app.before_request
# def create_csrf_token():
#     """
#     إنشاء رمز CSRF إذا لم يكن موجودًا بالفعل في الجلسة
#     يتم استدعاء هذه الدالة قبل كل طلب
#     """
#     # تم تعطيل هذه الدالة واستخدام آلية حماية CSRF المدمجة في Flask-WTF بدلاً من ذلك
#     pass



@app.before_request
def make_session_permanent():
    session.permanent = True
    app.permanent_session_lifetime = timedelta(days=7)  # تعيين مدة الجلسة لأسبوع

@app.after_request
def add_no_cache_headers(response):
    """إضافة ترويسات لمنع التخزين المؤقت في المتصفح"""
    # إضافة ترويسات لمنع التخزين المؤقت
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'

    # إضافة ترويسات أمان إضافية
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'SAMEORIGIN'
    response.headers['X-XSS-Protection'] = '1; mode=block'

    return response



@app.route('/edit_user/<int:user_id>', methods=['POST'])

@login_required

def edit_user(user_id):

    try:

        if not current_user.is_admin and current_user.id != user_id:

            return jsonify({'status': 'error', 'message': 'غير مصرح لك بتعديل هذا المستخدم'}), 403



        user = User.query.get_or_404(user_id)



        # التحقق من البيانات المرسلة

        teacher_name = request.form.get('teacher_name', '').strip()

        email = request.form.get('email', '').strip()

        workplace = request.form.get('workplace', '').strip()

        phone_number = request.form.get('phone_number', '').strip()

        specialization = request.form.get('specialization', '').strip()

        years_of_experience = request.form.get('years_of_experience', '')



        # الحقول الجديدة

        birth_date = request.form.get('birth_date', '')

        marital_status = request.form.get('marital_status', '0')

        position = request.form.get('position', '').strip()

        category = request.form.get('category', '')

        grade = request.form.get('grade', '')

        work_start_date = request.form.get('work_start_date', '')



        if not all([teacher_name, email, workplace, phone_number]):

            return jsonify({'status': 'error', 'message': 'الحقول الأساسية مطلوبة'}), 400



        # التحقق من تنسيق رقم الهاتف

        if not phone_number.isdigit() or len(phone_number) != 10:

            return jsonify({'status': 'error', 'message': 'رقم الهاتف يجب أن يتكون من 10 أرقام'}), 400



        # التحقق من تنسيق البريد الإلكتروني

        if not email.endswith('@gmail.com'):

            return jsonify({'status': 'error', 'message': 'يجب أن يكون البريد الإلكتروني من نوع Gmail'}), 400



        # التحقق من تكرار البريد الإلكتروني

        existing_email = User.query.filter(User.email == email, User.id != user_id).first()

        if existing_email:

            return jsonify({'status': 'error', 'message': 'البريد الإلكتروني مستخدم بالفعل'}), 400



        # التحقق من تكرار اسم المستخدم

        existing_name = User.query.filter(User.teacher_name == teacher_name, User.id != user_id).first()

        if existing_name:

            return jsonify({'status': 'error', 'message': 'اسم الأستاذ(ة) مستخدم بالفعل'}), 400



        # تحديث البيانات الأساسية

        user.teacher_name = teacher_name

        user.email = email

        user.workplace = workplace

        user.phone_number = phone_number



        # تحديث البيانات الإضافية

        if specialization:

            user.specialization = specialization



        if years_of_experience:

            try:

                user.years_of_experience = int(years_of_experience)

            except ValueError:

                pass



        # تحديث الحقول الجديدة

        if birth_date:

            try:

                user.birth_date = datetime.strptime(birth_date, '%Y-%m-%d').date()

            except ValueError:

                pass



        # طباعة قيمة marital_status الواردة للتأكد من صحتها

        print(f"قيمة الحالة العائلية الواردة: {marital_status}, النوع: {type(marital_status).__name__}")



        # تحديث الحالة العائلية

        if marital_status == 'none':

            user.marital_status = None

        else:

            user.marital_status = marital_status == '1'



        # طباعة للتأكد من تحديث القيمة

        print(f"قيمة الحالة العائلية بعد التحديث: {user.marital_status}, النوع: {type(user.marital_status).__name__}")



        if position:

            user.position = position



        if category:

            try:

                user.category = int(category)

            except ValueError:

                pass



        if grade:

            try:

                user.grade = int(grade)

            except ValueError:

                pass



        if work_start_date:

            try:

                user.work_start_date = datetime.strptime(work_start_date, '%Y-%m-%d').date()

            except ValueError:

                pass



        # حفظ التغييرات في قاعدة البيانات

        db.session.commit()



        # إعادة قراءة البيانات من قاعدة البيانات للتأكد من التحديث

        db.session.refresh(user)



        # تحضير البيانات المحدثة للإرجاع

        marital_status_text = 'غير محدد' if user.marital_status is None else ('متزوج(ة)' if user.marital_status else 'غير متزوج(ة)')

        birth_date_formatted = user.birth_date.strftime('%Y-%m-%d') if user.birth_date else 'غير محدد'

        work_start_date_formatted = user.work_start_date.strftime('%Y-%m-%d') if user.work_start_date else 'غير محدد'

        category_grade = f"{user.category or '-'} / {user.grade or '-'}"



        return jsonify({

            'status': 'success',

            'message': 'تم تحديث بيانات المستخدم بنجاح',

            'data': {

                'teacher_name': user.teacher_name,

                'email': user.email,

                'workplace': user.workplace,

                'phone_number': user.phone_number,

                'specialization': user.specialization or '',

                'years_of_experience': user.years_of_experience or 0,

                'birth_date': birth_date_formatted if birth_date_formatted != 'غير محدد' else '',

                'marital_status': user.marital_status,  # القيمة البوليانية

                'marital_status_text': marital_status_text,  # النص المعروض للمستخدم

                'position': user.position or '',

                'category': user.category or '',

                'grade': user.grade or '',

                'work_start_date': work_start_date_formatted if work_start_date_formatted != 'غير محدد' else '',

                'category_grade': category_grade

            }

        })

    except Exception as e:

        db.session.rollback()

        print(f"خطأ في تحديث بيانات المستخدم: {str(e)}")

        return jsonify({'status': 'error', 'message': str(e)}), 500



@app.route('/delete_user/<int:user_id>', methods=['GET', 'POST'])

@login_required

def delete_user(user_id):

    if not current_user.is_admin:

        return redirect(url_for('welcome'))



    try:

        user = User.query.get_or_404(user_id)



        # El cascade debería manejar automáticamente la eliminación de registros relacionados

        # Pero podemos explícitamente limpiar estos registros para mayor seguridad

        # Primero eliminar las referencias en Message donde no funciona el cascade por tener referencias múltiples

        Message.query.filter((Message.sender_id == user_id) | (Message.recipient_id == user_id)).delete()



        # Ahora eliminar el usuario, lo que activará el cascade en otras tablas

        db.session.delete(user)

        db.session.commit()



        # التحقق مما إذا كان الطلب من خلال AJAX

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':

            return jsonify({

                'status': 'success',

                'message': 'تم حذف المستخدم بنجاح'

            })

        else:

            flash('تم حذف المستخدم بنجاح', 'success')

    except Exception as e:

        db.session.rollback()

        error_details = str(e)

        app.logger.error(f"Error deleting user: {error_details}")



        # التحقق مما إذا كان الطلب من خلال AJAX

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':

            return jsonify({

                'status': 'error',

                'message': f'حدث خطأ أثناء حذف المستخدم: {error_details}'

            }), 500

        else:

            flash(f'حدث خطأ أثناء حذف المستخدم: {error_details}', 'error')



    # إذا لم يكن الطلب من خلال AJAX، قم بإعادة التوجيه

    return redirect(url_for('admin_panel'))



@app.route('/logout')

@login_required

def logout():
    # تحديث حالة المستخدم قبل تسجيل الخروج
    current_user.is_logged_in = False
    current_user.is_online = False
    current_user.is_logged_out = True
    current_user.last_seen = datetime.now()
    db.session.commit()

    # تسجيل الخروج
    logout_user()

    return redirect(url_for('login'))



@app.route('/self_edit', methods=['GET', 'POST'])

@login_required

def self_edit():

    user = User.query.get_or_404(current_user.id) # Get user object early



    if request.method == 'POST':

        # الحصول على البيانات من النموذج

        teacher_name = request.form.get('teacher_name', '').strip()

        email = request.form.get('email', '').strip().lower()

        workplace = request.form.get('workplace', '').strip()

        phone_number = request.form.get('phone_number', '').strip()

        current_password = request.form.get('current_password', '')

        new_password = request.form.get('new_password', '')

        confirm_new_password = request.form.get('confirm_new_password', '')



        # التحقق من صحة البيانات الأساسية

        if not all([teacher_name, email, workplace, phone_number]):

            flash('جميع الحقول الأساسية مطلوبة (الاسم، البريد، مكان العمل، الهاتف)', 'error')

            return redirect(url_for('self_edit'))



        # التحقق من صحة رقم الهاتف (مثال: 10 أرقام)

        if len(phone_number) != 10 or not phone_number.isdigit():

             flash('رقم الهاتف يجب أن يتكون من 10 أرقام', 'error')

             return redirect(url_for('self_edit'))



        # التحقق من البريد الإلكتروني

        if not email.endswith('@gmail.com'):

            flash('يجب أن يكون البريد الإلكتروني من نوع Gmail', 'error')

            return redirect(url_for('self_edit'))



        # التحقق من تكرار البريد الإلكتروني (إذا تغير)

        if email != user.email and User.query.filter(User.email == email, User.id != user.id).first():

            flash('البريد الإلكتروني مستخدم بالفعل', 'error')

            return redirect(url_for('self_edit'))



        # التحقق من تكرار اسم الأستاذ (إذا تغير)

        if teacher_name != user.teacher_name and User.query.filter(User.teacher_name == teacher_name, User.id != user.id).first():

            flash('اسم الأستاذ(ة) مستخدم بالفعل', 'error')

            return redirect(url_for('self_edit'))



        password_changed = False

        # التحقق من تغيير كلمة المرور

        if new_password:

            # التحقق من كلمة المرور الحالية

            if not current_password:

                flash('يجب إدخال كلمة المرور الحالية لتغييرها', 'error')

                return redirect(url_for('self_edit'))

            if not user.check_password(current_password):

                flash('كلمة المرور الحالية غير صحيحة', 'error')

                return redirect(url_for('self_edit'))



            # التحقق من تطابق كلمتي المرور الجديدتين

            if new_password != confirm_new_password:

                flash('كلمتا المرور الجديدتان غير متطابقتين', 'error')

                return redirect(url_for('self_edit'))



            # تحديث كلمة المرور

            user.set_password(new_password)

            password_changed = True



        # تحديث بيانات المستخدم الأخرى

        user.teacher_name = teacher_name

        user.email = email

        user.workplace = workplace

        user.phone_number = phone_number



        try:

            db.session.commit()

            if password_changed:

                flash('تم تحديث معلوماتك وكلمة المرور بنجاح!', 'success')

            else:

                flash('تم تحديث معلوماتك بنجاح!', 'success')

            return redirect(url_for('dashboard')) # Redirect to welcome on success

        except Exception as e:

            db.session.rollback()

            flash(f'حدث خطأ أثناء تحديث المعلومات: {str(e)}', 'error')

            return redirect(url_for('self_edit')) # Redirect back to edit on error



    # GET request or initial load

    # إنشاء CSRF token إذا لم يكن موجودًا (نقلته هنا ليكون متاحًا دائمًا للـ GET)

    if 'csrf_token' not in session:

        session['csrf_token'] = generate_csrf_token()



    return render_template('self_edit.html', user=user) # Pass user object to template for GET



@app.route('/messages')

@login_required

def messages():

    # الرسائل المستلمة (غير المحذوفة من قبل المستلم)

    received_messages = Message.query.filter(

        Message.recipient_id == current_user.id,

        Message.deleted_by_recipient == False

    ).order_by(Message.timestamp.desc()).all()



    # الرسائل المرسلة (غير المحذوفة من قبل المرسل)

    sent_messages = Message.query.filter(

        Message.sender_id == current_user.id,

        Message.deleted_by_sender == False

    ).order_by(Message.timestamp.desc()).all()



    # تحديد قائمة المستخدمين بناءً على نوع المستخدم الحالي
    users = []

    if current_user.is_admin:
        # إذا كان المستخدم مديرًا أو مشرفًا، يمكنه مراسلة جميع المستخدمين
        users = User.query.filter(User.id != current_user.id).all()
    else:
        # إذا كان المستخدم عاديًا، يمكنه فقط مراسلة المشرف الأول
        first_supervisor = User.query.filter_by(is_admin=True, admin_type='مشرف أول').first()
        if first_supervisor:
            users = [first_supervisor]



    return render_template('messages.html',

                         received_messages=received_messages,

                         sent_messages=sent_messages,

                         users=users)



def send_web_push(user_id, title, body):

    subscriptions = PushSubscription.query.filter_by(user_id=user_id).all()

    if not subscriptions:

        return



    for sub in subscriptions:

        try:

            subscription_info = json.loads(sub.subscription_json)

            webpush(

                subscription_info=subscription_info,

                data=json.dumps({

                    'title': title,

                    'body': body

                }),

                vapid_private_key=app.config['VAPID_PRIVATE_KEY'],

                vapid_claims={

                    'sub': f"mailto:{app.config['VAPID_CLAIM_EMAIL']}"

                }

            )

        except WebPushException as e:

            print(f"Web Push Error: {e}")

            if "410" in str(e):  # Subscription expired

                db.session.delete(sub)

                db.session.commit()



def send_notifications_async(recipient_id, recipient_email, sender_name, host_url, message_subject=None, message_content=None):
    """إرسال الإشعارات بشكل متوازٍ"""

    # الحصول على التاريخ والوقت الحالي بالتنسيق العربي
    now = datetime.now()
    current_date = now.strftime("%Y-%m-%d")
    formatted_date = formatDateForDisplay(current_date)
    current_time = now.strftime("%H:%M")

    def send_push():
        try:
            send_web_push(
                recipient_id,
                "رسالة جديدة",
                f"لديك رسالة جديدة من {sender_name}"
            )
        except Exception as e:
            print(f"خطأ في إرسال إشعار Push: {str(e)}")

    def send_email():
        try:
            if recipient_email:
                # إعداد نص البريد الإلكتروني المحسن بتنسيق HTML
                email_body = f"""
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>رسالة جديدة</title>
                </head>
                <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Arial, sans-serif; background-color: #f5f5f5;">
                    <div style="max-width: 600px; margin: 20px auto; background-color: #ffffff; border-radius: 15px; overflow: hidden; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);">
                        <!-- رأس الرسالة -->
                        <div style="background: linear-gradient(135deg, #1976d2, #0d47a1); padding: 25px; text-align: center;">
                            <h1 style="color: white; margin: 0; font-size: 24px; font-weight: bold;">فضاء أساتذة العلوم الفيزيائية</h1>
                            <p style="color: rgba(255, 255, 255, 0.9); margin: 10px 0 0 0; font-size: 18px;">رسالة جديدة</p>
                        </div>
                        <!-- محتوى الرسالة -->
                        <div style="padding: 30px; text-align: right; direction: rtl; color: #333; font-size: 16px; line-height: 1.6;">
                            <p style="font-size: 18px; margin-bottom: 20px;">السلام عليكم،</p>
                            <p style="font-size: 17px; margin-bottom: 20px;">لديك رسالة جديدة من <span style="color: #1976d2; font-weight: bold;">{sender_name}</span> على منصة فضاء أساتذة العلوم الفيزيائية.</p>

                            <!-- إضافة وقت وتاريخ الإرسال -->
                            <div style="margin: 20px 0; padding: 10px; background-color: #f0f7ff; border-radius: 5px; font-size: 14px; color: #555;">
                                <p style="margin: 0;"><i class="far fa-clock" style="margin-left: 5px;"></i> تم الإرسال في: {formatted_date} - الساعة {current_time}</p>
                            </div>
                """

                # إضافة موضوع الرسالة إذا كان متوفرًا
                if message_subject:
                    email_body += f"""
                            <div style="margin: 20px 0; padding: 15px; background-color: #f5f8ff; border-right: 4px solid #1976d2; border-radius: 5px;">
                                <p style="margin: 0; font-size: 16px;"><span style="font-weight: bold; color: #1976d2;">الموضوع:</span> {message_subject}</p>
                            </div>
                    """

                # إضافة محتوى الرسالة إذا كان متوفرًا
                if message_content:
                    email_body += f"""
                            <div style="margin: 20px 0; padding: 20px; background-color: #f9f9f9; border: 1px solid #eee; border-radius: 8px;">
                                <p style="margin: 0; font-size: 16px;"><span style="font-weight: bold; color: #555;">الرسالة:</span></p>
                                <p style="margin: 10px 0 0 0; font-size: 16px; line-height: 1.6; color: #333;">{message_content}</p>
                            </div>
                    """

                # إضافة رابط للانتقال إلى المنصة
                email_body += f"""
                            <div style="text-align: center; margin: 30px 0;">
                                <a href="{host_url}messages" style="display: inline-block; background-color: #1976d2; color: white; text-decoration: none; padding: 12px 25px; border-radius: 5px; font-size: 16px; font-weight: bold;">الانتقال إلى صفحة الرسائل</a>
                            </div>

                            <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; color: #666;">
                                <p style="margin: 0; font-size: 16px;">مع تحيات،</p>
                                <p style="margin: 5px 0 0 0; font-size: 16px; color: #1976d2; font-weight: bold;">فريق منصة فضاء أساتذة العلوم الفيزيائية</p>
                            </div>
                        </div>

                        <!-- تذييل الرسالة -->
                        <div style="background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 14px; color: #666;">
                            <p style="margin: 0;">هذه رسالة آلية، يرجى عدم الرد عليها.</p>
                            <p style="margin: 5px 0 0 0;">© 2023 فضاء أساتذة العلوم الفيزيائية. جميع الحقوق محفوظة.</p>
                        </div>
                    </div>
                </body>
                </html>
                """

                send_email_notification(
                    recipient_email,
                    "رسالة جديدة على منصة فضاء أساتذة العلوم الفيزيائية",
                    email_body
                )

                # تسجيل نجاح إرسال البريد الإلكتروني
                logger.info(f"تم إرسال إشعار بريد إلكتروني إلى {recipient_email} بنجاح")

        except Exception as e:
            logger.error(f"خطأ في إرسال البريد الإلكتروني: {str(e)}")
            logger.error(traceback.format_exc())

    # بدء threads للإشعارات
    push_thread = threading.Thread(target=send_push)
    email_thread = threading.Thread(target=send_email)

    # تشغيل الـ threads
    push_thread.start()
    email_thread.start()



def get_resource_type(filename):

    """تحديد نوع المورد بناءً على امتداد الملف"""

    if not filename or '.' not in filename:

        return 'raw'

    ext = filename.rsplit('.', 1)[1].lower()

    if ext in ['jpg', 'jpeg', 'png', 'gif', 'bmp']:

        return 'image'

    elif ext in ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']:

        # جميع أنواع الملفات المستندية تعامل كـ raw

        return 'raw'

    elif ext in ['zip', 'rar']:

        return 'raw'

    return 'raw'



def upload_file_async(file, message_id):

    """رفع الملف إلى Cloudinary بشكل غير متزامن"""

    try:

        # تحديد نوع المورد بناءً على امتداد الملف

        resource_type = get_resource_type(file.filename)



        with app.app_context():

            message = Message.query.get(message_id)

            if not message:

                logger.error(f"Message {message_id} not found in upload_file_async")

                return



            # الحصول على معلومات المرسل

            sender = User.query.get(message.sender_id)

            if not sender:

                logger.error(f"Sender {message.sender_id} not found in upload_file_async")

                return



            # حفظ الملف مؤقتاً

            secure_name = secure_filename(file.filename)

            temp_dir = app.config.get('TEMP_FOLDER', 'tmp')



            # التأكد من وجود مجلد المؤقت

            if not os.path.exists(temp_dir):

                os.makedirs(temp_dir)



            temp_file_path = os.path.join(temp_dir, secure_name)

            logger.info(f'حفظ الملف مؤقتاً في: {temp_file_path}')

            file.save(temp_file_path)



            # التحقق إذا كان الملف بصيغة PDF

            is_pdf = is_pdf_file(file.filename)

            logger.info(f'هل الملف PDF؟ {is_pdf}')



            # إنشاء اسم مجلد خاص بالمستخدم - استخدام teacher_name بدون بادئة

            safe_folder_name = ''.join(e for e in sender.teacher_name if e.isalnum())



            # إضافة رقم عشوائي للملف للتأكد من إنشاء نسخة جديدة حتى لو نفس الملف

            random_suffix = secrets.token_hex(4)  # يولد سلسلة عشوائية من 8 أحرف



            # استخراج اسم الملف والامتداد

            filename, file_ext = os.path.splitext(file.filename)



            # إنشاء معرف داخلي فريد مع اسم الملف والامتداد

            internal_id = f"{filename}_{random_suffix}{file_ext}"



            # إنشاء مسار Cloudinary باستخدام المجلد واسم الملف الأصلي مع إضافة الرقم العشوائي في آخر المجلد

            # هذا يضمن عدم تكرار الملفات مع الاحتفاظ باسم ملف نظيف عند العرض

            public_path = f"attachments/{safe_folder_name}_{random_suffix}/async/{filename}{file_ext}"



            # تخصيص نوع المورد والتنسيق بناءً على نوع الملف

            if is_pdf:

                # نستخدم نوع المورد image بدلاً من raw

                upload_resource_type = "image"

                logger.info(f'تحويل PDF إلى JPG: {filename}, نوع المورد: {upload_resource_type}')

            else:

                upload_resource_type = resource_type

                logger.info(f'استخدام الامتداد الأصلي: {filename}, نوع المورد: {upload_resource_type}')



            upload_options = {

                'resource_type': upload_resource_type,

                'public_id': public_path,  # استخدام المسار الكامل مع اسم الملف الأصلي فقط

                'use_filename': True,

                'format': "jpg" if is_pdf else None,  # تحديد امتداد jpg لملفات PDF

                'unique_filename': False,  # نتحكم في التسمية يدويًا

                'overwrite': True  # السماح بالكتابة فوق ملف موجود

            }



            logger.info(f"Attempting to upload file {file.filename} to Cloudinary for message {message_id}")

            logger.debug(f"Upload options: {upload_options}")



            try:

                result = cloudinary.uploader.upload(temp_file_path, **upload_options)

                logger.info(f"Successfully uploaded file to Cloudinary: {result.get('public_id')}")



                # تحديث المرفق في قاعدة البيانات

                message.attachment = result['secure_url']

                message.attachment_name = file.filename

                message.cloudinary_public_id = result.get('public_id')

                message.cloudinary_resource_type = upload_resource_type

                db.session.commit()

                logger.info(f"Updated message {message_id} with attachment information")



                # مسح الملف المؤقت بعد الرفع

                if os.path.exists(temp_file_path):

                    os.remove(temp_file_path)



            except Exception as e:

                logger.error(f"Error uploading file to Cloudinary: {str(e)}")

                logger.error(traceback.format_exc())



                # مسح الملف المؤقت في حالة الخطأ

                if os.path.exists(temp_file_path):

                    os.remove(temp_file_path)



                try:

                    # محاولة تحديث المرفق لإظهار وجود خطأ

                    message = Message.query.get(message_id)

                    if message:

                        message.attachment = None

                        message.attachment_name = "خطأ في رفع الملف: " + str(e)

                        db.session.commit()

                        logger.info(f"Updated message {message_id} with error information")

                except Exception as inner_e:

                    # تجاهل أي خطأ إضافي

                    logger.error(f"Error updating message with error info: {str(inner_e)}")



    except Exception as e:

        logger.error(f"Error in upload_file_async: {str(e)}")

        logger.error(traceback.format_exc())



@app.route('/send_group_message', methods=['POST'])
@login_required
def send_group_message():
    """إرسال رسالة جماعية إلى عدة مستلمين"""
    try:
        # التحقق من أن المستخدم مدير أو مشرف
        if not current_user.is_admin:
            return jsonify({'status': 'error', 'message': 'ليس لديك صلاحية إرسال رسائل جماعية'}), 403

        # الحصول على قائمة المستلمين
        recipient_ids = request.form.getlist('recipient_ids[]')
        content = request.form.get('content')

        if not recipient_ids or not content:
            return jsonify({'status': 'error', 'message': 'يرجى ملء جميع الحقول المطلوبة'}), 400

        # التحقق من وجود المستلمين
        recipients = User.query.filter(User.id.in_(recipient_ids)).all()
        if not recipients:
            return jsonify({'status': 'error', 'message': 'لم يتم العثور على أي مستلم'}), 404

        # إنشاء قائمة لتخزين الرسائل المرسلة
        sent_messages = []

        # متغيرات لتخزين معلومات المرفق (سيتم استخدامها لجميع الرسائل)
        attachment_url = None
        attachment_name = None
        cloudinary_public_id = None
        cloudinary_resource_type = None

        # معالجة المرفقات مرة واحدة فقط
        files = request.files.getlist('attachment')

        # تحقق مما إذا كان هناك ملفات تم رفعها
        if files and files[0].filename:
            file = files[0]  # استخدام الملف الأول فقط للرسائل الجماعية

            # التحقق من صلاحية الملف
            if not allowed_file(file.filename, for_messages=True):
                return jsonify({'status': 'error', 'message': 'صيغة الملف غير مسموح بها'}), 400

            # التحقق من حجم الملف
            if not check_file_size(file):
                return jsonify({'status': 'error', 'message': 'حجم الملف يتجاوز الحد المسموح به (10 ميجابايت)'}), 400

            # حفظ الملف مؤقتًا
            secure_name = secure_filename(file.filename)
            temp_dir = app.config.get('TEMP_FOLDER', 'tmp')

            # التأكد من وجود المجلد المؤقت
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)

            temp_file_path = os.path.join(temp_dir, secure_name)
            file.save(temp_file_path)

            # تحديد نوع المورد
            is_pdf = is_pdf_file(file.filename)
            upload_resource_type = "image" if is_pdf else get_resource_type(file.filename)

            # إنشاء اسم مجلد خاص بالمستخدم
            safe_folder_name = ''.join(e for e in current_user.teacher_name if e.isalnum())

            # إضافة رقم عشوائي للملف
            random_suffix = secrets.token_hex(4)

            # استخراج اسم الملف والامتداد
            filename, file_ext = os.path.splitext(file.filename)

            # إنشاء مسار Cloudinary - استخدام مجلد group_messages لتمييز المرفقات الجماعية
            public_path = f"attachments/{safe_folder_name}_{random_suffix}/group_messages/{filename}{file_ext}"

            # خيارات الرفع
            upload_options = {
                'resource_type': upload_resource_type,
                'public_id': public_path,
                'use_filename': True,
                'format': "jpg" if is_pdf else None,
                'unique_filename': False,
                'overwrite': True
            }

            # رفع الملف إلى Cloudinary مرة واحدة فقط
            logger.info(f"رفع ملف مرفق للرسالة الجماعية: {filename}{file_ext}")
            result = cloudinary.uploader.upload(temp_file_path, **upload_options)

            # تخزين معلومات المرفق لاستخدامها في جميع الرسائل
            attachment_url = result['secure_url']
            attachment_name = file.filename
            cloudinary_public_id = result.get('public_id')
            cloudinary_resource_type = upload_resource_type

            # حذف الملف المؤقت
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)

            logger.info(f"تم رفع الملف بنجاح: {attachment_url}")

        # إنشاء رسالة لكل مستلم باستخدام نفس معلومات المرفق
        for recipient in recipients:
            message = Message(
                sender_id=current_user.id,
                recipient_id=recipient.id,
                content=content,
                attachment=attachment_url,
                attachment_name=attachment_name,
                cloudinary_public_id=cloudinary_public_id,
                cloudinary_resource_type=cloudinary_resource_type
            )

            # إضافة الرسالة إلى قاعدة البيانات
            db.session.add(message)
            sent_messages.append(message)

        # حفظ جميع الرسائل في قاعدة البيانات
        db.session.commit()

        # إرسال إشعارات للمستلمين
        for message in sent_messages:
            recipient = User.query.get(message.recipient_id)
            if recipient:
                send_notifications_async(
                    recipient_id=recipient.id,
                    recipient_email=recipient.email,
                    sender_name=current_user.teacher_name,
                    host_url=request.host_url,
                    message_content=content
                )

        # تحديث الرسائل المرسلة في واجهة المستخدم
        sent_messages_query = Message.query.filter_by(
            sender_id=current_user.id,
            deleted_by_sender=False
        ).order_by(Message.timestamp.desc()).all()

        sent_html = render_template('message_list.html',
                                  messages=sent_messages_query,
                                  is_received=False)

        return jsonify({
            'status': 'success',
            'message': 'تم إرسال الرسائل الجماعية بنجاح',
            'group_message': True,
            'sent_count': len(sent_messages),
            'sent_html': sent_html
        })

    except Exception as e:
        print(f"Error in send_group_message: {str(e)}")
        logger.error(f"Error in send_group_message: {str(e)}")
        logger.error(traceback.format_exc())
        if 'db' in locals():
            db.session.rollback()
        return jsonify({'status': 'error', 'message': 'حدث خطأ أثناء إرسال الرسائل الجماعية'}), 500

@app.route('/send_message', methods=['POST'])
@login_required
def send_message():
    try:
        recipient_id = request.form.get('recipient_id')
        content = request.form.get('content')

        if not recipient_id or not content:
            return jsonify({'status': 'error', 'message': 'يرجى ملء جميع الحقول المطلوبة'}), 400

        recipient = User.query.get(recipient_id)
        if not recipient:
            return jsonify({'status': 'error', 'message': 'المستلم غير موجود'}), 404

        message = Message(
            sender_id=current_user.id,
            recipient_id=recipient_id,
            content=content
        )



        # استخدام getlist بدلاً من get للحصول على جميع الملفات المرفقة

        files = request.files.getlist('attachment')



        # تحقق مما إذا كان هناك ملفات تم رفعها

        if files and len(files) > 0 and files[0].filename:

            # استخدام الملف الأول كمرفق رئيسي للحفاظ على التوافق مع النظام الحالي

            file = files[0]

            logger.info(f"Message has attachment: {file.filename}")



            # استخدام دالة allowed_file المحدثة مع معلمة for_messages=True

            if not allowed_file(file.filename, for_messages=True):

                logger.warning(f"File type not allowed: {file.filename}")

                return jsonify({

                    'status': 'error',

                    'message': 'نوع الملف غير مسموح به. الملفات المدعومة: PDF, Word, Excel, PowerPoint, Text, PNG, JPG, GIF, BMP, ZIP, RAR'

                }), 400



            # التحقق من حجم الملف

            file.seek(0, os.SEEK_END)

            file_size = file.tell()

            file.seek(0)  # إعادة مؤشر الملف إلى البداية



            if file_size > MAX_FILE_SIZE:

                logger.warning(f"File size exceeds maximum limit: {file.filename} ({file_size} bytes)")

                return jsonify({

                    'status': 'error',

                    'message': 'حجم الملف يتجاوز الحد الأقصى المسموح به (10 ميجابايت)'

                }), 400



            # التحقق من حجم الملفات الإضافية

            for additional_file in files[1:]:

                if additional_file.filename:

                    additional_file.seek(0, os.SEEK_END)

                    additional_file_size = additional_file.tell()

                    additional_file.seek(0)  # إعادة مؤشر الملف إلى البداية



                    if additional_file_size > MAX_FILE_SIZE:

                        logger.warning(f"Additional file size exceeds maximum limit: {additional_file.filename} ({additional_file_size} bytes)")

                        return jsonify({

                            'status': 'error',

                            'message': f'حجم الملف {additional_file.filename} يتجاوز الحد الأقصى المسموح به (10 ميجابايت)'

                        }), 400



            try:

                # حفظ الملف مؤقتاً

                secure_name = secure_filename(file.filename)

                temp_file_path = os.path.join(app.config.get('TEMP_FOLDER', 'tmp'), secure_name)

                logger.info(f'حفظ الملف مؤقتاً في: {temp_file_path}')

                file.save(temp_file_path)



                # التحقق إذا كان الملف بصيغة PDF

                is_pdf = is_pdf_file(file.filename)

                logger.info(f'هل الملف PDF؟ {is_pdf}')



                # تحديد نوع المورد

                resource_type = get_resource_type(file.filename)

                logger.debug(f"Resource type determined as: {resource_type}")



                # إنشاء اسم مجلد خاص بالمستخدم بدون بادئة

                safe_folder_name = ''.join(e for e in current_user.teacher_name if e.isalnum())



                # إضافة رقم عشوائي منفصل للاستخدام داخل كلودناري

                random_suffix = secrets.token_hex(4)



                # إعدادات الرفع مع الحفاظ على اسم الملف الأصلي وإضافة مجلد المستخدم

                # لاحظ أننا نستخدم اسمًا داخليًا فقط لتمييز الملفات

                filename, file_ext = os.path.splitext(file.filename)



                # إنشاء معرف داخلي فريد مع اسم الملف والامتداد

                internal_id = f"{filename}_{random_suffix}{file_ext}"



                # إنشاء مسار Cloudinary باستخدام المجلد واسم الملف الأصلي مع إضافة الرقم العشوائي في آخر المجلد

                # هذا يضمن عدم تكرار الملفات مع الاحتفاظ باسم ملف نظيف عند العرض

                public_path = f"attachments/{safe_folder_name}_{random_suffix}/{filename}{file_ext}"



                # تخصيص نوع المورد والتنسيق بناءً على نوع الملف

                if is_pdf:

                    # نستخدم نوع المورد image بدلاً من raw

                    upload_resource_type = "image"

                    logger.info(f'تحويل PDF إلى JPG: {filename}, نوع المورد: {upload_resource_type}')

                else:

                    upload_resource_type = resource_type

                    logger.info(f'استخدام الامتداد الأصلي: {filename}, نوع المورد: {upload_resource_type}')



                upload_options = {

                    'resource_type': upload_resource_type,

                    'public_id': public_path,  # استخدام المسار الكامل مع اسم الملف الأصلي فقط

                    'use_filename': True,

                    'format': "jpg" if is_pdf else None,  # تحديد امتداد jpg لملفات PDF

                    'unique_filename': False,  # نتحكم في التسمية يدويًا

                    'overwrite': True  # السماح بالكتابة فوق ملف موجود

                }



                logger.info(f"Attempting to upload attachment in send_message: {file.filename}")

                logger.debug(f"Upload options: {upload_options}")



                try:

                    result = cloudinary.uploader.upload(temp_file_path, **upload_options)

                    logger.info(f"Successfully uploaded attachment: {result.get('public_id')}")



                    message.attachment = result['secure_url']

                    message.attachment_name = file.filename



                    # احفظ معلومات إضافية لتسهيل عملية الحذف لاحقاً

                    message.cloudinary_public_id = result.get('public_id')

                    message.cloudinary_resource_type = upload_resource_type



                    # حذف الملف المؤقت

                    if os.path.exists(temp_file_path):

                        os.remove(temp_file_path)



                except Exception as upload_error:

                    logger.error(f"Cloudinary upload error: {str(upload_error)}")

                    logger.error(traceback.format_exc())



                    # حذف الملف المؤقت في حالة الخطأ

                    if os.path.exists(temp_file_path):

                        os.remove(temp_file_path)



                    return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء رفع الملف: {str(upload_error)}'}), 500



            except Exception as e:

                logger.error(f"Error uploading file to Cloudinary: {str(e)}")

                logger.error(traceback.format_exc())



                # حذف الملف المؤقت في حالة الخطأ

                if 'temp_file_path' in locals() and os.path.exists(temp_file_path):

                    os.remove(temp_file_path)



                return jsonify({'status': 'error', 'message': 'حدث خطأ أثناء رفع الملف'}), 500



        db.session.add(message)

        db.session.commit()



        # إذا كان هناك ملفات إضافية (الملف الثاني وما بعده)، قم برفعها بشكل غير متزامن

        if files and len(files) > 1:

            for i in range(1, len(files)):

                if files[i].filename:

                    # رفع الملفات الإضافية بشكل غير متزامن

                    # سنستخدم النمط نفسه من الرفع

                    threading.Thread(target=upload_additional_file_async, args=(files[i], message.id, is_pdf_file(files[i].filename))).start()



        send_notifications_async(

            recipient_id=recipient.id,

            recipient_email=recipient.email,

            sender_name=current_user.teacher_name,

            host_url=request.host_url,

            message_content=content

        )



        # توليد HTML للرسالة المرسلة لتحديث واجهة المستخدم فوراً

        # الحصول على الرسائل المرسلة المحدثة

        sent_messages = Message.query.filter_by(

            sender_id=current_user.id,

            deleted_by_sender=False

        ).order_by(Message.timestamp.desc()).all()


        # توليد HTML للرسائل المرسلة

        sent_html = render_template('message_list.html',

                                  messages=sent_messages,

                                  is_received=False)



        return jsonify({

            'status': 'success',

            'message': 'تم إرسال الرسالة بنجاح',

            'messageId': message.id,

            'sent_html': sent_html

        })



    except Exception as e:

        print(f"Error saving message to database: {str(e)}")

        logger.error(f"Error saving message to database: {str(e)}")

        logger.error(traceback.format_exc())

        db.session.rollback()

        return jsonify({'status': 'error', 'message': 'حدث خطأ أثناء حفظ الرسالة'}), 500


    except Exception as e:

        print(f"Error in send_message: {str(e)}")

        logger.error(f"Error in send_message: {str(e)}")

        logger.error(traceback.format_exc())

        if 'db' in locals():

            db.session.rollback()

        return jsonify({'status': 'error', 'message': 'حدث خطأ أثناء إرسال الرسالة'}), 500



def process_message_async(file, message_id, recipient_email, sender_name, host_url):

    """معالجة الملف المرفق والإشعارات في الخلفية"""

    def process():

        try:

            # رفع الملف

            if file:

                upload_file_async(file, message_id)



            # إرسال الإشعارات

            send_notifications_async(

                recipient_id=message_id,

                recipient_email=recipient_email,

                sender_name=sender_name,

                host_url=host_url

            )

        except Exception as e:

            print(f"Error in async processing: {str(e)}")



    thread = threading.Thread(target=process)

    thread.daemon = True

    thread.start()



@app.route('/get_messages')

@login_required

def get_messages():

    try:

        # الحصول على الرسائل المستلمة (غير المحذوفة من قبل المستلم)

        received_messages = Message.query.filter_by(

            recipient_id=current_user.id,

            deleted_by_recipient=False

        ).order_by(Message.timestamp.desc()).all()



        # الحصول على الرسائل المرسلة (غير المحذوفة من قبل المرسل)

        sent_messages = Message.query.filter_by(

            sender_id=current_user.id,

            deleted_by_sender=False

        ).order_by(Message.timestamp.desc()).all()



        # الحصول على قائمة المستخدمين

        users = User.query.filter(User.id != current_user.id).all()



        # تقديم قوالب HTML للرسائل المستلمة والمرسلة

        received_html = render_template('message_list.html',

                                      messages=received_messages,

                                      is_received=True)


        sent_html = render_template('message_list.html',

                                   messages=sent_messages,

                                   is_received=False)


        # إرجاع البيانات بتنسيق JSON

        return jsonify({

            'status': 'success',

            'received': received_html,

            'sent': sent_html

        })


    except Exception as e:

        print(f"Error getting messages: {str(e)}")

        return jsonify({

            'status': 'error',

            'message': 'حدث خطأ أثناء تحميل الرسائل'

        }), 500



@app.route('/download_attachment/<int:message_id>')

@login_required

def download_attachment(message_id):

    try:

        message = Message.query.get_or_404(message_id)



        # التحقق من صلاحية الوصول

        if message.sender_id != current_user.id and message.recipient_id != current_user.id:

            abort(403)



        if not message.attachment or not message.attachment_name:

            abort(404)



        # تحميل الملف من Cloudinary

        response = requests.get(message.attachment)

        if response.status_code != 200:

            abort(404)



        # إنشاء ملف مؤقت

        temp_file = BytesIO(response.content)



        # اسم الملف للتنزيل

        download_filename = message.attachment_name



        # التحقق إذا كان الملف الأصلي PDF ولكن تم رفعه كصورة

        is_pdf_file_orig = is_pdf_file(download_filename)



        # تحديد نوع MIME للملف

        if is_pdf_file_orig and message.cloudinary_resource_type == 'image':

            mime_type = 'application/pdf'

            logger.info(f'تحويل JPG إلى PDF للملف المرفق: {download_filename}')



            try:

                # تحميل الصورة

                img = Image.open(temp_file)



                # إنشاء ملف PDF جديد في الذاكرة

                pdf_buffer = BytesIO()



                # حفظ الصورة كملف PDF

                img.save(pdf_buffer, format='PDF')



                # إعادة تعيين المؤشر إلى بداية البافر

                pdf_buffer.seek(0)



                # استخدام البافر بدلاً من الملف المؤقت

                temp_file = pdf_buffer

                logger.info(f'تم تحويل JPG إلى PDF بنجاح للملف المرفق: {download_filename}')

            except Exception as e:

                logger.error(f"خطأ في تحويل JPG إلى PDF للملف المرفق: {str(e)}")

                logger.error(traceback.format_exc())

                # إذا فشل التحويل، استمر بإرسال الملف الأصلي

                mime_type = mimetypes.guess_type(download_filename)[0] or 'application/octet-stream'

        else:

            mime_type = mimetypes.guess_type(download_filename)[0] or 'application/octet-stream'



        return send_file(

            temp_file,

            mimetype=mime_type,

            as_attachment=True,

            download_name=download_filename,

            max_age=0

        )



    except Exception as e:

        print(f"Error downloading attachment: {str(e)}")

        logger.error(f"Error downloading attachment: {str(e)}")

        logger.error(traceback.format_exc())

        abort(500)



    """

    إعادة توجيه رسالة إلى مستلم أو عدة مستلمين

    """

    logger.info(f"Forwarding message {message_id} to recipients {recipient_ids}")

    try:

        # تحميل الرسالة الأصلية

        original_message = Message.query.get(message_id)

        if not original_message:

            logger.error(f"Original message {message_id} not found")

            return {"error": "الرسالة الأصلية غير موجودة"}, 404



        # الحصول على معلومات المرسل

        sender = current_user

        safe_folder_name = ''.join(e for e in sender.teacher_name if e.isalnum())



        # التحقق مما إذا كانت الرسالة الأصلية تحتوي على مرفق

        attachment_url = None

        attachment_name = None

        cloudinary_public_id = None

        cloudinary_resource_type = None



        if original_message.attachment:

            try:

                logger.info(f"Original message has attachment: {original_message.attachment_name}")



                # رفع المرفق الأصلي إلى كلودناري مرة أخرى

                # استرجاع المرفق من الخادم

                response = requests.get(original_message.attachment)

                if response.status_code != 200:

                    logger.error(f"Failed to download attachment from {original_message.attachment}")

                    raise ValueError(f"Could not download attachment: {response.status_code}")



                # إنشاء ملف مؤقت للمرفق

                temp_file = io.BytesIO(response.content)

                temp_file.name = original_message.attachment_name or "forwarded_file"



                # تحديد نوع المورد

                resource_type = original_message.cloudinary_resource_type or get_resource_type(temp_file.name)



                # إضافة رقم عشوائي للملف للتأكد من إنشاء نسخة جديدة

                random_suffix = secrets.token_hex(4)  # يولد سلسلة عشوائية من 8 أحرف



                # استخراج اسم الملف والامتداد

                filename, file_ext = os.path.splitext(temp_file.name)



                # إنشاء معرف داخلي فريد مع اسم الملف والامتداد

                internal_id = f"{filename}_{random_suffix}{file_ext}"



                # إنشاء مسار Cloudinary باستخدام المجلد واسم الملف الأصلي (بدون الرقم العشوائي)

                public_path = f"attachments/{safe_folder_name}_{random_suffix}/forwarded/{filename}{file_ext}"



                # رفع الملف إلى كلودناري

                upload_options = {

                    'resource_type': resource_type,

                    'public_id': public_path,  # استخدام المسار الكامل مع اسم الملف الأصلي فقط

                    'use_filename': False,  # لا نستخدم اسم الملف الأصلي، بل نحدده يدويًا

                    'unique_filename': False,  # نتحكم في التسمية يدويًا

                    'overwrite': False  # نسمح بالكتابة فوق الملف إذا كان موجودًا بنفس الاسم

                }



                # إضافة خيارات إضافية للصور

                if resource_type == 'image':

                    upload_options.update({

                        'quality': 'auto',

                        'fetch_format': 'auto'

                    })



                logger.info(f"Uploading forwarded attachment {temp_file.name} to Cloudinary")

                logger.debug(f"Upload options: {upload_options}")



                result = cloudinary.uploader.upload(temp_file, **upload_options)

                logger.info(f"Successfully uploaded forwarded attachment to Cloudinary: {result.get('public_id')}")



                # تعيين معلومات المرفق

                attachment_url = result['secure_url']

                attachment_name = original_message.attachment_name

                cloudinary_public_id = result.get('public_id')

                cloudinary_resource_type = resource_type



            except Exception as e:

                logger.error(f"Error forwarding attachment: {str(e)}")

                logger.error(traceback.format_exc())

                # استمر في إعادة توجيه الرسالة حتى لو فشل رفع المرفق



        # إنشاء رسائل جديدة لكل مستلم

        forwarded_messages = []

        for recipient_id in recipient_ids:

            try:

                recipient = User.query.get(recipient_id)

                if not recipient:

                    logger.warning(f"Recipient {recipient_id} not found")

                    continue



                new_message = Message(

                    sender_id=sender.id,

                    recipient_id=recipient_id,

                    content=content,

                    attachment=attachment_url,

                    attachment_name=attachment_name,

                    cloudinary_public_id=cloudinary_public_id,

                    cloudinary_resource_type=cloudinary_resource_type,

                    forwarded_from=message_id

                )

                db.session.add(new_message)

                forwarded_messages.append(new_message)



            except Exception as e:

                logger.error(f"Error creating forwarded message for recipient {recipient_id}: {str(e)}")



        # حفظ التغييرات

        db.session.commit()



        # إرسال الإشعارات للرسائل المعاد توجيهها

        for message in forwarded_messages:

            try:

                send_message_notification(message)

            except Exception as e:

                logger.error(f"Error sending notification for forwarded message {message.id}: {str(e)}")



        return {

            "success": True,

            "message": "تمت إعادة التوجيه بنجاح",

            "forwarded_count": len(forwarded_messages)

        }



    except Exception as e:

        logger.error(f"Error in forward_message: {str(e)}")

        logger.error(traceback.format_exc())

        db.session.rollback()

        return {"error": f"حدث خطأ أثناء إعادة التوجيه: {str(e)}"}, 500



@app.route('/mark_as_read/<int:message_id>')

@login_required

def mark_as_read(message_id):
    """تحديد رسالة كمقروءة"""
    try:
        message = Message.query.get_or_404(message_id)

        if message.recipient_id == current_user.id:
            message.is_read = True
            db.session.commit()
            return jsonify({'status': 'success', 'message': 'تم تحديد الرسالة كمقروءة'})
        else:
            return jsonify({'status': 'error', 'message': 'ليس لديك الصلاحية لتحديد هذه الرسالة كمقروءة'}), 403

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error marking message as read: {str(e)}")
        return jsonify({'status': 'error', 'message': f'حدث خطأ: {str(e)}'}), 500



@app.route('/get_unread_count')

@login_required

def get_unread_count():

    count = current_user.get_unread_messages_count()

    return {'count': count}



@app.route('/profile/<int:user_id>')

@login_required

def profile(user_id):

    user = User.query.get_or_404(user_id)

    return render_template('profile.html', user=user)



@app.route('/delete_messages', methods=['POST'])

@login_required

def delete_messages():

    try:

        data = request.form.getlist('message_ids[]')

        if not data:

            return jsonify({'status': 'error', 'message': 'لم يتم تحديد أي رسائل'}), 400



        messages = Message.query.filter(Message.id.in_(data)).all()

        for message in messages:

            # التحقق من أن المستخدم له صلاحية حذف الرسالة

            if message.sender_id == current_user.id:

                message.deleted_by_sender = True

            if message.recipient_id == current_user.id:

                message.deleted_by_recipient = True



            # إذا تم حذف الرسالة من الطرفين، احذفها نهائياً

            if message.deleted_by_sender and message.deleted_by_recipient:

                # لا نقوم بحذف المرفق من Cloudinary للاحتفاظ بالملفات
                # حتى بعد حذف الرسائل
                # تم تعطيل كود حذف المرفقات من Cloudinary



                db.session.delete(message)



        db.session.commit()

        return jsonify({'status': 'success', 'message': 'تم حذف الرسائل بنجاح'})

    except Exception as e:

        print(f"Error deleting messages: {str(e)}")

        db.session.rollback()

        return jsonify({'status': 'error', 'message': 'حدث خطأ أثناء حذف الرسائل'}), 500



@app.route('/delete_message/<int:message_id>', methods=['POST'])

@login_required

def delete_message(message_id):

    try:

        message = Message.query.get_or_404(message_id)



        # التحقق من أن المستخدم له صلاحية حذف الرسالة

        if current_user.id not in [message.sender_id, message.recipient_id]:

            return jsonify({'status': 'error', 'message': 'غير مصرح لك بحذف هذه الرسالة'}), 403



        # تحديد من قام بالحذف

        if current_user.id == message.sender_id:

            message.deleted_by_sender = True

        else:

            message.deleted_by_recipient = True



        # إذا تم حذف الرسالة من الطرفين، احذفها نهائياً

        if message.deleted_by_sender and message.deleted_by_recipient:

            # لا نقوم بحذف المرفق من Cloudinary للاحتفاظ بالملفات
            # حتى بعد حذف الرسائل
            # تم تعطيل كود حذف المرفقات من Cloudinary



            db.session.delete(message)



        db.session.commit()

        return jsonify({'status': 'success', 'message': 'تم حذف الرسالة بنجاح'})

    except Exception as e:

        print(f"Error deleting message: {str(e)}")

        db.session.rollback()

        return jsonify({'status': 'error', 'message': 'حدث خطأ أثناء حذف الرسالة'}), 500



@app.route('/subscribe', methods=['POST'])

@login_required

def subscribe():

    subscription_json = request.get_json()

    subscription = PushSubscription(

        user_id=current_user.id,

        subscription_json=json.dumps(subscription_json)

    )

    db.session.add(subscription)

    db.session.commit()

    return jsonify({'status': 'success'})



@app.route('/vapid-public-key')

def get_vapid_public_key():

    return jsonify({'publicKey': app.config['VAPID_PUBLIC_KEY']})



# تم تعطيل دالة generate_csrf_token المخصصة واستخدام دالة generate_csrf من Flask-WTF بدلاً من ذلك
# def generate_csrf_token():
#     """
#     توليد رمز CSRF وتخزينه في الجلسة
#     يتم استدعاء هذه الدالة من خلال قوالب Jinja
#     """
#     # تم تعطيل هذه الدالة واستخدام دالة generate_csrf من Flask-WTF بدلاً من ذلك
#     pass

# إضافة دالة generate_csrf من Flask-WTF إلى متغيرات البيئة العامة لـ Jinja
app.jinja_env.globals['csrf_token'] = generate_csrf



@app.errorhandler(413)

def too_large(e):

    return jsonify({

        'status': 'error',

        'message': 'حجم الملف كبير جداً. الحد الأقصى المسموح به هو 10 ميجابايت'

    }), 413



# صفحة الملفات التعليمية

@app.route('/educational_files')

@login_required

def educational_files():

    semester = request.args.get('semester', 'all')  # افتراضياً جميع الفصول

    file_type = request.args.get('file_type', 'all')  # افتراضياً جميع الأنواع

    level = request.args.get('level', 'all')  # افتراضياً جميع المستويات



    # بناء الاستعلام الأساسي

    query = EducationalFile.query



    # تطبيق فلتر الفصل الدراسي إذا كان محددًا

    if semester != 'all':

        try:

            semester_int = int(semester)

            if semester_int in [1, 2, 3]:

                query = query.filter_by(semester=semester_int)

                semester = semester_int

            else:

                semester = 1

                query = query.filter_by(semester=1)

        except:

            semester = 1

            query = query.filter_by(semester=1)



    # إذا تم تحديد نوع ملف معين

    if file_type != 'all':

        query = query.filter_by(file_type=file_type)



    # إذا تم تحديد مستوى معين

    if level != 'all':

        query = query.filter_by(level=level)



    # ترتيب الملفات حسب تاريخ الرفع (الأحدث أولاً)

    files = query.order_by(EducationalFile.upload_date.desc()).all()



    return render_template('educational_files.html',

                          files=files,

                          current_semester=semester,

                          current_file_type=file_type,

                          current_level=level)



# رفع ملف تعليمي جديد

@app.route('/upload_educational_file', methods=['POST'])

@login_required

def upload_educational_file():

    if 'file' not in request.files:

        app.logger.error('لم يتم اختيار ملف - file not in request.files')

        return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})



    files = request.files.getlist('file')

    app.logger.info(f'تم استلام {len(files)} ملف')



    if not files or files[0].filename == '':

        app.logger.error('لم يتم اختيار ملف - empty file list or filename')

        return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})



    level = request.form.get('level', 'السنة الأولى متوسط')

    semester = request.form.get('semester', '1')

    file_type = request.form.get('file_type', '')

    description = request.form.get('description', '')

    subject = request.form.get('subject', 'فيزياء')



    try:

        semester = int(semester)

        if semester not in [1, 2, 3]:

            semester = 1

    except:

        semester = 1



    if file_type not in ['مذكرات', 'فروض', 'اختبارات', 'ملفات أخرى']:

        app.logger.error(f'نوع الملف غير صحيح: {file_type}')

        return jsonify({'success': False, 'message': 'نوع الملف غير صحيح'})



    # العد لتتبع عدد الملفات المرفوعة بنجاح

    success_count = 0

    error_count = 0

    error_messages = []



    # الحد الأقصى لحجم الملف (10 ميجابايت)

    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB in bytes



    for file in files:

        app.logger.info(f'معالجة الملف: {file.filename}')



        # التحقق من نوع الملف

        if not allowed_file(file.filename):

            error_message = f'نوع الملف "{file.filename}" غير مسموح به'

            app.logger.error(error_message)

            error_messages.append(error_message)

            error_count += 1

            continue



        # التحقق من حجم الملف

        file.seek(0, os.SEEK_END)

        file_size = file.tell()

        file.seek(0)  # إعادة المؤشر إلى بداية الملف



        if file_size > MAX_FILE_SIZE:

            error_message = f'حجم الملف "{file.filename}" يتجاوز الحد المسموح به (10 ميجابايت)'

            app.logger.error(error_message)

            error_messages.append(error_message)

            error_count += 1

            continue



        # حفظ الملف في كلاودناري

        try:

            secure_name = secure_filename(file.filename)

            temp_file_path = os.path.join(app.config['TEMP_FOLDER'], secure_name)

            app.logger.info(f'حفظ الملف مؤقتاً في: {temp_file_path}')

            file.save(temp_file_path)



            # الحصول على الاسم الأصلي للملف

            original_filename = file.filename



            # تحديد نوع المورد

            resource_type = get_resource_type(file.filename)

            app.logger.info(f'نوع المورد: {resource_type}')



            # تحديد مسار المجلد في كلاودناري

            folder_path = f"educational_files/{level}/الفصل_{semester}/{file_type}"



            # استخراج اسم الملف الأصلي

            file_name_with_ext = os.path.basename(original_filename)



            # التحقق إذا كان الملف بصيغة PDF

            is_pdf = is_pdf_file(original_filename)

            app.logger.info(f'هل الملف PDF؟ {is_pdf}')



            # إضافة طابع زمني للملف للتأكد من أنه فريد حتى عند رفع نفس الملف مرة أخرى

            timestamp = int(time.time())

            file_name_without_ext = os.path.splitext(file_name_with_ext)[0]



            # إذا كان الملف PDF، قم بتغيير امتداده إلى JPG عند الرفع

            if is_pdf:

                # نحتفظ بالاسم الأصلي لحفظه في قاعدة البيانات

                cloudinary_file_name = f"{file_name_without_ext}_{timestamp}"  # إضافة طابع زمني

                # نستخدم نوع المورد image بدلاً من raw

                upload_resource_type = "image"

                app.logger.info(f'تحويل PDF إلى JPG: {cloudinary_file_name}, نوع المورد: {upload_resource_type}')

            else:

                cloudinary_file_name = f"{file_name_without_ext}_{timestamp}"  # إضافة طابع زمني

                upload_resource_type = resource_type

                app.logger.info(f'استخدام الامتداد الأصلي: {cloudinary_file_name}, نوع المورد: {upload_resource_type}')



            app.logger.info(f'بدء رفع الملف إلى Cloudinary: {cloudinary_file_name}')



            # استخدام اسم الملف الأصلي كاسم الملف النهائي

            upload_result = cloudinary.uploader.upload(

                temp_file_path,

                folder=folder_path,

                public_id=cloudinary_file_name,  # استخدام اسم الملف مع الطابع الزمني

                resource_type=upload_resource_type,

                use_filename=True,

                format="jpg" if is_pdf else None,  # تحديد امتداد jpg لملفات PDF

                unique_filename=False,  # استخدمنا طابع زمني يدوياً، لا نحتاج Cloudinary لتوليد اسم فريد

                overwrite=True  # السماح بالكتابة فوق الملفات السابقة بنفس الاسم

            )



            app.logger.info(f'نتيجة الرفع: {upload_result["secure_url"]}')



            # حذف الملف المؤقت

            os.remove(temp_file_path)



            # إنشاء سجل الملف في قاعدة البيانات

            new_file = EducationalFile(

                filename=upload_result['public_id'],

                original_filename=original_filename,  # نحتفظ بالاسم الأصلي مع امتداد PDF

                file_type=file_type,

                semester=semester,

                subject=subject,

                level=level,

                mimetype=file.mimetype,

                file_size=upload_result.get('bytes', 0),

                description=description,

                cloudinary_url=upload_result['secure_url'],

                user_id=current_user.id,

                cloudinary_resource_type=upload_resource_type

            )



            db.session.add(new_file)

            db.session.commit()



            success_count += 1

            app.logger.info(f'تم رفع الملف بنجاح: {original_filename}')

        except Exception as e:

            app.logger.error(f"خطأ في رفع الملف {file.filename}: {str(e)}")

            app.logger.error(traceback.format_exc())  # طباعة تفاصيل الخطأ

            error_messages.append(f'حدث خطأ أثناء رفع الملف {file.filename}: {str(e)}')

            error_count += 1



    # إعداد الرد بناءً على نتيجة الرفع

    response = {

        'success': success_count > 0,

        'success_count': success_count,

        'error_count': error_count

    }



    # إضافة الرسائل المناسبة

    if success_count > 0:

        response['message'] = f'تم رفع {success_count} ملف بنجاح'

    else:

        response['message'] = 'لم يتم رفع أي ملف'



    # إضافة رسائل الخطأ إذا وجدت

    if error_count > 0:

        response['error_messages'] = error_messages



    return jsonify(response)

# إضافة دالة للحصول على رابط Cloudinary بدون علامات مائية
def get_cloudinary_url_without_watermark(url):
    """إزالة العلامات المائية والتأثيرات من روابط Cloudinary"""
    if not url or 'cloudinary' not in url:
        return url

    # تحليل URL
    parts = url.split('/')

    # البحث عن الجزء قبل الاسم (قد يحتوي على تحويلات)
    upload_index = -1
    for i, part in enumerate(parts):
        if part == 'upload':
            upload_index = i
            break

    if upload_index >= 0 and upload_index + 1 < len(parts):
        # تصحيح امتداد الملف إذا كانت نهاية URL تحتوي على .pdf ولكن الملف في الواقع صورة JPG
        # غالبًا ما يحدث هذا عند رفع ملف PDF كصورة JPG ولكن Cloudinary يحتفظ بالاسم الأصلي بامتداد .pdf
        if '/image/upload/' in url and url.lower().endswith('.pdf'):
            app.logger.info(f'تصحيح امتداد ملف PDF محول: {url}')
            # تغيير امتداد الملف من .pdf إلى .jpg في URL
            url_parts = url.rsplit('.', 1)
            if len(url_parts) > 1:
                url = url_parts[0] + '.jpg'
                app.logger.info(f'URL بعد التصحيح: {url}')

        # تقسيم URL مرة أخرى بعد التصحيح المحتمل
        parts = url.split('/')

        # إزالة أي تحويلات بعد upload وقبل الامتداد
        # بناء URL جديد
        new_url = '/'.join(parts[:upload_index+1]) + '/fl_attachment/' + '/'.join(parts[upload_index+1:])
        return new_url

    return url

@app.route('/download_educational_file/<int:file_id>')
@login_required
def download_educational_file(file_id):
    try:
        # الحصول على الملف التعليمي
        educational_file = EducationalFile.query.get_or_404(file_id)

        # زيادة عداد التنزيل
        educational_file.download_count += 1
        db.session.commit()

        if not educational_file.cloudinary_url:
            flash('لا يمكن تحميل الملف، الرابط غير متوفر', 'danger')
            return redirect(url_for('educational_files'))

        # التحقق مما إذا كان الملف الأصلي PDF (بناءً على الاسم الأصلي)
        original_ext = os.path.splitext(educational_file.original_filename)[1].lower()
        is_pdf_file_orig = original_ext == '.pdf'

        # تصحيح رابط الملف للتأكد من أنه يستخدم الامتداد الصحيح
        cloudinary_url = educational_file.cloudinary_url
        # الحصول على رابط التنزيل بدون علامات مائية
        download_url = get_cloudinary_url_without_watermark(cloudinary_url)
        app.logger.info(f'تنزيل الملف: {educational_file.original_filename}, الرابط: {download_url}')

        # تحميل الملف من Cloudinary
        try:
            response = requests.get(download_url)

            # التحقق من استجابة الخادم
            if response.status_code != 200:
                # إذا كان كود الاستجابة 404، فهذا يعني أن الملف غير موجود (تم حذفه)
                if response.status_code == 404:
                    flash('تم حذف الملف من طرف الإدارة', 'warning')
                else:
                    flash('حدث خطأ في الاتصال بالخادم، يرجى المحاولة مرة أخرى لاحقًا', 'danger')
                    app.logger.error(f'خطأ في تنزيل الملف: {response.status_code}')
                return redirect(url_for('educational_files'))

            # إنشاء ملف مؤقت
            temp_file = BytesIO(response.content)

            # تحديد اسم الملف للتنزيل (استخدام الاسم الأصلي مع الامتداد الأصلي)
            download_filename = educational_file.original_filename

            # إذا كان الملف الأصلي PDF وتم رفعه كـ JPG
            if is_pdf_file_orig and educational_file.cloudinary_resource_type == 'image':
                mime_type = 'application/pdf'
                app.logger.info(f'تحويل JPG إلى PDF للملف: {download_filename}')

                try:
                    # تحميل الصورة
                    img = Image.open(temp_file)

                    # إنشاء ملف PDF جديد في الذاكرة
                    pdf_buffer = BytesIO()

                    # حفظ الصورة كملف PDF
                    img.save(pdf_buffer, format='PDF')

                    # إعادة تعيين المؤشر إلى بداية البافر
                    pdf_buffer.seek(0)

                    # استخدام البافر بدلاً من الملف المؤقت
                    temp_file = pdf_buffer
                    app.logger.info(f'تم تحويل JPG إلى PDF بنجاح: {download_filename}')
                except Exception as e:
                    app.logger.error(f"خطأ في تحويل JPG إلى PDF: {str(e)}")
                    app.logger.error(traceback.format_exc())
                    # إذا فشل التحويل، استمر بإرسال الملف الأصلي
            else:
                # استخدام نوع الوسائط المناسب بناءً على الملف الأصلي
                mime_type = mimetypes.guess_type(download_filename)[0] or 'application/octet-stream'

            return send_file(
                temp_file,
                mimetype=mime_type,
                as_attachment=True,
                download_name=download_filename,
                max_age=0
            )

        except requests.exceptions.RequestException as e:
            app.logger.error(f"خطأ في طلب تنزيل الملف: {str(e)}")
            flash('تم حذف الملف من طرف الإدارة', 'warning')
            return redirect(url_for('educational_files'))

    except Exception as e:
        # معالجة أي أخطاء أخرى
        db.session.rollback()
        app.logger.error(f"خطأ في تحميل الملف: {str(e)}")
        app.logger.error(traceback.format_exc())
        flash('تم حذف الملف من طرف الإدارة', 'warning')
        return redirect(url_for('educational_files'))

@app.route('/delete_educational_file/<int:file_id>', methods=['POST'])
@login_required
def delete_educational_file(file_id):
    try:
        # الحصول على الملف المراد حذفه
        educational_file = EducationalFile.query.get_or_404(file_id)

        # التأكد من أن المستخدم لديه حق الحذف (المشرف أو صاحب الملف)
        if not current_user.is_admin and educational_file.user_id != current_user.id:
            return jsonify({'success': False, 'message': 'غير مسموح لك بحذف هذا الملف'}), 403

        # حذف الملف من كلاودناري إذا كان له cloudinary_public_id
        if educational_file.cloudinary_url:
            try:
                # استخراج المعرف العام لحذف الملف من كلاودناري
                public_id = educational_file.filename
                resource_type = educational_file.cloudinary_resource_type or 'raw'

                app.logger.info(f'محاولة حذف الملف من Cloudinary: {public_id}, نوع المورد: {resource_type}')

                # حذف الملف من كلاودناري
                result = cloudinary.uploader.destroy(
                    public_id,
                    resource_type=resource_type
                )

                app.logger.info(f'نتيجة حذف الملف من Cloudinary: {result}')
            except Exception as e:
                app.logger.error(f'خطأ في حذف الملف من Cloudinary: {str(e)}')
                # نستمر في حذف الملف من قاعدة البيانات حتى لو فشل حذفه من كلاودناري

        # حذف الملف من قاعدة البيانات
        db.session.delete(educational_file)
        db.session.commit()

        app.logger.info(f'تم حذف الملف بنجاح: {educational_file.original_filename}')

        return jsonify({'success': True, 'message': 'تم حذف الملف بنجاح'})
    except Exception as e:
        db.session.rollback()
        app.logger.error(f'خطأ في حذف الملف: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء حذف الملف'}), 500

@app.route('/get_filtered_files', methods=['GET'])
@login_required
def get_filtered_files():
    semester = request.args.get('semester', 'all')
    file_type = request.args.get('file_type', 'all')
    level = request.args.get('level', 'all')

    app.logger.info(f'تصفية الملفات: المستوى={level}, الفصل={semester}, النوع={file_type}')

    # بناء الاستعلام الأساسي
    query = EducationalFile.query

    # تطبيق فلتر الفصل الدراسي إذا كان محددًا
    if semester != 'all':
        try:
            semester_int = int(semester)
            if semester_int in [1, 2, 3]:
                query = query.filter_by(semester=semester_int)
                semester = semester_int
            else:
                semester = 'all'
        except:
            semester = 'all'

    # إذا تم تحديد نوع ملف معين
    if file_type != 'all':
        query = query.filter_by(file_type=file_type)

    # إذا تم تحديد مستوى معين
    if level != 'all':
        query = query.filter_by(level=level)

    # ترتيب الملفات حسب تاريخ الرفع (الأحدث أولاً)
    files = query.order_by(EducationalFile.upload_date.desc()).all()

    # تحضير البيانات للرد JSON
    files_data = []
    for file in files:
        uploader = User.query.get(file.user_id) if file.user_id else None

        files_data.append({
            'id': file.id,
            'original_filename': file.original_filename,
            'level': file.level,
            'semester': file.semester,
            'file_type': file.file_type,
            'description': file.description or '',
            'file_size': file.file_size,
            'uploader_name': uploader.teacher_name if uploader else 'غير معروف',
            'upload_date': file.upload_date.strftime('%Y-%m-%d %H:%M'),
            'download_count': file.download_count,
            'can_delete': current_user.is_admin or (file.user_id and file.user_id == current_user.id)
        })

    # تحضير المعلومات النصية للعنوان
    level_text = level if level != 'all' else 'جميع المستويات'

    semester_text = 'جميع الفصول'
    if semester == 'all':
        semester_text = 'جميع الفصول'
    elif semester == 1:
        semester_text = 'الفصل الأول'
    elif semester == 2:
        semester_text = 'الفصل الثاني'
    elif semester == 3:
        semester_text = 'الفصل الثالث'

    app.logger.info(f'تم العثور على {len(files_data)} ملف')

    return jsonify({
        'files': files_data,
        'level': level,
        'semester': semester,
        'file_type': file_type,
        'level_text': level_text,
        'semester_text': semester_text
    })

@app.route('/update_schedule', methods=['POST'])
@login_required
def update_schedule():
    try:
        # تسجيل لرؤية نوع الطلب المستلم
        app.logger.info(f'تم استلام طلب تحديث الجدول: content_type={request.content_type}')

        # التحقق من نوع المحتوى المرسل (JSON أو نموذج)
        if request.is_json:
            # إذا كان طلب JSON (مثل طلب تفريغ الجدول)
            data = request.get_json()
            app.logger.info(f'بيانات JSON مستلمة: {data}')
            if 'schedule_data' in data:
                all_data = data['schedule_data']
            else:
                all_data = data
        else:
            # إذا كان نموذج عادي (نموذج حفظ الجدول)
            schedule_data = request.form.get('schedule-data', '{}')
            app.logger.info(f'بيانات نموذج مستلمة: schedule-data={schedule_data[:100]}...')
            all_data = json.loads(schedule_data)

        app.logger.info(f'تم استلام بيانات الجدول: {all_data}')

        # التعامل مع التنسيق القديم (مصفوفة بسيطة)
        if isinstance(all_data, list):
            schedule_items = all_data
            time_items = []
            app.logger.info('تم استخدام التنسيق القديم (مصفوفة بسيطة)')
        # التعامل مع التنسيق الجديد (كائن يحتوي على مصفوفتين)
        else:
            # استخراج بيانات الجدول والتوقيت من الكائن
            schedule_items = all_data.get('schedule', [])
            time_items = all_data.get('times', [])
            app.logger.info('تم استخدام التنسيق الجديد (كائن يحتوي على مصفوفتين)')

        app.logger.info(f'عناصر الجدول: {len(schedule_items)}, عناصر التوقيت: {len(time_items)}')
        app.logger.info(f'نموذج بيانات التوقيت: {time_items[:2] if time_items else "لا يوجد"}')

        # بدء المعاملة
        app.logger.info('بدء معاملة قاعدة البيانات')

        try:
            # معالجة بيانات الجدول
            # حذف بيانات الجدول الحالية للمستخدم
            deleted_schedules = Schedule.query.filter_by(user_id=current_user.id).delete()
            app.logger.info(f'تم حذف {deleted_schedules} من سجلات الجدول الحالية للمستخدم {current_user.id}')

            # إضافة سجلات الجدول الجديدة (فقط إذا كانت موجودة)
            added_count = 0
            if schedule_items:
                for item in schedule_items:
                    day = item.get('day')
                    hour = item.get('hour')
                    class_name = item.get('class_name')

                    if day is not None and hour is not None and class_name:
                        # إنشاء سجل جديد
                        schedule_item = Schedule(
                            user_id=current_user.id,
                            day=day,
                            hour=hour,
                            class_name=class_name
                        )
                        db.session.add(schedule_item)
                        added_count += 1

                db.session.flush()  # تطبيق التغييرات دون تنفيذ commit
                app.logger.info(f'تم إضافة {added_count} سجل من بيانات الجدول للمستخدم {current_user.id}')
            else:
                app.logger.info(f'لم يتم إضافة أي سجلات جدول للمستخدم {current_user.id} (جدول فارغ)')

            # التحقق مما إذا كان هناك بيانات توقيت للمعالجة
            app.logger.info(f'بدء معالجة عناصر التوقيت: {len(time_items)} عنصر')

            # معالجة بيانات التوقيت بشكل منفصل دائمًا، حتى لو كان الجدول فارغًا
            # حذف بيانات التوقيت الحالية
            deleted_times = ScheduleTimes.query.filter_by(user_id=current_user.id).delete()
            app.logger.info(f'تم حذف {deleted_times} من سجلات التوقيت الحالية للمستخدم {current_user.id}')

            # إضافة سجلات التوقيت الجديدة
            times_updated = 0
            if time_items:
                for i, item in enumerate(time_items):
                    original_time = item.get('original_time')
                    time_range = item.get('time_range')

                    app.logger.info(f'معالجة عنصر التوقيت {i+1}: original_time={original_time}, time_range={time_range}')

                    if original_time and time_range:
                        try:
                            # إنشاء سجل توقيت جديد
                            time_item = ScheduleTimes(
                                user_id=current_user.id,
                                original_time=original_time,
                                time_range=time_range
                            )
                            db.session.add(time_item)
                            times_updated += 1
                        except Exception as time_error:
                            app.logger.error(f'خطأ في إضافة عنصر التوقيت: {str(time_error)}')
                            app.logger.error(traceback.format_exc())
                    else:
                        app.logger.warning(f'تم تخطي عنصر التوقيت غير الصالح: {item}')

                app.logger.info(f'تم تجهيز {times_updated} من بيانات التوقيت للإضافة')
            else:
                app.logger.info('لا توجد بيانات توقيت للإضافة')

            # الالتزام بالتغييرات
            db.session.commit()
            app.logger.info('تم الالتزام بكافة التغييرات في قاعدة البيانات')

            # التحقق من عدد سجلات التوقيت المحفوظة
            saved_times_count = ScheduleTimes.query.filter_by(user_id=current_user.id).count()
            app.logger.info(f'عدد سجلات التوقيت المحفوظة في قاعدة البيانات: {saved_times_count}')

        except Exception as db_error:
            db.session.rollback()
            app.logger.error(f'خطأ في معالجة قاعدة البيانات: {str(db_error)}')
            app.logger.error(traceback.format_exc())
            return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء حفظ البيانات: {str(db_error)}'})

        # ترجيع رد مع البيانات المحدثة
        response_data = {
            'schedule': schedule_items,
            'times': time_items
        }

        return jsonify({
            'status': 'success',
            'message': 'تم تحديث جدول التوقيت بنجاح',
            'data': response_data
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f'خطأ في تحديث جدول التوقيت: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء تحديث الجدول: {str(e)}'})

@app.route('/get_schedule')
@login_required
def get_schedule():
    try:
        app.logger.info(f'بدء استرجاع بيانات الجدول للمستخدم {current_user.id}')

        # البحث عن سجلات جدول التوقيت للمستخدم الحالي
        schedule_items = Schedule.query.filter_by(user_id=current_user.id).all()
        app.logger.info(f'تم العثور على {len(schedule_items)} من عناصر الجدول للمستخدم {current_user.id}')

        # تحويل السجلات إلى قائمة
        schedule_data = []
        for item in schedule_items:
            schedule_data.append({
                'day': item.day,
                'hour': item.hour,
                'class_name': item.class_name
            })

        # استرجاع بيانات التوقيت المخصصة للمستخدم
        time_items = ScheduleTimes.query.filter_by(user_id=current_user.id).all()
        app.logger.info(f'تم العثور على {len(time_items)} من عناصر التوقيت للمستخدم {current_user.id}')

        # تحويل سجلات التوقيت إلى قائمة
        time_data = []
        for item in time_items:
            app.logger.info(f'معالجة سجل توقيت: id={item.id}, original_time={item.original_time}, time_range={item.time_range}')
            time_data.append({
                'original_time': item.original_time,
                'time_range': item.time_range
            })

        app.logger.info(f'تم استرجاع {len(schedule_data)} عنصر من جدول التوقيت و {len(time_data)} من بيانات التوقيت للمستخدم {current_user.id}')

        # تنسيق البيانات بالشكل المتوقع في الواجهة
        response_data = {
            'schedule': schedule_data,
            'times': time_data
        }

        app.logger.info(f'بيانات الاستجابة: schedule_count={len(schedule_data)}, times_count={len(time_data)}')

        return jsonify({
            'status': 'success',
            'message': 'تم استرجاع بيانات الجدول بنجاح',
            'data': response_data
        })

    except Exception as e:
        app.logger.error(f'خطأ في استرجاع بيانات جدول التوقيت: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء استرجاع بيانات الجدول: {str(e)}'})

@app.route('/check_schedule_db')
@login_required
def check_schedule_db():
    try:
        # البحث عن سجلات جدول التوقيت للمستخدم الحالي
        schedule_items = Schedule.query.filter_by(user_id=current_user.id).all()

        # تحويل السجلات إلى قائمة
        data = []
        for item in schedule_items:
            data.append({
                'id': item.id,
                'user_id': item.user_id,
                'day': item.day,
                'hour': item.hour,
                'class_name': item.class_name
            })

        app.logger.info(f'تم استرجاع {len(data)} عنصر من قاعدة البيانات للمستخدم {current_user.id} للفحص')

        # إضافة معلومات المستخدم لعرضها في الواجهة
        user_info = {
            'id': current_user.id,
            'name': current_user.teacher_name,
            'workplace': current_user.workplace
        }

        return jsonify({
            'status': 'success',
            'message': 'تم استرجاع بيانات الجدول بنجاح',
            'data': data,
            'user_info': user_info,
            'total_items': len(data)
        })

    except Exception as e:
        app.logger.error(f'خطأ في استرجاع بيانات جدول التوقيت للفحص: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء استرجاع بيانات الجدول: {str(e)}'})

@app.route('/update_default_profile_image', methods=['POST'])
@login_required
def update_default_profile_image():
    """تحديث الصورة الشخصية الافتراضية للمستخدم"""
    try:
        # طباعة البيانات المستلمة لأغراض التشخيص
        app.logger.info(f"البيانات المستلمة لتحديث الصورة الشخصية: {request.form}")

        user_id = request.form.get('user_id')
        image_type = request.form.get('image_type', 'default')

        app.logger.info(f"معرف المستخدم المستلم: {user_id}, نوع الصورة: {image_type}")

        if not user_id:
            user_id = current_user.id
        else:
            user_id = int(user_id)

        # التحقق من الصلاحيات: فقط المستخدم نفسه أو المدير يمكنه تعديل الصورة الشخصية
        if user_id != current_user.id and not current_user.is_admin:
            app.logger.warning(f'محاولة غير مصرح بها لتحديث الصورة الشخصية للمستخدم {user_id}')
            return jsonify({'success': False, 'message': 'غير مصرح لك بتحديث هذه الصورة الشخصية'}), 403

        user = User.query.get_or_404(user_id)

        # تحديد رابط الصورة الافتراضية بناءً على النوع المطلوب
        default_images = {
            'default': '/static/images/default-profile.png',
            'male': '/static/images/male-profile.png',
            'female': '/static/images/female-profile.png',
            'avatar1': '/static/images/avatar1.png',
            'avatar2': '/static/images/avatar2.png',
            'avatar3': '/static/images/avatar3.png',
            'avatar4': '/static/images/avatar4.png',
            'avatar5': '/static/images/avatar5.png'
        }

        # استخدام الصورة الافتراضية إذا كان النوع المطلوب موجودًا، وإلا استخدام الصورة
        profile_picture = default_images.get(image_type, default_images['default'])


        # تحديث الصورة الشخصية للمستخدم
        user.profile_picture = profile_picture

        # حفظ التغييرات
        db.session.commit()

        app.logger.info(f'تم تحديث الصورة الشخصية للمستخدم {user.id} إلى {profile_picture}')

        return jsonify({
            'success': True,
            'message': 'تم تحديث الصورة الشخصية بنجاح',
            'profile_picture': profile_picture
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f'خطأ في تحديث الصورة الشخصية: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'success': False, 'message': f'حدث خطأ أثناء تحديث الصورة الشخصية: {str(e)}'}), 500

@app.route('/change_password', methods=['POST'])
@login_required
def change_password():
    """تغيير كلمة المرور الخاصة بالمستخدم"""
    try:
        app.logger.info("محاولة تغيير كلمة المرور")

        # التحقق من وجود النموذج
        if not request.form:
            app.logger.warning("لم يتم استلام بيانات النموذج")
            return jsonify({'success': False, 'message': 'لم يتم استلام بيانات النموذج'}), 400

        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')

        # التحقق من توفر جميع الحقول المطلوبة
        if not current_password or not new_password:
            app.logger.warning("حقول مفقودة في طلب تغيير كلمة المرور")
            return jsonify({'success': False, 'message': 'جميع الحقول مطلوبة'}), 400

        app.logger.info(f"التحقق من كلمة المرور الحالية للمستخدم {current_user.id}")

        # التحقق من صحة كلمة المرور الحالية
        if not current_user.check_password(current_password):
            app.logger.warning(f"كلمة المرور الحالية غير صحيحة للمستخدم {current_user.id}")
            return jsonify({'success': False, 'message': 'كلمة المرور الحالية غير صحيحة'}), 400

        # تعيين كلمة المرور الجديدة
        current_user.set_password(new_password)
        db.session.commit()

        app.logger.info(f"تم تغيير كلمة المرور بنجاح للمستخدم {current_user.id}")
        return jsonify({'success': True, 'message': 'تم تغيير كلمة المرور بنجاح'})

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"خطأ في تغيير كلمة المرور: {str(e)}")
        app.logger.error(traceback.format_exc())
        return jsonify({'success': False, 'message': f'حدث خطأ أثناء تغيير كلمة المرور: {str(e)}'}), 500

@app.route('/add_supervisor/<int:user_id>', methods=['POST'])
def add_supervisor(user_id):
    # التحقق من أن المستخدم الحالي هو المدير الرئيسي (وليس مجرد مشرف)
    if not current_user.is_authenticated or not current_user.is_admin or current_user.admin_type != "مدير":
        return jsonify({'status': 'error', 'message': 'غير مسموح لك بإضافة مشرفين. هذه الصلاحية متاحة فقط للمدير الرئيسي.'}), 403

    try:
        # التحقق من البيانات المدخلة
        request_data = request.get_json()
        admin_type = request_data.get('admin_type')
        if not admin_type or admin_type not in ["مشرف أول", "مشرف ثاني"]:
            return jsonify({'status': 'error', 'message': 'نوع الإشراف غير صحيح'}), 400

        # التحقق من المستخدم المراد إضافته كمشرف
        user = User.query.get(user_id)
        if not user:
            return jsonify({'status': 'error', 'message': 'لم يتم العثور على المستخدم'}), 404

        app.logger.info(f"معلومات المستخدم قبل التعديل: user_id={user.id}, name={user.teacher_name}, is_admin={user.is_admin}, admin_type={user.admin_type}")

        # التحقق من أن المستخدم ليس مشرفًا بالفعل
        if user.is_admin:
            return jsonify({'status': 'error', 'message': 'هذا المستخدم مشرف بالفعل'}), 400

        # إضافة المستخدم كمشرف
        user.is_admin = True  # تفعيل صلاحيات الإدارة
        user.admin_type = admin_type  # تعيين نوع الإشراف
        db.session.commit()

        app.logger.info(f"تم إضافة المستخدم {user.teacher_name} كمشرف. حالة is_admin={user.is_admin}, admin_type={user.admin_type}")

        return jsonify({'status': 'success', 'message': f'تم إضافة {user.teacher_name} كـ {admin_type} بنجاح'})
    except Exception as e:
        db.session.rollback()
        error_message = str(e)
        app.logger.error(f"حدث خطأ أثناء إضافة المشرف: {error_message}")
        return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء إضافة المشرف: {error_message}'}), 500


@app.route('/remove_supervisor/<int:user_id>', methods=['POST'])
def remove_supervisor(user_id):
    # التحقق من أن المستخدم الحالي هو المدير الرئيسي (وليس مجرد مشرف)
    if not current_user.is_authenticated or not current_user.is_admin or current_user.admin_type != "مدير":
        return jsonify({'status': 'error', 'message': 'غير مسموح لك بإزالة الصلاحيات الإشرافية. هذه الصلاحية متاحة فقط للمدير الرئيسي.'}), 403

    try:
        # التحقق من المستخدم المراد إزالة صلاحياته
        user = User.query.get(user_id)
        if not user:
            return jsonify({'status': 'error', 'message': 'لم يتم العثور على المستخدم'}), 404

        app.logger.info(f"معلومات المستخدم قبل إزالة الصلاحيات: user_id={user.id}, name={user.teacher_name}, is_admin={user.is_admin}, admin_type={user.admin_type}")

        # التحقق أن المستخدم ليس المدير نفسه
        if user.admin_type == "مدير":
            return jsonify({'status': 'error', 'message': 'لا يمكن إزالة صلاحيات المدير الرئيسي'}), 403

        # التحقق من أن المستخدم هو مشرف (له صلاحيات إدارية)
        if not user.is_admin:
            return jsonify({'status': 'error', 'message': 'هذا المستخدم ليس مشرفًا بالفعل'}), 400

        # إزالة الصلاحيات الإشرافية
        user.admin_type = None
        user.is_admin = False  # إزالة صلاحية الإدارة أيضًا
        db.session.commit()

        app.logger.info(f"تم إزالة صلاحيات المشرف للمستخدم {user.teacher_name}, حالة is_admin الآن: {user.is_admin}, وحالة admin_type الآن: {user.admin_type}")

        # إعادة بيانات المستخدم الكاملة مع الاستجابة
        user_data = {
            'id': user.id,
            'teacher_name': user.teacher_name,
            'email': user.email,
            'workplace': user.workplace,
            'phone_number': user.phone_number,
            'position': user.position,
        }

        return jsonify({
            'status': 'success',
            'message': 'تم إزالة الصلاحيات الإشرافية بنجاح',
            'user_data': user_data
        })
    except Exception as e:
        db.session.rollback()
        error_message = str(e)
        app.logger.error(f"حدث خطأ أثناء إزالة صلاحيات المشرف: {error_message}")
        return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء إزالة صلاحيات المشرف: {error_message}'}), 500


@app.route('/change_supervisor_type/<int:user_id>', methods=['POST'])
def change_supervisor_type(user_id):
    # التحقق من أن المستخدم الحالي هو المدير الرئيسي (وليس مجرد مشرف)
    if not current_user.is_authenticated or not current_user.is_admin or current_user.admin_type != "مدير":
        return jsonify({'status': 'error', 'message': 'غير مسموح لك بتغيير نوع الإشراف. هذه الصلاحية متاحة فقط للمدير الرئيسي.'}), 403

    try:
        # التحقق من البيانات المدخلة
        request_data = request.get_json()
        admin_type = request_data.get('admin_type')
        if not admin_type or admin_type not in ["مشرف أول", "مشرف ثاني"]:
            return jsonify({'status': 'error', 'message': 'نوع الإشراف غير صحيح'}), 400

        # التحقق من المستخدم المراد تغيير نوع إشرافه
        user = User.query.get(user_id)
        if not user:
            return jsonify({'status': 'error', 'message': 'لم يتم العثور على المستخدم'}), 404

        app.logger.info(f"معلومات المستخدم قبل تغيير نوع الإشراف: user_id={user.id}, name={user.teacher_name}, is_admin={user.is_admin}, admin_type={user.admin_type}")

        # التحقق أن المستخدم ليس المدير نفسه
        if user.admin_type == "مدير":
            return jsonify({'status': 'error', 'message': 'لا يمكن تغيير نوع إشراف المدير الرئيسي'}), 403

        # التحقق من أن المستخدم هو مشرف بالفعل
        if not user.is_admin:
            return jsonify({'status': 'error', 'message': 'هذا المستخدم ليس مشرفًا، قم بإضافته كمشرف أولاً'}), 400

        # تغيير نوع الإشراف
        user.admin_type = admin_type
        db.session.commit()

        app.logger.info(f"تم تغيير نوع الإشراف للمستخدم {user.teacher_name} إلى {admin_type}, حالة is_admin: {user.is_admin}, وحالة admin_type: {user.admin_type}")

        return jsonify({'status': 'success', 'message': f'تم تغيير نوع الإشراف إلى {admin_type} بنجاح'})
    except Exception as e:
        db.session.rollback()
        error_message = str(e)
        app.logger.error(f"حدث خطأ أثناء تغيير نوع الإشراف: {error_message}")
        return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء تغيير نوع الإشراف: {error_message}'}), 500

def upload_additional_file_async(file, message_id, is_pdf=False):
    """رفع الملفات الإضافية إلى Cloudinary بشكل غير متزامن مع دعم ملفات PDF"""
    try:
        # تحديد نوع المورد بناءً على امتداد الملف
        resource_type = get_resource_type(file.filename)

        # التأكد من أن الملف مسموح به للرسائل
        if not allowed_file(file.filename, for_messages=True):
            logger.warning(f"Additional file type not allowed: {file.filename}")
            return False

        # التحقق من حجم الملف
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)
        if file_size > MAX_FILE_SIZE:
            logger.warning(f"Additional file size exceeds limit: {file.filename} (size: {file_size})")
            return False

        with app.app_context():
            message = Message.query.get(message_id)
            if not message:
                logger.error(f"Message {message_id} not found in upload_additional_file_async")
                return

            # الحصول على معلومات المرسل
            sender = User.query.get(message.sender_id)
            if not sender:
                logger.error(f"Sender {message.sender_id} not found in upload_additional_file_async")
                return

            # حفظ الملف مؤقتاً
            secure_name = secure_filename(file.filename)
            temp_dir = app.config.get('TEMP_FOLDER', 'tmp')

            # التأكد من وجود مجلد المؤقت
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)

            temp_file_path = os.path.join(temp_dir, secure_name)
            logger.info(f'حفظ الملف مؤقتاً في: {temp_file_path}')
            file.save(temp_file_path)

            # إنشاء اسم مجلد خاص بالمستخدم - استخدام teacher_name بدون بادئة
            safe_folder_name = ''.join(e for e in sender.teacher_name if e.isalnum())

            # إضافة رقم عشوائي للملف للتأكد من إنشاء نسخة جديدة حتى لو نفس الملف
            random_suffix = secrets.token_hex(4)  # يولد سلسلة عشوائية من 8 أحرف

            # استخراج اسم الملف والامتداد
            filename, file_ext = os.path.splitext(file.filename)

            # إنشاء معرف داخلي فريد مع اسم الملف والامتداد
            internal_id = f"{filename}_{random_suffix}{file_ext}"

            # إنشاء مسار Cloudinary باستخدام المجلد واسم الملف الأصلي مع إضافة الرقم العشوائي في آخر المجلد
            # هذا يضمن عدم تكرار الملفات مع الاحتفاظ باسم ملف نظيف عند العرض
            public_path = f"attachments/{safe_folder_name}_{random_suffix}/async/{filename}{file_ext}"

            # تعيين نوع المورد المناسب بناءً على نوع الملف
            if is_pdf:
                # نستخدم نوع المورد image بدلاً من raw
                upload_resource_type = "image"
                logger.info(f'تحويل PDF إلى JPG: {filename}, نوع المورد: {upload_resource_type}')
            else:
                upload_resource_type = resource_type
                logger.info(f'استخدام الامتداد الأصلي: {filename}, نوع المورد: {upload_resource_type}')

            upload_options = {
                'resource_type': upload_resource_type,
                'public_id': public_path,  # استخدام المسار الكامل مع اسم الملف الأصلي فقط
                'use_filename': True,
                'format': "jpg" if is_pdf else None,  # تحديد امتداد jpg لملفات PDF
                'unique_filename': False,  # نتحكم في التسمية يدويًا
                'overwrite': True  # السماح بالكتابة فوق ملف موجود
            }

            logger.info(f"Attempting to upload additional file {file.filename} to Cloudinary for message {message_id}")
            logger.debug(f"Upload options: {upload_options}")

            try:
                result = cloudinary.uploader.upload(temp_file_path, **upload_options)
                logger.info(f"Successfully uploaded additional file to Cloudinary: {result.get('public_id')}")

                # ملاحظة: هنا لا نقوم بتحديث المرفق الأساسي للرسالة لأنه تم تعيينه بالفعل
                # يمكن إنشاء جدول منفصل لمرفقات متعددة في المستقبل

                # مسح الملف المؤقت بعد الرفع
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)

                logger.info(f"Successfully uploaded additional file for message {message_id}")

            except Exception as e:
                logger.error(f"Error uploading additional file to Cloudinary: {str(e)}")
                logger.error(traceback.format_exc())

                # مسح الملف المؤقت في حالة الخطأ
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)

        # إنهاء المهمة بنجاح
        return True

    except Exception as e:
        logger.error(f"Error in upload_additional_file_async: {str(e)}")
        logger.error(traceback.format_exc())
        return False

@app.route('/profile/update_password', methods=['POST'])
@login_required
def update_password_direct():
    """تحديث كلمة المرور مباشرة دون التحقق من كلمة المرور الحالية"""
    try:
        app.logger.info("محاولة تحديث كلمة المرور المباشر")

        # استخراج البيانات من الطلب JSON
        data = request.get_json()

        if not data:
            app.logger.warning("لم يتم استلام بيانات JSON")
            return jsonify({'success': False, 'message': 'لم يتم استلام بيانات صالحة'}), 400

        new_password = data.get('new_password')

        # التحقق من وجود كلمة المرور الجديدة
        if not new_password:
            app.logger.warning("كلمة المرور الجديدة مفقودة")
            return jsonify({'success': False, 'message': 'كلمة المرور الجديدة مطلوبة'}), 400

        # تعيين كلمة المرور الجديدة مباشرة
        current_user.set_password(new_password)
        db.session.commit()

        app.logger.info(f"تم تحديث كلمة المرور بنجاح للمستخدم {current_user.id}")
        return jsonify({'success': True, 'message': 'تم تحديث كلمة المرور بنجاح'})

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"خطأ في تحديث كلمة المرور: {str(e)}")
        app.logger.error(traceback.format_exc())
        return jsonify({'success': False, 'message': f'حدث خطأ أثناء تحديث كلمة المرور: {str(e)}'}), 500

# جدول لتخزين بيانات التوقيت المخصصة
class ScheduleTimes(db.Model):
    """
    نموذج جدول توقيت الحصص المخصص
    يستخدم هذا الجدول لتخزين أوقات الحصص المعدلة من قبل المستخدم
    لكل وقت أصلي، يمكن تخزين نطاق زمني مخصص
    """
    # المفتاح الرئيسي
    id = db.Column(db.Integer, primary_key=True)
    # معرف المستخدم (المعلم) الذي ينتمي إليه هذا التوقيت
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    # الوقت الأصلي (مثل "08:00") - يستخدم كمعرف للخلية في واجهة المستخدم
    original_time = db.Column(db.String(10), nullable=False)
    # نطاق الوقت المخصص (مثل "08:00 - 08:45")
    time_range = db.Column(db.String(20), nullable=False)

    # العلاقة مع المستخدم - إضافة cascade لحذف تلقائي للتوقيتات عند حذف المستخدم
    teacher = db.relationship('User', backref=db.backref('schedule_times', cascade='all, delete-orphan'))

    # تم تعطيل القيد الفريد مؤقتًا لتسهيل عمليات الحفظ وتجنب أخطاء التكرار
    # في المستقبل، يمكن إعادة تفعيله إذا لزم الأمر
    # __table_args__ = (db.UniqueConstraint('user_id', 'original_time', name='unique_time_range'),)

# نموذج للتدرجات السنوية
class YearlyProgress(db.Model):
    """
    نموذج التدرجات السنوية
    يستخدم لتخزين بيانات التدرج السنوي لكل سنة دراسية
    """
    id = db.Column(db.Integer, primary_key=True)
    # السنة الدراسية (1=السنة الأولى، 2=السنة الثانية، إلخ)
    year = db.Column(db.Integer, nullable=False)
    # رقم الأسبوع
    week = db.Column(db.Integer, nullable=False)
    # الشهر
    month = db.Column(db.String(20), nullable=False)
    # محتوى الحصة الأولى
    first_lesson = db.Column(db.Text, nullable=True)
    # محتوى الحصة الثانية
    second_lesson = db.Column(db.Text, nullable=True)
    # حالة دمج الخلايا
    is_merged = db.Column(db.Boolean, default=False)
    # تاريخ آخر تحديث
    last_updated = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# نموذج لتتبع تقدم البرنامج التعليمي
class Progress(db.Model):
    """
    نموذج تقدم البرنامج
    يستخدم لتتبع آخر حصة تعليمية تم تدريسها لكل مستوى
    """
    id = db.Column(db.Integer, primary_key=True)
    # المستوى الدراسي (السنة الأولى متوسط، السنة الثانية متوسط، إلخ)
    level = db.Column(db.String(50), nullable=False)
    # الشهر
    month = db.Column(db.String(20), nullable=False)
    # رقم الأسبوع
    week = db.Column(db.Integer, nullable=False)
    # عنوان الحصة
    lesson_title = db.Column(db.Text, nullable=False)
    # رقم معرف التدرج السنوي المرتبط
    yearly_progress_id = db.Column(db.Integer, nullable=False)
    # نوع الحصة (first=الأولى، second=الثانية، merged=مدمجة)
    lesson_type = db.Column(db.String(10), nullable=False)
    # حالة الدمج
    is_merged = db.Column(db.Boolean, default=False)
    # معرف المستخدم الذي أضاف السجل
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    # تاريخ الإضافة
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    # تاريخ آخر تحديث
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقة مع المستخدم
    user = db.relationship('User', backref=db.backref('progress_items', cascade='all, delete-orphan'))

# التأكد من إنشاء جدول ScheduleTimes عند بدء التطبيق
with app.app_context():
    try:
        inspector = inspect(db.engine)
        # التحقق من وجود جدول ScheduleTimes
        if 'schedule_times' in inspector.get_table_names():
            # حذف الجدول القديم لتطبيق التغييرات في الهيكل
            app.logger.info("حذف جدول ScheduleTimes القديم وإعادة إنشائه")
            db.session.execute(text("DROP TABLE schedule_times"))
            db.session.commit()
            app.logger.info("تم حذف جدول ScheduleTimes بنجاح")

        # إنشاء الجدول بالهيكل الجديد
        app.logger.info("جاري إنشاء جدول ScheduleTimes")
        db.create_all()
        app.logger.info("تم إنشاء جدول ScheduleTimes بنجاح")
    except Exception as e:
        app.logger.error(f"خطأ أثناء التحقق من جدول ScheduleTimes: {str(e)}")
        app.logger.error(traceback.format_exc())

# مسار تحديث أقسام الملف الشخصي
@app.route('/update_section/<section>', methods=['POST'])
@login_required
def update_section(section):
    """تحديث قسم محدد من بيانات الملف الشخصي"""
    try:
        app.logger.info(f"محاولة تحديث قسم {section} من الملف الشخصي للمستخدم {current_user.id}")
        app.logger.info(f"البيانات المرسلة: {request.form}")

        # استلام معرف المستخدم من النموذج أو استخدام المستخدم الحالي
        user_id = request.form.get('user_id')
        if user_id:
            user_id = int(user_id)
        else:
            user_id = current_user.id

        app.logger.info(f"معرف المستخدم المستلم: {user_id}")

        # التحقق من الصلاحيات: فقط المستخدم نفسه أو المدير يمكنه تعديل الملف الشخصي
        if user_id != current_user.id and not current_user.is_admin:
            app.logger.warning(f'محاولة غير مصرح بها لتحديث الملف الشخصي للمستخدم {user_id}')
            return jsonify({'success': False, 'message': 'غير مصرح لك بتحديث هذا الملف الشخصي'}), 403

        # الحصول على المستخدم المطلوب تحديث بياناته
        user = User.query.get_or_404(user_id)
        app.logger.info(f"تم العثور على المستخدم: {user.teacher_name}")

        # تحديث البيانات حسب القسم
        if section == 'personal':
            # تحديث المعلومات الشخصية
            if 'email' in request.form:
                user.email = request.form.get('email')

            if 'phone_number' in request.form:
                user.phone_number = request.form.get('phone_number')

            if 'workplace' in request.form:
                user.workplace = request.form.get('workplace')

            # التعامل مع تاريخ الميلاد
            if 'birth_date' in request.form:
                birth_date = request.form.get('birth_date')
                if birth_date and birth_date.strip():
                    try:
                        user.birth_date = datetime.strptime(birth_date, '%Y-%m-%d').date()
                    except ValueError as e:
                        app.logger.error(f"خطأ في تنسيق تاريخ الميلاد: {e}")
                        return jsonify({'success': False, 'message': f'تنسيق تاريخ الميلاد غير صحيح: {birth_date}'}), 400
                else:
                    user.birth_date = None

            # التعامل مع الحالة العائلية
            if 'marital_status' in request.form:
                marital_status = request.form.get('marital_status')
                app.logger.info(f"الحالة العائلية المرسلة: {marital_status}")

                if marital_status == 'none':
                    user.marital_status = None
                elif marital_status == '1':
                    user.marital_status = True
                elif marital_status == '0':
                    user.marital_status = False

            if 'teacher_name' in request.form:
                user.teacher_name = request.form.get('teacher_name')

            # تحديث كلمة المرور إذا تم تقديمها
            if 'password' in request.form and request.form.get('password'):
                # تأكد من أن كلمة المرور ليست النجوم الافتراضية
                password = request.form.get('password')
                if password != '••••••••':
                    # تأكد من أن كلمة المرور وتأكيدها متطابقان
                    if 'password_confirm' in request.form and password == request.form.get('password_confirm'):
                        user.set_password(password)
                    else:
                        return jsonify({'success': False, 'message': 'كلمة المرور وتأكيدها غير متطابقين'}), 400

            app.logger.info(f"تم تحديث المعلومات الشخصية للمستخدم {user.id}")

        elif section == 'admin':
            # تحديث معلومات المهنة والصنف والدرجة
            if 'position' in request.form:
                user.position = request.form.get('position')

            # التعامل مع الصنف
            if 'category' in request.form:
                category = request.form.get('category')
                try:
                    if category and category.strip():
                        user.category = int(category)
                    else:
                        user.category = None
                except (ValueError, TypeError) as e:
                    app.logger.error(f"خطأ في تحويل الصنف: {e}")
                    user.category = None

            # التعامل مع الدرجة
            if 'grade' in request.form:
                grade = request.form.get('grade')
                try:
                    if grade and grade.strip():
                        user.grade = int(grade)
                    else:
                        user.grade = None
                except (ValueError, TypeError) as e:
                    app.logger.error(f"خطأ في تحويل الدرجة: {e}")
                    user.grade = None

            # التعامل مع سنوات الخبرة
            if 'years_of_experience' in request.form:
                years = request.form.get('years_of_experience')
                try:
                    if years and years.strip():
                        user.years_of_experience = int(years)
                    else:
                        user.years_of_experience = None
                except (ValueError, TypeError) as e:
                    app.logger.error(f"خطأ في تحويل سنوات الخبرة: {e}")
                    user.years_of_experience = None

            # التعامل مع تاريخ بدء العمل
            if 'work_start_date' in request.form:
                work_date = request.form.get('work_start_date')
                if work_date and work_date.strip():
                    try:
                        user.work_start_date = datetime.strptime(work_date, '%Y-%m-%d').date()
                    except ValueError as e:
                        app.logger.error(f"خطأ في تنسيق تاريخ بداية العمل: {e}")
                        return jsonify({'success': False, 'message': f'تنسيق تاريخ بداية العمل غير صحيح: {work_date}'}), 400
                else:
                    user.work_start_date = None

            app.logger.info(f"تم تحديث معلومات الوظيفة والرتبة للمستخدم {user.id}")

        elif section == 'academic':
            # تحديث المعلومات الأكاديمية
            if 'specialization' in request.form:
                user.specialization = request.form.get('specialization')

            # هناك تكرار مع قسم admin - نتحقق أولاً
            if 'position' in request.form and section != 'admin':
                user.position = request.form.get('position')

            if 'years_of_experience' in request.form and not hasattr(user, 'experience_years'):
                try:
                    years = request.form.get('years_of_experience')
                    user.years_of_experience = int(years) if years and years.strip() else None
                except ValueError:
                    user.years_of_experience = None

            if 'education_level' in request.form:
                user.education_level = request.form.get('education_level')

            app.logger.info(f"تم تحديث المعلومات الأكاديمية للمستخدم {user.id}")

        elif section == 'additional':
            # تحديث المعلومات الإضافية
            if 'bio' in request.form:
                user.bio = request.form.get('bio')

            app.logger.info(f"تم تحديث المعلومات الإضافية للمستخدم {user.id}")

        elif section == 'bio':
            # تحديث السيرة الذاتية
            if 'bio' in request.form:
                user.bio = request.form.get('bio')

            app.logger.info(f"تم تحديث السيرة الذاتية للمستخدم {user.id}")

        else:
            app.logger.warning(f"القسم غير معروف: {section}")
            return jsonify({'success': False, 'message': f'القسم {section} غير معروف'}), 400

        # حفظ التغييرات
        db.session.commit()
        app.logger.info(f"تم حفظ التغييرات في قاعدة البيانات للمستخدم {user.id}")

        # إعداد البيانات للرد
        response_data = {
            'id': user.id,
            'teacher_name': user.teacher_name,
            'email': user.email,
            'phone_number': user.phone_number,
            'workplace': user.workplace,
            'marital_status': user.marital_status,
            'position': user.position,
            'category': user.category,
            'grade': user.grade,
            'years_of_experience': user.years_of_experience,
            'specialization': user.specialization,
            'bio': user.bio
        }

        # إضافة البيانات التي تحتاج لمعالجة خاصة
        if user.birth_date:
            response_data['birth_date'] = user.birth_date.isoformat()
        else:
            response_data['birth_date'] = None

        if user.work_start_date:
            response_data['work_start_date'] = user.work_start_date.isoformat()
        else:
            response_data['work_start_date'] = None

        return jsonify({
            'success': True,
            'message': 'تم تحديث البيانات بنجاح',
            'user_data': response_data
        })

    except Exception as e:
        db.session.rollback()
        error_message = str(e)
        app.logger.error(f"حدث خطأ أثناء تحديث قسم {section} من الملف الشخصي: {error_message}")
        app.logger.error(traceback.format_exc())
        return jsonify({'success': False, 'message': f'حدث خطأ أثناء تحديث البيانات: {error_message}'}), 500

@app.route('/save-yearly-progress', methods=['POST'])
@login_required
def save_yearly_progress():
    """
    حفظ بيانات التدرج السنوي
    يتلقى بيانات JSON تحتوي على:
    - year: السنة الدراسية (1-4)
    - data: قائمة بالبيانات لكل أسبوع
    - tableStructure: معلومات هيكل الجدول (الأسابيع المحذوفة وأقصى رقم أسبوع)
    """
    # التحقق من الصلاحيات
    if not current_user.is_admin:
        return jsonify({"status": "error", "message": "غير مصرح لك بتنفيذ هذه العملية"}), 403

    try:
        data = request.json
        year = data.get('year')
        progress_data = data.get('data')
        table_structure = data.get('tableStructure', {})

        if not year or not progress_data:
            return jsonify({"status": "error", "message": "بيانات غير كاملة"}), 400

        # ترتيب الأشهر للتأكد من الترقيم الصحيح
        month_order = [
            'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر',
            'جانفي', 'فيفري', 'مارس', 'أفريل', 'ماي'
        ]

        # ترتيب البيانات حسب الشهر ثم رقم الأسبوع
        progress_data.sort(key=lambda x: (month_order.index(x['month']) if x['month'] in month_order else 999, x['week']))

        # حذف البيانات القديمة للسنة المحددة
        YearlyProgress.query.filter_by(year=year).delete()

        # إضافة البيانات الجديدة
        current_week = 1
        for item in progress_data:
            # حفظ البيانات مع تحديث رقم الأسبوع للتأكد من التسلسل الصحيح
            new_progress = YearlyProgress(
                year=year,
                week=current_week,  # استخدام رقم الأسبوع المتسلسل
                month=item.get('month'),
                first_lesson=item.get('firstLesson'),
                second_lesson=item.get('secondLesson'),
                is_merged=item.get('isMerged', False)
            )
            db.session.add(new_progress)
            current_week += 1

        # حفظ معلومات هيكل الجدول إذا وجدت
        if table_structure:
            # يمكن إضافة أي منطق إضافي هنا للتعامل مع هيكل الجدول
            app.logger.info(f"Table structure saved: {table_structure}")

        # حفظ التغييرات في قاعدة البيانات
        db.session.commit()

        return jsonify({"status": "success", "message": "تم حفظ التدرجات السنوية بنجاح"})

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error saving yearly progress: {str(e)}")
        return jsonify({"status": "error", "message": f"حدث خطأ أثناء حفظ البيانات: {str(e)}"}), 500

@app.route('/get-yearly-progress/<int:year>', methods=['GET'])
@login_required
def get_yearly_progress(year):
    """
    استرجاع بيانات التدرج السنوي للسنة المحددة
    """
    try:
        # استرجاع البيانات المطلوبة
        progress_items = YearlyProgress.query.filter_by(year=year).order_by(YearlyProgress.week).all()

        # ترتيب الأشهر
        month_order = [
            'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر',
            'جانفي', 'فيفري', 'مارس', 'أفريل', 'ماي'
        ]

        # تحويل البيانات إلى تنسيق JSON
        result = []
        for item in progress_items:
            result.append({
                "week": item.week,
                "month": item.month,
                "firstLesson": item.first_lesson,
                "secondLesson": item.second_lesson,
                "isMerged": item.is_merged
            })

        # ترتيب النتائج حسب الشهر ثم رقم الأسبوع للتأكد من العرض الصحيح
        result.sort(key=lambda x: (month_order.index(x['month']) if x['month'] in month_order else 999, x['week']))

        return jsonify({"status": "success", "data": result})

    except Exception as e:
        app.logger.error(f"Error fetching yearly progress: {str(e)}")
        return jsonify({"status": "error", "message": f"حدث خطأ أثناء استرجاع البيانات: {str(e)}"}), 500

# صفحة التقدم في البرنامج
@app.route('/progress')
@login_required
def progress_page():
    """عرض صفحة التقدم في البرنامج للمستخدم الحالي"""
    return progress_page_for_user(current_user.id)

@app.route('/progress/<int:user_id>')
@login_required
def progress_page_for_user(user_id):
    """عرض صفحة التقدم في البرنامج لمستخدم محدد"""
    # التحقق من الصلاحيات: يُسمح فقط للمستخدم نفسه أو المشرفين بعرض صفحة التقدم
    if user_id != current_user.id and not current_user.is_admin:
        flash('danger|غير مصرح لك بعرض هذه الصفحة')
        return redirect(url_for('dashboard'))

    # استرجاع المستخدم المطلوب
    user = User.query.get_or_404(user_id)

    # استرجاع سجلات التقدم الخاصة بالمستخدم المحدد مرتبة بتاريخ الإضافة (الأحدث أولاً)
    progress_items = Progress.query.filter_by(user_id=user_id).order_by(Progress.created_at.desc()).all()

    # استرجاع قيمة september_weeks من الإعدادات
    try:
        # البحث عن إعداد september_weeks
        september_weeks_setting = Setting.query.filter_by(key='september_weeks').first()
        september_weeks = int(september_weeks_setting.value) if september_weeks_setting else 2
        app.logger.info(f"استرجاع قيمة september_weeks: {september_weeks}")
    except Exception as e:
        app.logger.error(f"خطأ في استرجاع قيمة september_weeks: {str(e)}")
        september_weeks = 2  # القيمة الافتراضية إذا حدث خطأ

    # الحصول على قائمة المعلمين (للمشرفين فقط)
    teachers = None
    if current_user.is_admin:
        teachers = User.query.filter(User.id != current_user.id).all()

    return render_template('progress.html',
                           progress_items=progress_items,
                           september_weeks=september_weeks,
                           target_user=user,
                           teachers=teachers)

# استرجاع سجلات التقدم
@app.route('/get-progress')
@login_required
def get_progress():
    """استرجاع سجلات التقدم في البرنامج للمستخدم الحالي"""
    return get_progress_for_user(current_user.id)

@app.route('/get-progress/<int:user_id>')
@login_required
def get_progress_for_user(user_id):
    """استرجاع سجلات التقدم في البرنامج لمستخدم محدد"""
    try:
        # التحقق من الصلاحيات: يُسمح فقط للمستخدم نفسه أو المشرفين بعرض المعلومات
        if user_id != current_user.id and not current_user.is_admin:
            return jsonify({"status": "error", "message": "غير مصرح لك بعرض هذه المعلومات"}), 403

        # استرجاع سجلات التقدم الخاصة بالمستخدم المحدد مرتبة بتاريخ الإضافة (الأحدث أولاً)
        progress_items = Progress.query.filter_by(user_id=user_id).order_by(Progress.created_at.desc()).all()

        # الحصول على معلومات المستخدم
        user = User.query.get_or_404(user_id)

        # تحويل البيانات إلى تنسيق JSON
        result = []
        for item in progress_items:
            result.append({
                "id": item.id,
                "level": item.level,
                "month": item.month,
                "week": item.week,
                "lesson_title": item.lesson_title,
                "yearly_progress_id": item.yearly_progress_id,
                "lesson_type": item.lesson_type,
                "is_merged": item.is_merged,
                "user_id": item.user_id,
                "created_at": item.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                "updated_at": item.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                "teacher_name": user.teacher_name
            })

        return jsonify({
            "status": "success",
            "progress": result,
            "user": {
                "id": user.id,
                "teacher_name": user.teacher_name
            }
        })

    except Exception as e:
        app.logger.error(f"Error fetching progress: {str(e)}")
        return jsonify({"status": "error", "message": f"حدث خطأ أثناء استرجاع البيانات: {str(e)}"}), 500

# حفظ سجل تقدم جديد أو تحديث سجل موجود
@app.route('/save-progress', methods=['POST'])
@login_required
def save_progress():
    """حفظ سجل تقدم جديد أو تحديث سجل موجود"""
    try:
        # التحقق من وجود معرف سجل (للتعديل)
        progress_id = request.form.get('progress_id')

        if progress_id:
            # تحديث سجل موجود
            progress = Progress.query.get_or_404(progress_id)

            # التحقق من صلاحية التعديل (المستخدم الحالي هو من أضاف السجل أو مشرف)
            if progress.user_id != current_user.id and not current_user.is_admin:
                return jsonify({"status": "error", "message": "غير مصرح لك بتعديل هذا السجل"}), 403
        else:
            # إنشاء سجل جديد
            # تحديد المستخدم: إذا كان مشرفًا ويوجد معرف مستخدم في الطلب، استخدم هذا المعرف
            user_id = request.form.get('user_id')

            if current_user.is_admin and user_id:
                # التحقق من وجود المستخدم
                user = User.query.get(user_id)
                if not user:
                    return jsonify({"status": "error", "message": "المستخدم المحدد غير موجود"}), 404

                # الحصول على المستوى من النموذج
                level = request.form.get('level')

                # حذف السجلات السابقة للمستوى نفسه
                existing_progress = Progress.query.filter_by(user_id=user_id, level=level).all()
                for existing in existing_progress:
                    db.session.delete(existing)

                # إنشاء سجل للمستخدم المحدد
                progress = Progress(
                    user_id=user_id
                )
            else:
                # الحصول على المستوى من النموذج
                level = request.form.get('level')

                # حذف السجلات السابقة للمستوى نفسه
                existing_progress = Progress.query.filter_by(user_id=current_user.id, level=level).all()
                for existing in existing_progress:
                    db.session.delete(existing)

                # إنشاء سجل للمستخدم الحالي
                progress = Progress(
                    user_id=current_user.id
                )

        # تحديث البيانات من النموذج
        progress.level = request.form.get('level')
        progress.month = request.form.get('month')
        progress.week = int(request.form.get('week'))
        progress.lesson_title = request.form.get('lesson_title')
        progress.yearly_progress_id = int(request.form.get('yearly_progress_id'))
        progress.lesson_type = request.form.get('lesson_type')
        progress.is_merged = request.form.get('is_merged') == 'true'

        # حفظ البيانات
        if not progress_id:
            db.session.add(progress)
        db.session.commit()

        return jsonify({"status": "success", "message": "تم حفظ سجل التقدم بنجاح", "progress_id": progress.id})

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error saving progress: {str(e)}")
        return jsonify({"status": "error", "message": f"حدث خطأ أثناء حفظ البيانات: {str(e)}"}), 500

# حذف سجل تقدم
@app.route('/delete-progress/<int:progress_id>', methods=['POST'])
@login_required
def delete_progress(progress_id):
    """حذف سجل تقدم محدد"""
    try:
        # استرجاع السجل المطلوب حذفه
        progress = Progress.query.get_or_404(progress_id)

        # التحقق من صلاحية الحذف (المستخدم الحالي هو من أضاف السجل أو مشرف)
        if progress.user_id != current_user.id and not current_user.is_admin:
            return jsonify({"status": "error", "message": "غير مصرح لك بحذف هذا السجل"}), 403

        # حذف السجل
        db.session.delete(progress)
        db.session.commit()

        return jsonify({"status": "success", "message": "تم حذف سجل التقدم بنجاح"})

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error deleting progress: {str(e)}")
        return jsonify({"status": "error", "message": f"حدث خطأ أثناء حذف البيانات: {str(e)}"}), 500

# إضافة مسارات جديدة لإدارة الإعدادات

@app.route('/get-settings', methods=['GET'])
@login_required
def get_settings():
    """
    استرجاع إعدادات النظام
    """
    try:
        # البحث عن إعدادات النظام في قاعدة البيانات
        # نفترض وجود جدول Settings يحتوي على الإعدادات
        # يمكن استخدام طريقة بديلة مثل ملف تكوين أو تخزين في قاعدة بيانات
        settings = {}

        # محاولة استرجاع إعداد عدد الأسابيع المعتمدة في سبتمبر
        september_weeks_setting = Setting.query.filter_by(key='september_weeks').first()

        if september_weeks_setting:
            settings['september_weeks'] = int(september_weeks_setting.value)
        else:
            # إذا لم يتم العثور على الإعداد، نستخدم القيمة الافتراضية 2
            settings['september_weeks'] = 2

        return jsonify({"status": "success", "settings": settings})

    except Exception as e:
        app.logger.error(f"Error fetching settings: {str(e)}")
        return jsonify({"status": "error", "message": f"حدث خطأ أثناء استرجاع الإعدادات: {str(e)}"}), 500


@app.route('/save-settings', methods=['POST'])
@login_required
def save_settings():
    """
    حفظ إعدادات النظام
    """
    # التحقق من صلاحيات المستخدم (يجب أن يكون مدير)
    if not current_user.is_admin:
        return jsonify({"status": "error", "message": "غير مصرح لك بتنفيذ هذه العملية"}), 403

    try:
        data = request.json

        # التحقق من وجود بيانات
        if not data:
            return jsonify({"status": "error", "message": "لم يتم تقديم أي بيانات"}), 400

        # حفظ إعداد عدد الأسابيع المعتمدة في سبتمبر
        if 'september_weeks' in data:
            september_weeks = int(data['september_weeks'])

            # التحقق من صحة القيمة
            if september_weeks < 0 or september_weeks > 4:
                return jsonify({"status": "error", "message": "قيمة عدد الأسابيع يجب أن تكون بين 0 و 4"}), 400

            # البحث عن الإعداد الحالي
            setting = Setting.query.filter_by(key='september_weeks').first()

            if setting:
                # تحديث القيمة الحالية
                setting.value = str(september_weeks)
            else:
                # إنشاء إعداد جديد
                setting = Setting(key='september_weeks', value=str(september_weeks))
                db.session.add(setting)

            # حفظ التغييرات
            db.session.commit()

        return jsonify({"status": "success", "message": "تم حفظ الإعدادات بنجاح"})

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error saving settings: {str(e)}")
        return jsonify({"status": "error", "message": f"حدث خطأ أثناء حفظ الإعدادات: {str(e)}"}), 500


# نموذج للإعدادات
class Setting(db.Model):
    """
    نموذج الإعدادات
    يستخدم لتخزين إعدادات النظام
    """
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(50), unique=True, nullable=False)
    value = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# نموذج لانشغالات الأساتذة
class Concern(db.Model):
    """
    نموذج انشغالات الأساتذة
    يستخدم لتخزين انشغالات الأساتذة ومتابعة حالتها
    """
    id = db.Column(db.Integer, primary_key=True)
    # معرف الأستاذ الذي قدم الانشغال
    teacher_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    # عنوان الانشغال
    title = db.Column(db.String(200), nullable=False)
    # محتوى الانشغال
    content = db.Column(db.Text, nullable=False)
    # تاريخ تقديم الانشغال
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    # حالة الانشغال (pending=قيد المراجعة، approved=مقبول، rejected=مرفوض)
    status = db.Column(db.String(20), default='pending')
    # تعليق المدير أو المشرف على الانشغال
    admin_comment = db.Column(db.Text, nullable=True)
    # معرف المدير أو المشرف الذي راجع الانشغال
    reviewed_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    # تاريخ المراجعة
    reviewed_at = db.Column(db.DateTime, nullable=True)

    # العلاقة مع المستخدم (الأستاذ)
    teacher = db.relationship('User', foreign_keys=[teacher_id], backref=db.backref('concerns', lazy='dynamic'))
    # العلاقة مع المستخدم (المدير أو المشرف)
    reviewer = db.relationship('User', foreign_keys=[reviewed_by], backref=db.backref('reviewed_concerns', lazy='dynamic'))
    # العلاقة مع الردود
    replies = db.relationship('ConcernReply', backref='concern', lazy='dynamic', cascade='all, delete-orphan')

# نموذج للردود على الانشغالات
class ConcernReply(db.Model):
    """
    نموذج الردود على الانشغالات
    يستخدم لتخزين ردود الأساتذة على الانشغالات
    """
    id = db.Column(db.Integer, primary_key=True)
    # معرف الانشغال الذي تم الرد عليه
    concern_id = db.Column(db.Integer, db.ForeignKey('concern.id', ondelete='CASCADE'), nullable=False)
    # معرف المستخدم الذي قام بالرد
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    # محتوى الرد
    content = db.Column(db.Text, nullable=False)
    # تاريخ إضافة الرد
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    # تاريخ تعديل الرد (إذا تم تعديله)
    updated_at = db.Column(db.DateTime, nullable=True)

    # العلاقة مع المستخدم
    user = db.relationship('User', backref=db.backref('concern_replies', lazy='dynamic'))

# إضافة وظيفة التهجير بعد تعريف كلاس Setting مباشرة

# التأكد من إنشاء جدول Setting و Concern و ConcernReply إذا لم يكن موجودًا
@app.before_first_request
def create_tables():
    try:
        # التحقق من وجود جدول Setting
        inspector = db.inspect(db.engine)

        # التحقق من جدول Setting
        if not inspector.has_table('setting'):
            app.logger.info("جدول Setting غير موجود. جاري الإنشاء...")

            # إضافة الإعدادات الافتراضية
            default_settings = [
                Setting(key='september_weeks', value='2')  # القيمة الافتراضية: 2 أسابيع
            ]

            for setting in default_settings:
                existing = Setting.query.filter_by(key=setting.key).first()
                if not existing:
                    db.session.add(setting)

            app.logger.info("تم إنشاء جدول Setting بنجاح")
        else:
            app.logger.info("جدول Setting موجود بالفعل")

        # التحقق من جدول Concern
        if not inspector.has_table('concern'):
            app.logger.info("جدول Concern غير موجود. جاري الإنشاء...")

            # إنشاء جدول Concern
            db.create_all()
            app.logger.info("تم إنشاء جدول Concern بنجاح")
        else:
            app.logger.info("جدول Concern موجود بالفعل")

        # التحقق من جدول ConcernReply
        if not inspector.has_table('concern_reply'):
            app.logger.info("جدول ConcernReply غير موجود. جاري الإنشاء...")

            # إنشاء جدول ConcernReply
            db.create_all()
            app.logger.info("تم إنشاء جدول ConcernReply بنجاح")
        else:
            app.logger.info("جدول ConcernReply موجود بالفعل")

        # حفظ التغييرات
        db.session.commit()

    except Exception as e:
        app.logger.error(f"خطأ أثناء إنشاء الجداول: {str(e)}")
        db.session.rollback()

@app.route('/get-progress-item/<int:progress_id>')
@login_required
def get_progress_item(progress_id):
    """استرجاع سجل تقدم محدد بناءً على المعرف"""
    try:
        # استرجاع السجل المطلوب
        progress = Progress.query.get_or_404(progress_id)

        # التحقق من الصلاحيات
        if progress.user_id != current_user.id and not current_user.is_admin:
            return jsonify({"status": "error", "message": "غير مصرح لك بعرض هذا السجل"}), 403

        # تحويل البيانات إلى تنسيق JSON
        result = {
            "id": progress.id,
            "level": progress.level,
            "month": progress.month,
            "week": progress.week,
            "lesson_title": progress.lesson_title,
            "yearly_progress_id": progress.yearly_progress_id,
            "lesson_type": progress.lesson_type,
            "is_merged": progress.is_merged,
            "user_id": progress.user_id,
            "created_at": progress.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            "updated_at": progress.updated_at.strftime('%Y-%m-%d %H:%M:%S')
        }

        return jsonify({"status": "success", "progress": result})

    except Exception as e:
        app.logger.error(f"Error fetching progress item: {str(e)}")
        return jsonify({"status": "error", "message": f"حدث خطأ أثناء استرجاع البيانات: {str(e)}"}), 500

@app.route('/get-september-weeks', methods=['GET'])
@login_required
def get_september_weeks():
    """
    استرجاع عدد أسابيع سبتمبر مباشرة
    """
    try:
        # استرجاع قيمة september_weeks من الإعدادات
        september_weeks_setting = Setting.query.filter_by(key='september_weeks').first()
        september_weeks = int(september_weeks_setting.value) if september_weeks_setting else 2

        return jsonify({
            "status": "success",
            "september_weeks": september_weeks
        })

    except Exception as e:
        app.logger.error(f"Error fetching september_weeks: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"حدث خطأ أثناء استرجاع عدد أسابيع سبتمبر: {str(e)}",
            "september_weeks": 2  # القيمة الافتراضية
        }), 500

@app.route('/get-lesson-titles', methods=['GET'])
@login_required
def get_lesson_titles():
    """
    استرجاع عناوين الحصص من التدرج السنوي حسب المستوى والشهر والأسبوع
    """
    try:
        # الحصول على البيانات من الطلب
        level = request.args.get('level')
        month = request.args.get('month')
        week = request.args.get('week')

        # التحقق من وجود البيانات المطلوبة
        if not level or not month or not week:
            return jsonify({"status": "error", "message": "الرجاء تحديد المستوى والشهر والأسبوع"}), 400

        # استرجاع سجلات التدرج السنوي المطابقة
        yearly_progress = YearlyProgress.query.filter_by(
            level=level,
            month=month,
            week=int(week)
        ).all()

        # قائمة عناوين الحصص
        lesson_titles = []

        # جمع عناوين الحصص وترتيبها
        for item in yearly_progress:
            if item.lesson_title and item.lesson_title not in lesson_titles:
                lesson_titles.append(item.lesson_title)

        # إضافة عناوين الحصص الشائعة
        common_lessons = [
            "تطبيقات وتمارين",
            "حصة مراجعة",
            "حصة تقويم"
        ]

        for title in common_lessons:
            if title not in lesson_titles:
                lesson_titles.append(title)

        return jsonify({
            "status": "success",
            "lesson_titles": lesson_titles
        })

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"حدث خطأ أثناء استرجاع عناوين الحصص: {str(e)}"
        }), 500

@app.route('/check_messages')
@login_required
def check_messages():
    """التحقق من وجود رسائل جديدة والحصول على اسم المرسل الأخير"""
    try:
        app.logger.info(f"التحقق من الرسائل الجديدة للمستخدم {current_user.id}")

        # الحصول على عدد الرسائل غير المقروءة
        unread_count = Message.query.filter_by(
            recipient_id=current_user.id,
            is_read=False,
            deleted_by_recipient=False
        ).count()

        # الحصول على آخر رسالة غير مقروءة وإسم المرسل
        sender_name = "شخص ما"
        last_message = Message.query.filter_by(
            recipient_id=current_user.id,
            is_read=False,
            deleted_by_recipient=False
        ).order_by(Message.timestamp.desc()).first()

        if last_message:
            sender = User.query.get(last_message.sender_id)
            if sender:
                sender_name = sender.teacher_name
                app.logger.debug(f"آخر رسالة غير مقروءة من: {sender_name}")

        response_data = {
            'count': unread_count,
            'sender': sender_name
        }
        app.logger.debug(f"بيانات الاستجابة: {response_data}")
        return jsonify(response_data)

    except Exception as e:
        app.logger.error(f"خطأ في check_messages: {str(e)}")
        app.logger.error(traceback.format_exc())
        return jsonify({
            'count': 0,
            'sender': ""
        })

@app.route('/progress-dashboard')
@login_required
def progress_dashboard():
    # التحقق من أن المستخدم هو مدير
    if not current_user.is_admin:
        flash('ليس لديك صلاحية لعرض هذه الصفحة', 'danger')
        return redirect(url_for('welcome'))

    # الحصول على الأسبوع الحالي (مثل ما يظهر في صفحة التقدم)
    current_week = get_current_week()

    return render_template('progress_dashboard.html', current_week=current_week)

@app.route('/get-all-teachers-progress')
@login_required
def get_all_teachers_progress():
    # التحقق من أن المستخدم هو مدير
    if not current_user.is_admin:
        return jsonify({'error': 'غير مصرح'}), 403

    # الحصول على جميع المستخدمين (بما في ذلك المديرين)
    users = User.query.all()

    result = []

    for user in users:
        # جلب آخر تقدم لكل مستوى دراسي للمستخدم
        progress_data = {}

        # قائمة بالمستويات
        levels = ['السنة الأولى متوسط', 'السنة الثانية متوسط', 'السنة الثالثة متوسط', 'السنة الرابعة متوسط']

        for level in levels:
            # الحصول على آخر سجل تقدم للمستوى
            latest_progress = Progress.query.filter_by(
                user_id=user.id,
                level=level
            ).order_by(Progress.created_at.desc()).first()

            if latest_progress:
                progress_data[level] = {
                    'teacher_name': user.teacher_name,
                    'workplace': user.workplace,  # إضافة مكان العمل
                    'level': level,
                    'last_lesson_title': latest_progress.lesson_title,
                    'last_lesson_week': latest_progress.week,
                    'month': latest_progress.month,
                    'is_admin': user.is_admin,
                    'admin_type': user.admin_type if user.admin_type else ''
                }

        # إضافة بيانات المستخدم إلى القائمة النهائية
        for level, data in progress_data.items():
            result.append(data)

    return jsonify(result)

# دالة مساعدة للحصول على الأسبوع الحالي
def get_current_week():
    # الحصول على عدد أسابيع سبتمبر
    setting = Setting.query.filter_by(key='september_weeks').first()
    september_weeks = int(setting.value) if setting else 2

    # الحصول على التاريخ الحالي
    now = datetime.now()
    current_month = now.month  # 1-12 (1=January)

    # تحويل شهر النظام إلى الشهر الأكاديمي
    academic_month = 0
    arabic_month = ''

    if current_month >= 9:  # سبتمبر (9) إلى ديسمبر (12)
        academic_month = current_month - 8  # 1-4
        if academic_month == 1:
            arabic_month = 'سبتمبر'
        elif academic_month == 2:
            arabic_month = 'أكتوبر'
        elif academic_month == 3:
            arabic_month = 'نوفمبر'
        elif academic_month == 4:
            arabic_month = 'ديسمبر'
    else:  # جانفي (1) إلى جوان (6)
        academic_month = current_month + 4  # 5-10
        if academic_month == 5:
            arabic_month = 'جانفي'
        elif academic_month == 6:
            arabic_month = 'فيفري'
        elif academic_month == 7:
            arabic_month = 'مارس'
        elif academic_month == 8:
            arabic_month = 'أفريل'
        elif academic_month == 9:
            arabic_month = 'ماي'
        elif academic_month == 10:
            arabic_month = 'جوان'

    # حساب الأسبوع الحالي في الشهر (1-4)
    day_of_month = now.day
    week_in_month = 1
    if day_of_month <= 7:
        week_in_month = 1
    elif day_of_month <= 14:
        week_in_month = 2
    elif day_of_month <= 21:
        week_in_month = 3
    else:
        week_in_month = 4

    # حساب الأسبوع الكلي
    total_week = week_in_month

    # إضافة أسابيع الشهور السابقة
    months_order = {
        'سبتمبر': 1,
        'أكتوبر': 2,
        'نوفمبر': 3,
        'ديسمبر': 4,
        'جانفي': 5,
        'فيفري': 6,
        'مارس': 7,
        'أفريل': 8,
        'ماي': 9,
        'جوان': 10
    }

    month_order = months_order.get(arabic_month, 0)

    for m in range(1, month_order):
        if m == 1:  # سبتمبر
            total_week += september_weeks
        else:  # بقية الشهور
            total_week += 4

    return total_week

@app.route('/teacher_schedules')
@login_required
def teacher_schedules():
    # التحقق من الصلاحيات: يجب أن يكون المستخدم مديرًا أو مشرفًا
    if not current_user.is_admin:
        flash('غير مصرح لك بالوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('index'))

    # الحصول على جميع الأساتذة (غير المشرفين) والمشرفين باستثناء المدير
    teachers = User.query.filter(
        db.or_(
            User.is_admin == False,  # الأساتذة العاديين
            db.and_(User.is_admin == True, User.admin_type != 'مدير')  # المشرفين باستثناء المدير
        )
    ).order_by(User.workplace, User.teacher_name).all()

    return render_template('teacher_schedules.html', teachers=teachers)

@app.route('/get_teacher_schedule/<int:user_id>')
@login_required
def get_teacher_schedule(user_id):
    try:
        # التحقق من الصلاحيات: يجب أن يكون المستخدم مديرًا أو مشرفًا
        if not current_user.is_admin:
            return jsonify({'status': 'error', 'message': 'غير مصرح لك بالوصول إلى هذه البيانات'}), 403

        app.logger.info(f'بدء استرجاع بيانات الجدول للمستخدم {user_id}')

        # التحقق من وجود المستخدم
        teacher = User.query.get(user_id)
        if not teacher:
            return jsonify({'status': 'error', 'message': 'لم يتم العثور على المستخدم'}), 404

        # البحث عن سجلات جدول التوقيت للمستخدم المطلوب
        schedule_items = Schedule.query.filter_by(user_id=user_id).all()
        app.logger.info(f'تم العثور على {len(schedule_items)} من عناصر الجدول للمستخدم {user_id}')

        # تحويل السجلات إلى قائمة
        schedule_data = []
        for item in schedule_items:
            schedule_data.append({
                'day': item.day,
                'hour': item.hour,
                'class_name': item.class_name
            })

        # استرجاع بيانات التوقيت المخصصة للمستخدم
        time_items = ScheduleTimes.query.filter_by(user_id=user_id).all()
        app.logger.info(f'تم العثور على {len(time_items)} من عناصر التوقيت للمستخدم {user_id}')

        # تحويل سجلات التوقيت إلى قائمة
        time_data = []
        for item in time_items:
            time_data.append({
                'original_time': item.original_time,
                'time_range': item.time_range
            })

        app.logger.info(f'تم استرجاع {len(schedule_data)} عنصر من جدول التوقيت و {len(time_data)} من بيانات التوقيت للمستخدم {user_id}')

        # تنسيق البيانات بالشكل المتوقع في الواجهة
        response_data = {
            'schedule': schedule_data,
            'times': time_data
        }

        return jsonify({
            'status': 'success',
            'message': 'تم استرجاع بيانات الجدول بنجاح',
            'data': response_data
        })

    except Exception as e:
        app.logger.error(f'خطأ في استرجاع بيانات جدول التوقيت: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء استرجاع بيانات الجدول: {str(e)}'}), 500

# مسار صفحة محادثة واتساب (تم توجيهه مؤقتًا إلى صفحة الصيانة)
@app.route('/whatsapp_chat')
@login_required
def whatsapp_chat():
    """عرض صفحة الصيانة بدلاً من صفحة الواتساب"""
    return render_template('maintenance.html')

# مسار صفحة انشغالات الأساتذة
@app.route('/concerns')
@login_required
def concerns_page():
    """عرض صفحة انشغالات الأساتذة"""
    try:
        # جلب الانشغالات المقبولة فقط للعرض العام
        approved_concerns = Concern.query.filter_by(status='approved').order_by(Concern.created_at.desc()).all()
        app.logger.info(f"تم استرجاع {len(approved_concerns)} انشغال مقبول")
        return render_template('concerns.html', concerns=approved_concerns)
    except Exception as e:
        app.logger.error(f"خطأ في عرض صفحة الانشغالات: {str(e)}")
        app.logger.error(traceback.format_exc())
        flash('حدث خطأ أثناء تحميل الانشغالات', 'error')
        return render_template('concerns.html', concerns=[])

# مسار إضافة انشغال جديد
@app.route('/add_concern', methods=['POST'])
@login_required
def add_concern():
    """إضافة انشغال جديد"""
    try:
        # طباعة معلومات الطلب للتشخيص
        app.logger.info(f'طريقة الطلب: {request.method}')
        app.logger.info(f'رؤوس الطلب: {dict(request.headers)}')
        app.logger.info(f'بيانات النموذج: {dict(request.form)}')
        app.logger.info(f'معرف المستخدم الحالي: {current_user.id}')

        # استلام البيانات من النموذج
        title = request.form.get('title', '')
        content = request.form.get('content', '')

        # طباعة البيانات المستلمة للتشخيص
        app.logger.info(f'بيانات الانشغال المستلمة: العنوان={title}, المحتوى={content}')

        # التحقق من البيانات
        if not title or not content:
            return jsonify({'status': 'error', 'message': 'يرجى ملء جميع الحقول المطلوبة'}), 400

        # إنشاء انشغال جديد
        try:
            new_concern = Concern(
                teacher_id=current_user.id,
                title=title,
                content=content,
                status='pending',
                created_at=datetime.now()
            )
            app.logger.info(f'تم إنشاء كائن الانشغال بنجاح: {new_concern}')
        except Exception as e:
            app.logger.error(f'خطأ في إنشاء كائن الانشغال: {str(e)}')
            return jsonify({'status': 'error', 'message': f'خطأ في إنشاء الانشغال: {str(e)}'}), 500

        # حفظ الانشغال في قاعدة البيانات
        try:
            db.session.add(new_concern)
            db.session.commit()
            app.logger.info(f'تم إضافة انشغال جديد بواسطة المستخدم {current_user.id}: {title}')
        except Exception as e:
            db.session.rollback()
            app.logger.error(f'خطأ في حفظ الانشغال في قاعدة البيانات: {str(e)}')
            return jsonify({'status': 'error', 'message': f'خطأ في حفظ الانشغال: {str(e)}'}), 500

        return jsonify({
            'status': 'success',
            'message': 'تم إرسال الانشغال بنجاح وسيتم مراجعته من قبل الإدارة'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f'خطأ في إضافة انشغال جديد: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء إرسال الانشغال: {str(e)}'}), 500

# مسار لوحة تحكم الانشغالات للمدير والمشرفين
@app.route('/admin/concerns')
@login_required
def admin_concerns():
    """لوحة تحكم الانشغالات للمدير والمشرفين"""
    # التحقق من أن المستخدم مدير أو مشرف
    if not current_user.is_admin:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('index'))

    # جلب جميع الانشغالات مرتبة حسب الحالة والتاريخ
    pending_concerns = Concern.query.filter_by(status='pending').order_by(Concern.created_at.desc()).all()
    approved_concerns = Concern.query.filter_by(status='approved').order_by(Concern.created_at.desc()).all()
    rejected_concerns = Concern.query.filter_by(status='rejected').order_by(Concern.created_at.desc()).all()

    return render_template(
        'admin_concerns.html',
        pending_concerns=pending_concerns,
        approved_concerns=approved_concerns,
        rejected_concerns=rejected_concerns
    )

# مسار مراجعة انشغال (قبول أو رفض)
@app.route('/admin/review_concern/<int:concern_id>', methods=['POST'])
@login_required
def review_concern(concern_id):
    """مراجعة انشغال (قبول أو رفض)"""
    # التحقق من أن المستخدم مدير أو مشرف
    if not current_user.is_admin:
        return jsonify({'status': 'error', 'message': 'ليس لديك صلاحية للقيام بهذه العملية'}), 403

    try:
        # استلام البيانات من النموذج
        status = request.form.get('status')  # 'approved' أو 'rejected'
        comment = request.form.get('comment')

        # التحقق من البيانات
        if not status or status not in ['approved', 'rejected']:
            return jsonify({'status': 'error', 'message': 'حالة غير صالحة'}), 400

        # البحث عن الانشغال
        concern = Concern.query.get_or_404(concern_id)

        # تحديث حالة الانشغال
        concern.status = status
        concern.admin_comment = comment
        concern.reviewed_by = current_user.id
        concern.reviewed_at = datetime.now()

        # حفظ التغييرات
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': 'تمت مراجعة الانشغال بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f'خطأ في مراجعة انشغال: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء مراجعة الانشغال: {str(e)}'}), 500

# مسار حذف انشغال
@app.route('/admin/delete_concern/<int:concern_id>', methods=['POST'])
@login_required
def delete_concern(concern_id):
    """حذف انشغال"""
    # التحقق من أن المستخدم مدير أو مشرف
    if not current_user.is_admin:
        return jsonify({'status': 'error', 'message': 'ليس لديك صلاحية للقيام بهذه العملية'}), 403

    try:
        # البحث عن الانشغال
        concern = Concern.query.get_or_404(concern_id)

        # حذف الانشغال
        db.session.delete(concern)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': 'تم حذف الانشغال بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f'خطأ في حذف انشغال: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء حذف الانشغال: {str(e)}'}), 500

# مسار API لجلب الانشغالات
@app.route('/api/get_concerns')
@login_required
def get_concerns():
    """جلب الانشغالات حسب الحالة"""
    try:
        # استلام معلمة الحالة من الاستعلام
        status = request.args.get('status', 'all')

        # بناء الاستعلام
        query = Concern.query

        # تطبيق فلتر الحالة إذا تم تحديده
        if status != 'all' and status in ['pending', 'approved', 'rejected']:
            query = query.filter_by(status=status)

        # ترتيب النتائج حسب التاريخ (الأحدث أولاً)
        concerns = query.order_by(Concern.created_at.desc()).all()

        # تحويل النتائج إلى JSON
        result = []
        for concern in concerns:
            teacher = User.query.get(concern.teacher_id)
            reviewer = User.query.get(concern.reviewed_by) if concern.reviewed_by else None

            result.append({
                'id': concern.id,
                'title': concern.title,
                'content': concern.content,
                'status': concern.status,
                'created_at': concern.created_at.strftime('%Y-%m-%d %H:%M'),
                'teacher': {
                    'id': teacher.id,
                    'name': teacher.teacher_name,
                    'workplace': teacher.workplace
                },
                'admin_comment': concern.admin_comment,
                'reviewer': {
                    'id': reviewer.id,
                    'name': reviewer.teacher_name
                } if reviewer else None,
                'reviewed_at': concern.reviewed_at.strftime('%Y-%m-%d %H:%M') if concern.reviewed_at else None
            })

        return jsonify({
            'status': 'success',
            'concerns': result
        })
    except Exception as e:
        app.logger.error(f'خطأ في جلب الانشغالات: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء جلب الانشغالات: {str(e)}'}), 500

# مسار API لجلب ردود انشغال معين
@app.route('/api/get_concern_replies/<int:concern_id>')
@login_required
def get_concern_replies(concern_id):
    """جلب ردود انشغال معين"""
    try:
        # التحقق من وجود الانشغال
        concern = Concern.query.get_or_404(concern_id)

        # التحقق من أن الانشغال مقبول
        if concern.status != 'approved':
            return jsonify({
                'status': 'error',
                'message': 'لا يمكن عرض ردود على انشغال غير مقبول'
            }), 403

        # جلب الردود مرتبة حسب التاريخ (الأقدم أولاً)
        replies = ConcernReply.query.filter_by(concern_id=concern_id).order_by(ConcernReply.created_at.asc()).all()

        # تحويل النتائج إلى JSON
        result = []
        for reply in replies:
            user = User.query.get(reply.user_id)

            result.append({
                'id': reply.id,
                'content': reply.content,
                'created_at': reply.created_at.strftime('%Y-%m-%d %H:%M'),
                'updated_at': reply.updated_at.strftime('%Y-%m-%d %H:%M') if reply.updated_at else None,
                'user': {
                    'id': user.id,
                    'name': user.teacher_name,
                    'is_admin': user.is_admin
                },
                'is_owner': reply.user_id == current_user.id
            })

        return jsonify({
            'status': 'success',
            'replies': result
        })

    except Exception as e:
        app.logger.error(f'خطأ في جلب ردود الانشغال: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء جلب الردود: {str(e)}'}), 500

# مسار إضافة رد على انشغال
@app.route('/api/add_concern_reply/<int:concern_id>', methods=['POST'])
@login_required
def add_concern_reply(concern_id):
    """إضافة رد على انشغال"""
    try:
        # التحقق من وجود الانشغال
        concern = Concern.query.get_or_404(concern_id)

        # التحقق من أن الانشغال مقبول
        if concern.status != 'approved':
            return jsonify({
                'status': 'error',
                'message': 'لا يمكن إضافة رد على انشغال غير مقبول'
            }), 403

        # استلام البيانات من النموذج
        content = request.form.get('content', '')

        # التحقق من البيانات
        if not content:
            return jsonify({'status': 'error', 'message': 'يرجى إدخال محتوى الرد'}), 400

        # إنشاء رد جديد
        new_reply = ConcernReply(
            concern_id=concern_id,
            user_id=current_user.id,
            content=content,
            created_at=datetime.now()
        )

        # حفظ الرد في قاعدة البيانات
        db.session.add(new_reply)
        db.session.commit()

        # إعداد بيانات الرد للإرجاع
        user = User.query.get(current_user.id)
        reply_data = {
            'id': new_reply.id,
            'content': new_reply.content,
            'created_at': new_reply.created_at.strftime('%Y-%m-%d %H:%M'),
            'user': {
                'id': user.id,
                'name': user.teacher_name,
                'is_admin': user.is_admin
            },
            'is_owner': True
        }

        return jsonify({
            'status': 'success',
            'message': 'تم إضافة الرد بنجاح',
            'reply': reply_data
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f'خطأ في إضافة رد على انشغال: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء إضافة الرد: {str(e)}'}), 500

# مسار تعديل رد على انشغال
@app.route('/api/edit_concern_reply/<int:reply_id>', methods=['POST'])
@login_required
def edit_concern_reply(reply_id):
    """تعديل رد على انشغال"""
    try:
        # التحقق من وجود الرد
        reply = ConcernReply.query.get_or_404(reply_id)

        # التحقق من أن المستخدم هو صاحب الرد
        if reply.user_id != current_user.id:
            return jsonify({
                'status': 'error',
                'message': 'لا يمكنك تعديل رد لم تقم بإضافته'
            }), 403

        # استلام البيانات من النموذج
        content = request.form.get('content', '')

        # التحقق من البيانات
        if not content:
            return jsonify({'status': 'error', 'message': 'يرجى إدخال محتوى الرد'}), 400

        # تحديث الرد
        reply.content = content
        reply.updated_at = datetime.now()

        # حفظ التغييرات
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': 'تم تعديل الرد بنجاح',
            'reply': {
                'id': reply.id,
                'content': reply.content,
                'updated_at': reply.updated_at.strftime('%Y-%m-%d %H:%M')
            }
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f'خطأ في تعديل رد على انشغال: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء تعديل الرد: {str(e)}'}), 500

# مسار حذف رد على انشغال
@app.route('/api/delete_concern_reply/<int:reply_id>', methods=['POST'])
@login_required
def delete_concern_reply(reply_id):
    """حذف رد على انشغال"""
    try:
        # التحقق من وجود الرد
        reply = ConcernReply.query.get_or_404(reply_id)

        # التحقق من أن المستخدم هو صاحب الرد أو مدير
        if reply.user_id != current_user.id and not current_user.is_admin:
            return jsonify({
                'status': 'error',
                'message': 'لا يمكنك حذف رد لم تقم بإضافته'
            }), 403

        # حذف الرد
        db.session.delete(reply)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': 'تم حذف الرد بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f'خطأ في حذف رد على انشغال: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء حذف الرد: {str(e)}'}), 500

# مسار API لإرسال رسالة واتساب
@app.route('/api/send_whatsapp_message', methods=['POST'])
@login_required
def send_whatsapp_message():
    """إرسال رسالة واتساب جديدة"""
    try:
        data = request.json
        recipient_id = data.get('recipient_id')
        message_text = data.get('message')

        # التحقق من البيانات
        if not recipient_id or not message_text:
            return jsonify({'status': 'error', 'message': 'بيانات غير كاملة'}), 400

        # التحقق من وجود المستلم
        recipient = User.query.get(recipient_id)
        if not recipient:
            return jsonify({'status': 'error', 'message': 'المستلم غير موجود'}), 404

        # إنشاء رسالة جديدة
        new_message = Message(
            sender_id=current_user.id,
            recipient_id=recipient_id,
            content=message_text,
            is_read=False
        )

        # حفظ الرسالة في قاعدة البيانات
        db.session.add(new_message)
        db.session.commit()

        # إعداد بيانات الرد
        response_data = {
            'id': new_message.id,
            'sender_id': new_message.sender_id,
            'recipient_id': new_message.recipient_id,
            'content': new_message.content,
            'timestamp': new_message.timestamp.strftime('%H:%M'),
            'is_read': new_message.is_read
        }

        return jsonify({'status': 'success', 'message': 'تم إرسال الرسالة بنجاح', 'data': response_data})

    except Exception as e:
        db.session.rollback()
        app.logger.error(f'خطأ في إرسال رسالة واتساب: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء إرسال الرسالة: {str(e)}'}), 500

# مسار API لتحديث حالة قراءة الرسائل
@app.route('/api/update_read_status', methods=['POST'])
@login_required
def update_read_status():
    """تحديث حالة قراءة الرسائل"""
    try:
        data = request.json
        message_ids = data.get('message_ids', [])

        if not message_ids:
            return jsonify({'status': 'error', 'message': 'لم يتم تحديد أي رسائل'}), 400

        # تحديث حالة القراءة للرسائل المحددة
        updated_count = 0
        for msg_id in message_ids:
            message = Message.query.get(msg_id)
            if message and message.recipient_id == current_user.id and not message.is_read:
                message.is_read = True
                updated_count += 1

        # حفظ التغييرات في قاعدة البيانات
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': f'تم تحديث حالة القراءة لـ {updated_count} رسائل',
            'updated_count': updated_count
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f'خطأ في تحديث حالة قراءة الرسائل: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء تحديث حالة القراءة: {str(e)}'}), 500

# مسار API لجلب رسائل المحادثة
@app.route('/api/get_whatsapp_messages/<int:user_id>', methods=['GET'])
@login_required
def get_whatsapp_messages(user_id):
    """جلب رسائل المحادثة مع مستخدم محدد"""
    try:
        # التحقق من وجود المستخدم
        user = User.query.get(user_id)
        if not user:
            return jsonify({'status': 'error', 'message': 'المستخدم غير موجود'}), 404

        # جلب الرسائل بين المستخدم الحالي والمستخدم المحدد
        messages = Message.query.filter(
            ((Message.sender_id == current_user.id) & (Message.recipient_id == user_id)) |
            ((Message.sender_id == user_id) & (Message.recipient_id == current_user.id))
        ).order_by(Message.timestamp).all()

        # تحويل الرسائل إلى تنسيق JSON
        messages_data = []
        for msg in messages:
            messages_data.append({
                'id': msg.id,
                'sender_id': msg.sender_id,
                'recipient_id': msg.recipient_id,
                'content': msg.content,
                'timestamp': msg.timestamp.strftime('%H:%M'),
                'is_read': msg.is_read,
                'is_outgoing': msg.sender_id == current_user.id
            })

            # تحديث حالة القراءة للرسائل المستلمة
            if msg.recipient_id == current_user.id and not msg.is_read:
                msg.is_read = True
                # إضافة معلومات التحديث للرد
                msg.updated_read_status = True
            else:
                msg.updated_read_status = False

        # حفظ التغييرات في قاعدة البيانات
        db.session.commit()

        return jsonify({
            'status': 'success',
            'data': {
                'messages': messages_data,
                'user': {
                    'id': user.id,
                    'name': user.teacher_name,
                    'profile_picture': user.profile_picture
                }
            }
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f'خطأ في جلب رسائل واتساب: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء جلب الرسائل: {str(e)}'}), 500

# مسار API للتحقق من حالة قراءة الرسائل
@app.route('/api/check_read_status', methods=['POST'])
@login_required
def check_read_status():
    """التحقق من حالة قراءة الرسائل"""
    try:
        data = request.json
        message_ids = data.get('message_ids', [])

        if not message_ids:
            return jsonify({'status': 'error', 'message': 'لم يتم تحديد أي رسائل'}), 400

        # التحقق من حالة القراءة للرسائل المحددة
        messages_status = {}
        for msg_id in message_ids:
            message = Message.query.get(msg_id)
            if message:
                messages_status[msg_id] = {
                    'is_read': message.is_read,
                    'sender_id': message.sender_id,
                    'recipient_id': message.recipient_id
                }

        return jsonify({
            'status': 'success',
            'data': messages_status
        })

    except Exception as e:
        app.logger.error(f'خطأ في التحقق من حالة قراءة الرسائل: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء التحقق من حالة القراءة: {str(e)}'}), 500

# مسار API لحذف رسالة واتساب
@app.route('/api/delete_whatsapp_message/<int:message_id>', methods=['DELETE'])
@login_required
def delete_whatsapp_message(message_id):
    """حذف رسالة من المحادثة"""
    try:
        # البحث عن الرسالة
        message = Message.query.get(message_id)

        # التحقق من وجود الرسالة
        if not message:
            return jsonify({'status': 'error', 'message': 'الرسالة غير موجودة'}), 404

        # التحقق من أن المستخدم هو مرسل الرسالة
        if message.sender_id != current_user.id:
            return jsonify({'status': 'error', 'message': 'غير مسموح بحذف رسائل الآخرين'}), 403

        # حذف الرسالة من قاعدة البيانات
        db.session.delete(message)

        # حفظ التغييرات
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': 'تم حذف الرسالة بنجاح',
            'data': {
                'message_id': message_id,
                'is_deleted': True
            }
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f'خطأ في حذف الرسالة: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': f'حدث خطأ أثناء حذف الرسالة: {str(e)}'}), 500

# مسار API لتحديث حالة المستخدم
@app.route('/api/update_user_status', methods=['POST'])
@login_required
def update_user_status():
    """تحديث حالة المستخدم (متصل/غير متصل)"""
    try:
        data = request.json
        is_online = data.get('is_online', False)

        # تحديث حالة المستخدم
        current_user.is_online = is_online

        # إذا كان المستخدم متصل، تحديث وقت آخر ظهور
        if is_online:
            current_user.last_seen = datetime.now()

        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': 'تم تحديث حالة المستخدم بنجاح',
            'data': {
                'is_online': current_user.is_online,
                'last_seen': current_user.last_seen.isoformat() if current_user.last_seen else None
            }
        })
    except Exception as e:
        app.logger.error(f'خطأ في تحديث حالة المستخدم: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': 'حدث خطأ أثناء تحديث حالة المستخدم'}), 500

# مسار API للحصول على حالة المستخدم
@app.route('/api/get_user_status/<int:user_id>', methods=['GET'])
@login_required
def get_user_status(user_id):
    """الحصول على حالة مستخدم محدد"""
    try:
        user = User.query.get(user_id)

        if not user:
            return jsonify({'status': 'error', 'message': 'المستخدم غير موجود'}), 404

        return jsonify({
            'status': 'success',
            'data': {
                'user_id': user.id,
                'is_online': user.is_online,
                'last_seen': user.last_seen.isoformat() if user.last_seen else None
            }
        })
    except Exception as e:
        app.logger.error(f'خطأ في الحصول على حالة المستخدم: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': 'حدث خطأ أثناء الحصول على حالة المستخدم'}), 500


# مسار API للحصول على عبارة الترحيب وحالة الظهور
@app.route('/api/get_greeting', methods=['GET'])
def get_greeting():
    """الحصول على عبارة الترحيب وحالة الظهور"""
    try:
        # الحصول على عبارة الترحيب من قاعدة البيانات
        greeting = SiteSettings.get_value('greeting_text', 'السلام عليكم و رحمة الله و بركاته')
        app.logger.info(f"تم استرجاع عبارة الترحيب: {greeting}")

        # الحصول على حالة ظهور الشريط
        greeting_visible = SiteSettings.get_value('greeting_visible', 'true')
        app.logger.info(f"تم استرجاع حالة ظهور الشريط: {greeting_visible}")

        # تحويل القيمة النصية إلى قيمة منطقية
        is_visible = greeting_visible.lower() == 'true'
        app.logger.info(f"هل الشريط ظاهر؟ {is_visible}")

        # التحقق من صلاحيات المستخدم (هل هو مدير أو مشرف)
        is_admin = current_user.is_authenticated and current_user.is_admin
        app.logger.info(f"هل المستخدم مدير أو مشرف؟ {is_admin}")

        return jsonify({
            'status': 'success',
            'data': {
                'greeting': greeting,
                'visible': is_visible,  # هذه قيمة منطقية (True/False) وليست نصية
                'is_admin': is_admin
            }
        })
    except Exception as e:
        app.logger.error(f'خطأ في الحصول على عبارة الترحيب: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': 'حدث خطأ أثناء الحصول على عبارة الترحيب'}), 500


# مسار API لتحديث عبارة الترحيب
@app.route('/api/update_greeting', methods=['POST'])
@login_required
def update_greeting():
    """تحديث عبارة الترحيب"""
    try:
        # التحقق من صلاحيات المستخدم (يجب أن يكون مدير أو مشرف)
        if not current_user.is_admin:
            return jsonify({'status': 'error', 'message': 'ليس لديك صلاحية لتعديل عبارة الترحيب'}), 403

        # التحقق من البيانات المرسلة
        data = request.json
        new_greeting = data.get('greeting', '').strip()

        if not new_greeting:
            return jsonify({'status': 'error', 'message': 'عبارة الترحيب لا يمكن أن تكون فارغة'}), 400

        # تحديث عبارة الترحيب في قاعدة البيانات
        SiteSettings.set_value('greeting_text', new_greeting)

        return jsonify({
            'status': 'success',
            'message': 'تم تحديث عبارة الترحيب بنجاح',
            'data': {
                'greeting': new_greeting
            }
        })
    except Exception as e:
        app.logger.error(f'خطأ في تحديث عبارة الترحيب: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': 'حدث خطأ أثناء تحديث عبارة الترحيب'}), 500


# مسار API لتحديث حالة ظهور الشريط
@app.route('/api/update_greeting_visibility', methods=['POST'])
@login_required
def update_greeting_visibility():
    """تحديث حالة ظهور شريط الترحيب"""
    try:
        # التحقق من صلاحيات المستخدم (يجب أن يكون مدير أو مشرف)
        if not current_user.is_admin:
            app.logger.warning(f"محاولة غير مصرح بها لتحديث حالة ظهور الشريط من قبل المستخدم {current_user.username}")
            return jsonify({'status': 'error', 'message': 'ليس لديك صلاحية لتعديل ظهور الشريط'}), 403

        # التحقق من البيانات المرسلة
        data = request.json
        visible = data.get('visible', True)
        app.logger.info(f"طلب تحديث حالة ظهور الشريط إلى: {visible}")

        # تحديث حالة ظهور الشريط في قاعدة البيانات
        app.logger.info(f"طلب تحديث حالة ظهور الشريط إلى: {visible}, نوع القيمة: {type(visible)}")

        # تحويل القيمة إلى نص
        new_value = 'true' if visible else 'false'
        app.logger.info(f"تحديث قيمة greeting_visible في قاعدة البيانات إلى: {new_value}")

        # حذف الإعداد الحالي إذا كان موجودًا
        existing_setting = SiteSettings.query.filter_by(key='greeting_visible').first()
        if existing_setting:
            existing_setting.value = new_value
            db.session.commit()
            app.logger.info(f"تم تحديث الإعداد الموجود إلى: {new_value}")
        else:
            # إنشاء إعداد جديد
            new_setting = SiteSettings(key='greeting_visible', value=new_value)
            db.session.add(new_setting)
            db.session.commit()
            app.logger.info(f"تم إنشاء إعداد جديد بقيمة: {new_value}")

        # التحقق من نجاح التحديث
        updated_value = SiteSettings.get_value('greeting_visible')
        app.logger.info(f"تم التحقق من القيمة بعد التحديث: {updated_value}")

        return jsonify({
            'status': 'success',
            'message': 'تم تحديث حالة ظهور الشريط بنجاح',
            'data': {
                'visible': visible
            }
        })
    except Exception as e:
        app.logger.error(f'خطأ في تحديث حالة ظهور الشريط: {str(e)}')
        app.logger.error(traceback.format_exc())
        return jsonify({'status': 'error', 'message': 'حدث خطأ أثناء تحديث حالة ظهور الشريط'}), 500