{% extends 'base.html' %}

{% block title %}إدارة انشغالات الأساتذة{% endblock %}

{% block head %}
<link href="{{ url_for('static', filename='concerns.css') }}" rel="stylesheet">
<style>
    /* تنسيق عام لإزالة المساحة الفارغة */
    body {
        overflow-x: hidden;
    }

    .container {
        padding: 0;
    }

    .row {
        margin: 0;
    }
    /* تنسيقات النموذج */
    .modal-content {
        border-radius: 8px;
        border: none;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        overflow: hidden;
    }

    /* تعديل المساحة الفارغة بين أزرار التبويب والمحتوى */
    .tab-content {
        margin-top: 10px !important;
        padding: 0 !important;
        border: none !important;
    }

    .tab-pane {
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
    }

    .nav-tabs {
        margin-bottom: 0 !important;
        border-bottom: 1px solid #dee2e6;
    }

    .nav-tabs .nav-link.active {
        border-bottom-color: #fff;
    }

    /* تعديل حاويات الانشغالات */
    .concern-card {
        margin-top: 0 !important;
        border-top: none;
    }

    /* إزالة المساحة الفارغة في الصفحة */
    .admin-concerns-container {
        padding: 0 !important;
    }

    /* تعديل المسافة بين التبويبات والمحتوى */
    #concernsTabsContent {
        padding: 0 !important;
        margin-top: 20px !important;
    }

    /* إزالة المساحة الفارغة بشكل نهائي */
    .tab-content > .tab-pane {
        display: block !important;
        height: auto !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* إخفاء التبويبات غير النشطة */
    .tab-content > .tab-pane:not(.active) {
        display: none !important;
    }

    /* تنسيقات الروابط في محتوى الانشغال وتعليقات المشرفين */
    .concern-content a, .comment-content a {
        color: var(--primary-color);
        text-decoration: none;
        border-bottom: 1px dashed var(--primary-color);
        transition: all 0.2s ease;
        word-break: break-word;
    }

    .concern-content a:hover, .comment-content a:hover {
        color: var(--primary-dark);
        border-bottom: 1px solid var(--primary-dark);
    }

    #approveModal .modal-header {
        background: linear-gradient(135deg, #2ecc71, #27ae60);
        color: white;
        border: none;
        padding: 12px 15px;
    }

    #rejectModal .modal-header {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        color: white;
        border: none;
        padding: 12px 15px;
    }

    .modal-title {
        font-weight: 600;
        font-size: 1rem;
    }

    .modal-body {
        padding: 15px;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 4px;
        font-size: 0.9rem;
    }

    .form-control {
        border-radius: 6px;
        padding: 8px 10px;
        border: 1px solid #e0e0e0;
        transition: all 0.3s;
        font-size: 0.9rem;
    }

    .form-control:focus {
        border-color: #1976d2;
        box-shadow: 0 0 0 0.15rem rgba(25, 118, 210, 0.15);
    }

    .modal-footer {
        padding: 10px 15px;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }

    #approveModal .btn-success {
        background: linear-gradient(135deg, #2ecc71, #27ae60);
        border: none;
        padding: 6px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.85rem;
        transition: all 0.3s;
    }

    #approveModal .btn-success:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 8px rgba(46, 204, 113, 0.2);
        background: linear-gradient(135deg, #27ae60, #219653);
    }

    #rejectModal .btn-danger {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        border: none;
        padding: 6px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.85rem;
        transition: all 0.3s;
    }

    #rejectModal .btn-danger:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 8px rgba(231, 76, 60, 0.2);
        background: linear-gradient(135deg, #c0392b, #a93226);
    }

    .btn-secondary {
        background-color: #f8f9fa;
        color: #495057;
        border: none;
        padding: 6px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.85rem;
        transition: all 0.3s;
    }

    .btn-secondary:hover {
        background-color: #e9ecef;
    }

    /* تنسيقات إضافية للتبويبات */
    .nav-tabs .nav-link.active {
        position: relative;
        z-index: 2;
    }

    /* تنسيقات للشاشات الصغيرة */
    @media (max-width: 576px) {
        .modal-dialog {
            margin: 0.5rem;
        }

        .modal-body {
            padding: 12px;
        }

        .modal-footer {
            padding: 8px 12px;
        }

        .btn-success, .btn-danger, .btn-secondary {
            padding: 5px 12px;
            font-size: 0.8rem;
        }

        /* إضافة فاصل بين رأس الصفحة والتبويبات للهواتف فقط */
        .admin-concerns-container .page-header {
            margin-bottom: 15px !important;
        }

        /* تعديل المسافة بين التبويبات والمحتوى */
        #concernsTabsContent {
            margin-top: 15px !important;
        }

        /* إضافة مسافة بين رأس الصفحة وشريط التبويبات */
        .admin-concerns-container .nav-tabs {
            margin-top: 15px !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    <div class="admin-concerns-container">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <h1>إدارة انشغالات الأساتذة</h1>
            <p class="page-description mb-0">
                من خلال هذه الصفحة يمكنك مراجعة انشغالات الأساتذة والموافقة عليها أو رفضها.
            </p>
        </div>

        <!-- التبويبات -->
        <ul class="nav nav-tabs" id="concernsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button" role="tab" aria-controls="pending" aria-selected="true">
                    <i class="fas fa-clock me-2" style="font-size: 0.85rem;"></i> قيد المراجعة
                    {% if pending_concerns %}
                        <span class="badge bg-warning rounded-pill ms-2">{{ pending_concerns|length }}</span>
                    {% endif %}
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="approved-tab" data-bs-toggle="tab" data-bs-target="#approved" type="button" role="tab" aria-controls="approved" aria-selected="false">
                    <i class="fas fa-check-circle me-2" style="font-size: 0.85rem;"></i> تمت الموافقة
                    {% if approved_concerns %}
                        <span class="badge bg-success rounded-pill ms-2">{{ approved_concerns|length }}</span>
                    {% endif %}
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="rejected-tab" data-bs-toggle="tab" data-bs-target="#rejected" type="button" role="tab" aria-controls="rejected" aria-selected="false">
                    <i class="fas fa-times-circle me-2" style="font-size: 0.85rem;"></i> مرفوضة
                    {% if rejected_concerns %}
                        <span class="badge bg-danger rounded-pill ms-2">{{ rejected_concerns|length }}</span>
                    {% endif %}
                </button>
            </li>
        </ul>

        <!-- محتوى التبويبات -->
        <div class="tab-content" id="concernsTabsContent">
            <!-- تبويب الانشغالات قيد المراجعة -->
            <div class="tab-pane fade show active" id="pending" role="tabpanel" aria-labelledby="pending-tab">
                {% if pending_concerns %}
                    {% for concern in pending_concerns %}
                        <div class="concern-card" id="concern-{{ concern.id }}">
                            <div class="concern-header">
                                <h3 class="concern-title">{{ concern.title }}</h3>
                                <span class="concern-status status-pending">
                                    <i class="fas fa-clock" style="font-size: 0.75rem;"></i> قيد المراجعة
                                </span>
                            </div>
                            <div class="concern-meta">
                                <div class="concern-teacher">
                                    <i class="fas fa-user"></i>
                                    <span>{{ concern.teacher.teacher_name }}</span>
                                </div>
                                <div class="concern-date">
                                    <i class="far fa-calendar-alt"></i>
                                    <span>{{ concern.created_at.strftime('%Y-%m-%d') }}</span>
                                </div>
                            </div>
                            <div class="concern-body">
                                <div class="concern-content">{{ concern.content }}</div>
                                <div class="concern-workplace">
                                    <strong>مكان العمل:</strong> {{ concern.teacher.workplace }}
                                </div>
                            </div>
                            <div class="concern-actions">
                                <button class="btn-delete" onclick="openDeleteModal({{ concern.id }}, 'pending')">
                                    حذف <i class="fas fa-trash-alt"></i>
                                </button>
                                <button class="btn-reject" onclick="openRejectModal({{ concern.id }})">
                                    رفض <i class="fas fa-times-circle"></i>
                                </button>
                                <button class="btn-approve" onclick="openApproveModal({{ concern.id }})">
                                    موافقة <i class="fas fa-check-circle"></i>
                                </button>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="no-concerns">
                        <i class="far fa-clock"></i>
                        <h3>لا توجد انشغالات قيد المراجعة</h3>
                        <p>لم يتم إضافة أي انشغالات جديدة تنتظر المراجعة.</p>
                    </div>
                {% endif %}
            </div>

            <!-- تبويب الانشغالات المقبولة -->
            <div class="tab-pane fade" id="approved" role="tabpanel" aria-labelledby="approved-tab">
                {% if approved_concerns %}
                    {% for concern in approved_concerns %}
                        <div class="concern-card">
                            <div class="concern-header">
                                <h3 class="concern-title">{{ concern.title }}</h3>
                                <span class="concern-status status-approved">
                                    <i class="fas fa-check-circle" style="font-size: 0.75rem;"></i> تمت الموافقة
                                </span>
                            </div>
                            <div class="concern-meta">
                                <div class="concern-teacher">
                                    <i class="fas fa-user"></i>
                                    <span>{{ concern.teacher.teacher_name }}</span>
                                </div>
                                <div class="concern-date">
                                    <i class="far fa-calendar-alt"></i>
                                    <span>{{ concern.created_at.strftime('%Y-%m-%d') }}</span>
                                </div>
                            </div>
                            <div class="concern-body">
                                <div class="concern-content">{{ concern.content }}</div>
                                <div class="concern-workplace">
                                    <strong>مكان العمل:</strong> {{ concern.teacher.workplace }}
                                </div>
                                {% if concern.admin_comment %}
                                    <div class="concern-comment">
                                        <div class="comment-header">
                                            <div>
                                                <i class="fas fa-comment"></i>
                                                <span>تعليق المراجع</span>
                                            </div>
                                            <div>
                                                <i class="far fa-clock"></i>
                                                <span>{{ concern.reviewed_at.strftime('%Y-%m-%d') }}</span>
                                            </div>
                                        </div>
                                        <div class="comment-content">{{ concern.admin_comment }}</div>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="concern-actions">
                                <button class="btn-delete" onclick="openDeleteModal({{ concern.id }}, 'approved')">
                                    حذف <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="no-concerns">
                        <i class="far fa-check-circle"></i>
                        <h3>لا توجد انشغالات تمت الموافقة عليها</h3>
                        <p>لم تتم الموافقة على أي انشغالات حتى الآن.</p>
                    </div>
                {% endif %}
            </div>

            <!-- تبويب الانشغالات المرفوضة -->
            <div class="tab-pane fade" id="rejected" role="tabpanel" aria-labelledby="rejected-tab">
                {% if rejected_concerns %}
                    {% for concern in rejected_concerns %}
                        <div class="concern-card">
                            <div class="concern-header">
                                <h3 class="concern-title">{{ concern.title }}</h3>
                                <span class="concern-status status-rejected">
                                    <i class="fas fa-times-circle" style="font-size: 0.75rem;"></i> مرفوضة
                                </span>
                            </div>
                            <div class="concern-meta">
                                <div class="concern-teacher">
                                    <i class="fas fa-user"></i>
                                    <span>{{ concern.teacher.teacher_name }}</span>
                                </div>
                                <div class="concern-date">
                                    <i class="far fa-calendar-alt"></i>
                                    <span>{{ concern.created_at.strftime('%Y-%m-%d') }}</span>
                                </div>
                            </div>
                            <div class="concern-body">
                                <div class="concern-content">{{ concern.content }}</div>
                                <div class="concern-workplace">
                                    <strong>مكان العمل:</strong> {{ concern.teacher.workplace }}
                                </div>
                                {% if concern.admin_comment %}
                                    <div class="concern-comment">
                                        <div class="comment-header">
                                            <div>
                                                <i class="fas fa-comment"></i>
                                                <span>سبب الرفض</span>
                                            </div>
                                            <div>
                                                <i class="far fa-clock"></i>
                                                <span>{{ concern.reviewed_at.strftime('%Y-%m-%d') }}</span>
                                            </div>
                                        </div>
                                        <div class="comment-content">{{ concern.admin_comment }}</div>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="concern-actions">
                                <button class="btn-delete" onclick="openDeleteModal({{ concern.id }}, 'rejected')">
                                    حذف <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="no-concerns">
                        <i class="far fa-times-circle"></i>
                        <h3>لا توجد انشغالات مرفوضة</h3>
                        <p>لم يتم رفض أي انشغالات بعد.</p>
                    </div>
                {% endif %}
            </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج الموافقة على الانشغال -->
<div class="modal fade" id="approveModal" tabindex="-1" aria-labelledby="approveModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approveModalLabel">
                    <i class="fas fa-check-circle me-1" style="font-size: 0.85rem;"></i> الموافقة على الانشغال
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="approveForm">
                    <input type="hidden" id="approveConcernId" name="concern_id">
                    <div class="mb-3">
                        <label for="approveComment" class="form-label">تعليق (اختياري)</label>
                        <textarea class="form-control" id="approveComment" name="comment" rows="3" placeholder="أضف تعليقًا إذا كنت ترغب في ذلك..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1" style="font-size: 0.8rem;"></i> إلغاء
                </button>
                <button type="button" class="btn btn-success" id="confirmApprove">
                    <i class="fas fa-check me-1" style="font-size: 0.8rem;"></i> تأكيد
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نموذج رفض الانشغال -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectModalLabel">
                    <i class="fas fa-times-circle me-1" style="font-size: 0.85rem;"></i> رفض الانشغال
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="rejectForm">
                    <input type="hidden" id="rejectConcernId" name="concern_id">
                    <div class="mb-3">
                        <label for="rejectComment" class="form-label">سبب الرفض (اختياري)</label>
                        <textarea class="form-control" id="rejectComment" name="comment" rows="3" placeholder="اذكر سبب رفض الانشغال..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1" style="font-size: 0.8rem;"></i> إلغاء
                </button>
                <button type="button" class="btn btn-danger" id="confirmReject">
                    <i class="fas fa-ban me-1" style="font-size: 0.8rem;"></i> تأكيد
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نموذج حذف الانشغال -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-trash-alt me-1" style="font-size: 0.85rem;"></i> حذف الانشغال
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="deleteForm">
                    <input type="hidden" id="deleteConcernId" name="concern_id">
                    <input type="hidden" id="deleteStatus" name="status">
                    <p>هل أنت متأكد من رغبتك في حذف هذا الانشغال؟</p>
                    <p class="text-danger"><strong>تحذير:</strong> لا يمكن التراجع عن هذا الإجراء.</p>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1" style="font-size: 0.8rem;"></i> إلغاء
                </button>
                <button type="button" class="btn btn-danger" id="confirmDelete">
                    <i class="fas fa-trash-alt me-1" style="font-size: 0.8rem;"></i> تأكيد الحذف
                </button>
            </div>
        </div>
    </div>
</div>

<!-- سكريبت للتعامل مع الموافقة والرفض والحذف -->
<script>
    // دالة لتحويل الروابط في النص إلى روابط قابلة للنقر
    function linkifyText(text) {
        if (!text) return '';

        // تعريف التعبيرات المنتظمة للروابط
        const urlRegex = {
            // للروابط التي تبدأ بـ http:// أو https://
            protocol: /(\b(https?):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gim,

            // للروابط التي تبدأ بـ www.
            www: /(^|[^\/])(www\.[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gim,

            // للروابط التي تبدأ بـ domain.tld
            domain: /(^|\s)([a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]\.[a-zA-Z]{2,}(\.[a-zA-Z]{2,})?(\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])?)/gim
        };

        // نسخة من النص الأصلي للعمل عليها
        let result = text;

        // استبدال الروابط التي تبدأ بـ http:// أو https://
        result = result.replace(urlRegex.protocol, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>');

        // استبدال الروابط التي تبدأ بـ www.
        result = result.replace(urlRegex.www, '$1<a href="http://$2" target="_blank" rel="noopener noreferrer">$2</a>');

        // استبدال الروابط التي تبدأ بـ domain.tld
        result = result.replace(urlRegex.domain, function(match, p1, p2) {
            // تجنب استبدال الروابط التي تم استبدالها بالفعل
            if (match.indexOf('<a href="') !== -1) {
                return match;
            }
            // تجنب استبدال الروابط التي تبدأ بـ www. (تم استبدالها في الخطوة السابقة)
            if (p2.startsWith('www.')) {
                return match;
            }
            return p1 + '<a href="http://' + p2 + '" target="_blank" rel="noopener noreferrer">' + p2 + '</a>';
        });

        return result;
    }

    // متغيرات النماذج
    let approveModal, rejectModal, deleteModal;

    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة النماذج
        approveModal = new bootstrap.Modal(document.getElementById('approveModal'));
        rejectModal = new bootstrap.Modal(document.getElementById('rejectModal'));
        deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));

        // تحويل الروابط في محتوى الانشغالات إلى روابط قابلة للنقر
        document.querySelectorAll('.concern-content').forEach(contentElement => {
            const originalContent = contentElement.textContent;
            contentElement.innerHTML = linkifyText(originalContent);
        });

        // تحويل الروابط في تعليقات المشرفين إلى روابط قابلة للنقر
        document.querySelectorAll('.comment-content').forEach(commentElement => {
            const originalContent = commentElement.textContent;
            commentElement.innerHTML = linkifyText(originalContent);
        });

        // إضافة مستمعي الأحداث لأزرار التأكيد
        document.getElementById('confirmApprove').addEventListener('click', function() {
            reviewConcern('approved');
        });

        document.getElementById('confirmReject').addEventListener('click', function() {
            reviewConcern('rejected');
        });

        document.getElementById('confirmDelete').addEventListener('click', function() {
            deleteConcern();
        });
    });

    // دالة لفتح نموذج الموافقة
    function openApproveModal(concernId) {
        document.getElementById('approveConcernId').value = concernId;
        approveModal.show();
    }

    // دالة لفتح نموذج الرفض
    function openRejectModal(concernId) {
        document.getElementById('rejectConcernId').value = concernId;
        rejectModal.show();
    }

    // دالة لفتح نموذج الحذف
    function openDeleteModal(concernId, status) {
        document.getElementById('deleteConcernId').value = concernId;
        document.getElementById('deleteStatus').value = status;
        deleteModal.show();
    }

    // دالة لمراجعة الانشغال (موافقة أو رفض)
    function reviewConcern(status) {
        // تحديد النموذج والمعرف حسب الحالة
        const isApprove = status === 'approved';
        const concernId = isApprove ?
            document.getElementById('approveConcernId').value :
            document.getElementById('rejectConcernId').value;
        const comment = isApprove ?
            document.getElementById('approveComment').value :
            document.getElementById('rejectComment').value;

        // الحصول على رمز CSRF من وسم meta
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (!csrfToken) {
            console.error('لم يتم العثور على رمز CSRF');
            alert('حدث خطأ في إعداد الأمان. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
            return;
        }

        // إنشاء بيانات النموذج
        const formData = new FormData();
        formData.append('status', status);
        formData.append('comment', comment);
        formData.append('csrf_token', csrfToken);

        // إرسال الطلب إلى الخادم
        fetch(`/admin/review_concern/${concernId}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // إغلاق النموذج
                if (isApprove) {
                    approveModal.hide();
                } else {
                    rejectModal.hide();
                }

                // إزالة الانشغال من القائمة
                const concernElement = document.getElementById(`concern-${concernId}`);
                if (concernElement) {
                    concernElement.remove();
                }

                // إعادة تحميل الصفحة لتحديث جميع التبويبات
                setTimeout(() => {
                    window.location.reload();
                }, 500);
            } else {
                alert(data.message || 'حدث خطأ أثناء مراجعة الانشغال');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء مراجعة الانشغال');
        });
    }

    // دالة لحذف الانشغال
    function deleteConcern() {
        // الحصول على معرف الانشغال وحالته
        const concernId = document.getElementById('deleteConcernId').value;
        const status = document.getElementById('deleteStatus').value;

        // الحصول على رمز CSRF من وسم meta
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (!csrfToken) {
            console.error('لم يتم العثور على رمز CSRF');
            alert('حدث خطأ في إعداد الأمان. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
            return;
        }

        // إنشاء بيانات النموذج
        const formData = new FormData();
        formData.append('csrf_token', csrfToken);

        // إرسال الطلب إلى الخادم
        fetch(`/admin/delete_concern/${concernId}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // إغلاق النموذج
                deleteModal.hide();

                // إزالة الانشغال من القائمة
                if (status === 'pending') {
                    const concernElement = document.getElementById(`concern-${concernId}`);
                    if (concernElement) {
                        concernElement.remove();
                    }
                } else {
                    // إعادة تحميل الصفحة لتحديث جميع التبويبات
                    setTimeout(() => {
                        window.location.reload();
                    }, 500);
                }
            } else {
                alert(data.message || 'حدث خطأ أثناء حذف الانشغال');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف الانشغال');
        });
    }
</script>
{% endblock %}
