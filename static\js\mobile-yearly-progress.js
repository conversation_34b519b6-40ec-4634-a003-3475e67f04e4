/**
 * ملف JavaScript لتحسين تجربة المستخدم في قسم التدرج السنوي على الهواتف المحمولة
 */

document.addEventListener('DOMContentLoaded', function() {
    // ربط عناصر واجهة المستخدم المخصصة للهاتف مع العناصر الأصلية
    
    // ربط قائمة اختيار السنة الدراسية
    const yearSelector = document.getElementById('year-selector');
    const yearSelectorMobile = document.getElementById('year-selector-mobile');
    
    if (yearSelector && yearSelectorMobile) {
        // نسخ الخيارات المحددة من العنصر الأصلي إلى العنصر المخصص للهاتف
        yearSelectorMobile.value = yearSelector.value;
        
        // إضافة مستمع حدث للعنصر المخصص للهاتف
        yearSelectorMobile.addEventListener('change', function() {
            yearSelector.value = this.value;
            // تشغيل حدث التغيير على العنصر الأصلي لتنفيذ أي وظائف مرتبطة به
            const event = new Event('change');
            yearSelector.dispatchEvent(event);
        });
        
        // إضافة مستمع حدث للعنصر الأصلي
        yearSelector.addEventListener('change', function() {
            yearSelectorMobile.value = this.value;
        });
    }
    
    // ربط حقل عدد الأسابيع في شهر سبتمبر
    const septemberWeeks = document.getElementById('september-weeks');
    const septemberWeeksMobile = document.getElementById('september-weeks-mobile');
    
    if (septemberWeeks && septemberWeeksMobile) {
        // نسخ القيمة من العنصر الأصلي إلى العنصر المخصص للهاتف
        septemberWeeksMobile.value = septemberWeeks.value;
        
        // إضافة مستمع حدث للعنصر المخصص للهاتف
        septemberWeeksMobile.addEventListener('input', function() {
            septemberWeeks.value = this.value;
        });
        
        // إضافة مستمع حدث للعنصر الأصلي
        septemberWeeks.addEventListener('input', function() {
            septemberWeeksMobile.value = this.value;
        });
    }
    
    // ربط زر حفظ عدد الأسابيع
    const saveSeptemberWeeks = document.getElementById('save-september-weeks');
    const saveSeptemberWeeksMobile = document.getElementById('save-september-weeks-mobile');
    
    if (saveSeptemberWeeks && saveSeptemberWeeksMobile) {
        // إضافة مستمع حدث للعنصر المخصص للهاتف
        saveSeptemberWeeksMobile.addEventListener('click', function() {
            // تشغيل حدث النقر على العنصر الأصلي
            saveSeptemberWeeks.click();
        });
    }
    
    // ربط زر تفريغ الحقول
    const clearLessonsBtn = document.getElementById('clear-lessons-btn');
    const clearLessonsBtnMobile = document.getElementById('clear-lessons-btn-mobile');
    
    if (clearLessonsBtn && clearLessonsBtnMobile) {
        // إضافة مستمع حدث للعنصر المخصص للهاتف
        clearLessonsBtnMobile.addEventListener('click', function() {
            // تشغيل حدث النقر على العنصر الأصلي
            clearLessonsBtn.click();
        });
    }
    
    // ربط زر حفظ التدرجات
    const saveProgressBtn = document.getElementById('save-progress-btn');
    const saveProgressBtnMobile = document.getElementById('save-progress-btn-mobile');
    
    if (saveProgressBtn && saveProgressBtnMobile) {
        // إضافة مستمع حدث للعنصر المخصص للهاتف
        saveProgressBtnMobile.addEventListener('click', function() {
            // تشغيل حدث النقر على العنصر الأصلي
            saveProgressBtn.click();
        });
    }
    
    // تحسين تجربة المستخدم في الجدول على الهواتف المحمولة
    const tableResponsive = document.querySelector('#yearly-progress-section .table-responsive');
    
    if (tableResponsive) {
        // إضافة تلميح للمستخدم للإشارة إلى إمكانية التمرير
        const scrollHint = document.createElement('div');
        scrollHint.className = 'scroll-hint d-md-none';
        scrollHint.style.textAlign = 'center';
        scrollHint.style.fontSize = '0.8rem';
        scrollHint.style.color = '#6c757d';
        scrollHint.style.marginBottom = '8px';
        scrollHint.innerHTML = '<i class="fas fa-arrows-left-right me-1"></i> مرر أفقياً لرؤية كامل الجدول';
        
        // إضافة التلميح قبل الجدول
        tableResponsive.parentNode.insertBefore(scrollHint, tableResponsive);
    }
});
