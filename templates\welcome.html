{% extends "base.html" %}
{% block title %}الصفحة الرئيسية{% endblock %}
{% block content %}
<div class="container-fluid py-3 px-2 px-md-4">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" id="csrf-token">

    <div class="row g-2">
        <!-- القسم الجانبي - الأقسام المسندة وملخص التوقيت -->
        <div class="col-md-3 order-md-0">
            <div class="card shadow-sm mb-3 sidebar-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-clipboard-list me-2"></i>ملخص جدول التوقيت</h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6 class="text-primary mb-3 sidebar-section-title">
                            <i class="fas fa-chalkboard me-2"></i>الأقسام المسندة
                        </h6>
                        <div class="assigned-classes-container" id="assigned-classes-buttons">
                            <!-- سيتم ملء هذا القسم بالجافا سكريبت -->
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="text-primary mb-3 sidebar-section-title">
                            <i class="fas fa-clock me-2"></i>إحصائيات التدريس
                        </h6>
                        <div class="teaching-stats">
                            <div class="stat-item">
                                <span class="stat-label">عدد ساعات التدريس:</span>
                                <span class="stat-value" id="teaching-hours-count">0</span>
                            </div>

                            <div class="stat-item pedagogy-day">
                                <span class="stat-label">اليوم البيداغوجي:</span>
                                <div class="select-container">
                                    <select id="pedagogy-day-select" class="form-select form-select-sm no-arrow">
                                        <option value="">-- اختر اليوم --</option>
                                        <option value="0">الأحد</option>
                                        <option value="1">الإثنين</option>
                                        <option value="2">الثلاثاء</option>
                                        <option value="3">الأربعاء</option>
                                        <option value="4">الخميس</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form action="{{ url_for('update_schedule') }}" method="POST" id="schedule-form" class="mb-3">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <input type="hidden" name="schedule-data" id="hidden-schedule-data" value="">
                        <div class="d-grid gap-2">
                            <button type="submit" id="save-schedule-btn" class="btn btn-primary py-2">
                                <i class="fas fa-save me-1"></i> حفظ جدول التوقيت
                            </button>

                            <button type="button" id="clear-schedule-btn" class="btn btn-outline-danger py-2">
                                <i class="fas fa-trash-alt me-1"></i> تفريغ الجدول
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- القسم الأوسط - جدول التوقيت الأسبوعي -->
        <div class="col-md-9 order-md-1">
            <!-- قسم جدول التوقيت الأسبوعي -->
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white py-2">
                    <h5 class="mb-0">جدول التوقيت الأسبوعي</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info small py-1 mb-2">
                        <i class="fas fa-info-circle me-1"></i>
                        يمكنك النقر على خلايا التوقيت (العمود الأول) لتعديل توقيت الحصص حسب احتياجاتك.
                    </div>
                    <div class="table-responsive mobile-table-container">
                        <table class="table table-bordered text-center compact-schedule" id="schedule-table">
                            <thead class="table-light">
                                <tr>
                                    <th class="time-header">التوقيت</th>
                                    <th class="day-header">الأحد</th>
                                    <th class="day-header">الإثنين</th>
                                    <th class="day-header">الثلاثاء</th>
                                    <th class="day-header">الأربعاء</th>
                                    <th class="day-header">الخميس</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- الفترة الصباحية -->
                                <tr>
                                    <td class="table-light time-cell" contenteditable="true" data-original-time="08:00">08:00 - 09:00</td>
                                    <td data-day="0" data-hour="8" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="1" data-hour="8" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="2" data-hour="8" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="3" data-hour="8" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="4" data-hour="8" class="schedule-cell" contenteditable="true"></td>
                                </tr>
                                <tr>
                                    <td class="table-light time-cell" contenteditable="true" data-original-time="09:00">09:00 - 10:00</td>
                                    <td data-day="0" data-hour="9" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="1" data-hour="9" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="2" data-hour="9" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="3" data-hour="9" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="4" data-hour="9" class="schedule-cell" contenteditable="true"></td>
                                </tr>
                                <tr>
                                    <td class="table-light time-cell" contenteditable="true" data-original-time="10:00">10:00 - 11:00</td>
                                    <td data-day="0" data-hour="10" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="1" data-hour="10" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="2" data-hour="10" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="3" data-hour="10" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="4" data-hour="10" class="schedule-cell" contenteditable="true"></td>
                                </tr>
                                <tr>
                                    <td class="table-light time-cell" contenteditable="true" data-original-time="11:00">11:00 - 12:00</td>
                                    <td data-day="0" data-hour="11" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="1" data-hour="11" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="2" data-hour="11" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="3" data-hour="11" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="4" data-hour="11" class="schedule-cell" contenteditable="true"></td>
                                </tr>

                                <!-- فاصل بين الفترتين -->
                                <tr class="bg-white">
                                    <td colspan="6" class="text-center">
                                        <strong>فترة الراحة</strong>
                                    </td>
                                </tr>

                                <!-- الفترة المسائية -->
                                <tr>
                                    <td class="table-light time-cell" contenteditable="true" data-original-time="13:00">13:00 - 14:00</td>
                                    <td data-day="0" data-hour="13" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="1" data-hour="13" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="2" data-hour="13" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="3" data-hour="13" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="4" data-hour="13" class="schedule-cell" contenteditable="true"></td>
                                </tr>
                                <tr>
                                    <td class="table-light time-cell" contenteditable="true" data-original-time="14:00">14:00 - 15:00</td>
                                    <td data-day="0" data-hour="14" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="1" data-hour="14" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="2" data-hour="14" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="3" data-hour="14" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="4" data-hour="14" class="schedule-cell" contenteditable="true"></td>
                                </tr>
                                <tr>
                                    <td class="table-light time-cell" contenteditable="true" data-original-time="15:00">15:00 - 16:00</td>
                                    <td data-day="0" data-hour="15" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="1" data-hour="15" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="2" data-hour="15" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="3" data-hour="15" class="schedule-cell" contenteditable="true"></td>
                                    <td data-day="4" data-hour="15" class="schedule-cell" contenteditable="true"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// معالج أحداث تحميل المستند
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل الصفحة بنجاح وتهيئة التطبيق');

    // تحميل بيانات جدول التوقيت
    console.log('جاري تحميل بيانات الجدول عند بدء الصفحة...');
    loadScheduleData();

    // تهيئة أحداث خلايا الجدول
    document.querySelectorAll('.schedule-cell').forEach(function(cell) {
        cell.addEventListener('focus', function() {
            this.classList.add('cell-editing');
            this.style.backgroundColor = '#f8f9fa';
        });

        cell.addEventListener('blur', function() {
            this.classList.remove('cell-editing');
            this.style.backgroundColor = '';

            // تطبيق التظليل إذا كانت الخلية تحتوي على بيانات
            const content = this.textContent.trim();
            if (content) {
                this.classList.add('filled-cell');
            } else {
                this.classList.remove('filled-cell');
            }

            // تحديث قائمة الأقسام عند تعديل خلية
            updateAssignedClassesList();
        });

        cell.addEventListener('keydown', function(e) {
            const content = this.textContent;
            if (content.length >= 10 && e.keyCode !== 8 && e.keyCode !== 46 && e.keyCode !== 37 && e.keyCode !== 39) {
                e.preventDefault();
            }
        });
    });

    // إضافة معالجات أحداث لخلايا التوقيت
    document.querySelectorAll('.time-cell').forEach(function(cell) {
        // إزالة جميع المستمعين السابقين لتجنب التكرار
        const clone = cell.cloneNode(true);
        cell.parentNode.replaceChild(clone, cell);
        cell = clone;

        // حفظ القيمة الأصلية عند التركيز
        cell.addEventListener('focus', function() {
            this.dataset.prevValue = this.textContent;
            console.log('تركيز على خلية توقيت:', {
                originalTime: this.getAttribute('data-original-time'),
                currentValue: this.textContent
            });
        });

        // منع إدخال أكثر من رقمين للساعات أو الدقائق
        cell.addEventListener('keydown', function(e) {
            // السماح بمفاتيح التنقل والحذف
            const allowedKeys = [8, 46, 37, 39, 9, 13, 32, 58, 45]; // Backspace, Delete, Left, Right, Tab, Enter, Space, :, -
            if (allowedKeys.includes(e.keyCode)) {
                return;
            }

            // السماح فقط بالأرقام
            if ((e.keyCode < 48 || e.keyCode > 57) && (e.keyCode < 96 || e.keyCode > 105)) {
                e.preventDefault();
                return;
            }

            // الحصول على موضع المؤشر والنص المحدد
            const selection = window.getSelection();
            const range = selection.getRangeAt(0);
            const selectedText = range.toString();

            // إذا كان هناك نص محدد، فسيتم استبداله بالحرف المكتوب
            if (selectedText.length > 0) {
                return;
            }

            // تحليل النص الحالي
            const text = this.textContent;
            const cursorPosition = range.startOffset;

            // تحديد ما إذا كان المؤشر في جزء الساعات أو الدقائق
            const parts = text.split('-').map(part => part.trim());

            let isInFirstPart = true;
            let hoursPart = '';
            let minutesPart = '';

            // تحديد موضع المؤشر (في الجزء الأول أو الثاني من التوقيت)
            if (text.includes('-')) {
                const firstPartEndIndex = text.indexOf('-');
                isInFirstPart = cursorPosition <= firstPartEndIndex;
            }

            // فحص الجزء الذي يكتب فيه المستخدم
            if (isInFirstPart && parts[0]) {
                // التحقق من الساعات والدقائق في الجزء الأول
                if (parts[0].includes(':')) {
                    const timeParts = parts[0].split(':');
                    hoursPart = timeParts[0];
                    minutesPart = timeParts[1] || '';

                    // منع إدخال أكثر من رقمين للساعات
                    if (hoursPart.length >= 2 && cursorPosition <= hoursPart.length && !selectedText) {
                        if (text.charAt(cursorPosition-1) !== ':') {
                            e.preventDefault();
                        }
                    }

                    // منع إدخال أكثر من رقمين للدقائق
                    if (minutesPart.length >= 2 && cursorPosition > hoursPart.length + 1) {
                        e.preventDefault();
                    }
                }
            } else if (parts[1]) {
                // التحقق من الساعات والدقائق في الجزء الثاني
                if (parts[1].includes(':')) {
                    const timeParts = parts[1].split(':');
                    hoursPart = timeParts[0];
                    minutesPart = timeParts[1] || '';

                    // منع إدخال أكثر من رقمين للساعات
                    if (hoursPart.length >= 2 && cursorPosition > text.indexOf('-') + 1 &&
                        cursorPosition <= text.indexOf('-') + 1 + hoursPart.length && !selectedText) {
                        if (text.charAt(cursorPosition-1) !== ':') {
                            e.preventDefault();
                        }
                    }

                    // منع إدخال أكثر من رقمين للدقائق
                    const secondColonPosition = text.indexOf(':', text.indexOf('-'));
                    if (minutesPart.length >= 2 && cursorPosition > secondColonPosition) {
                        e.preventDefault();
                    }
                }
            }
        });

        // التحقق من صحة القيمة عند فقدان التركيز
        cell.addEventListener('blur', function() {
            const newValue = this.textContent;
            const prevValue = this.dataset.prevValue;
            const originalTime = this.getAttribute('data-original-time');

            console.log('فقدان التركيز من خلية توقيت:', {
                originalTime: originalTime,
                prevValue: prevValue,
                newValue: newValue,
                changed: newValue !== prevValue
            });

            // إذا تغيرت القيمة، تطبيق تأثير بصري
            if (newValue !== prevValue) {
                // إضافة تأثير بصري مؤقت للخلية
                this.style.backgroundColor = '#e8f7fe';

                // تلاشي التأثير بعد فترة
                setTimeout(() => {
                    this.style.backgroundColor = '';
                }, 2000);
            }

            // تطبيق تنسيق متسق على التوقيت
            if (!isValidTimeRange(newValue)) {
                // إذا كانت القيمة غير صالحة، استعادة القيمة السابقة
                if (prevValue && isValidTimeRange(prevValue)) {
                    this.textContent = prevValue;
                    showAlert('تم استعادة القيمة السابقة لأن التنسيق غير صحيح', 'warning');
                    console.warn('استعادة القيمة السابقة بسبب تنسيق غير صالح:', {
                        originalValue: newValue,
                        restoredValue: prevValue
                    });
                } else {
                    // إذا كانت القيمة السابقة غير صالحة أيضًا، استعادة القيمة الافتراضية
                    const hour = parseInt(originalTime.split(':')[0], 10);
                    const nextHour = (hour + 1) % 24;
                    const defaultValue = `${originalTime} - ${nextHour.toString().padStart(2, '0')}:00`;
                    this.textContent = defaultValue;
                    showAlert('تم استعادة القيمة الافتراضية للتوقيت', 'warning');
                    console.warn('استعادة القيمة الافتراضية:', {
                        originalValue: newValue,
                        restoredValue: defaultValue
                    });
                }
            } else {
                // إذا كانت القيمة صالحة، تطبيق التنسيق المتسق
                const formattedValue = formatTimeRange(newValue);
                if (formattedValue !== newValue) {
                    console.log('تطبيق تنسيق متسق على التوقيت:', {
                        originalValue: newValue,
                        formattedValue: formattedValue
                    });
                    this.textContent = formattedValue;
                }
            }
        });
    });
});

// التحقق من صحة نطاق الوقت (HH:MM - HH:MM)
function isValidTimeRange(timeRange) {
    // تنسيق مبسط: hh:mm - hh:mm
    const regex = /^([0-1]?[0-9]|2[0-3]):([0-5][0-9])\s*-\s*([0-1]?[0-9]|2[0-3]):([0-5][0-9])$/;

    if (!regex.test(timeRange.trim())) {
        return false;
    }

    // التحقق من عدم زيادة الأرقام عن رقمين
    const parts = timeRange.split('-').map(part => part.trim());

    for (const part of parts) {
        const [hours, minutes] = part.split(':');

        // التحقق من أن الساعات والدقائق لا تزيد عن رقمين
        if (hours.length > 2 || minutes.length > 2) {
            return false;
        }
    }

    return true;
}

// تنسيق نطاق الوقت بشكل متسق
function formatTimeRange(timeRange) {
    if (!isValidTimeRange(timeRange)) return timeRange;

    // تقسيم النطاق إلى وقت البداية والنهاية
    const parts = timeRange.split('-').map(part => part.trim());

    // تنسيق كل جزء للتأكد من وجود أصفار في البداية (مثلا 8:0 -> 08:00)
    const formattedParts = parts.map(part => {
        const [hours, minutes] = part.split(':').map(n => parseInt(n, 10));
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    });

    // إعادة تجميع نطاق الوقت المنسق
    return `${formattedParts[0]} - ${formattedParts[1]}`;
}

// الحصول على الوقت التالي (بزيادة ساعة كاملة بشكل افتراضي)
function getNextTimeIncrement(timeStr, incrementMinutes = 60) {
    const [hoursStr, minutesStr] = timeStr.split(':');
    const hours = parseInt(hoursStr, 10);
    const minutes = parseInt(minutesStr, 10);

    // حساب الوقت الجديد
    let totalMinutes = hours * 60 + minutes + incrementMinutes;
    const newHours = Math.floor(totalMinutes / 60);
    const newMinutes = totalMinutes % 60;

    // تنسيق الوقت الجديد
    return `${newHours.toString().padStart(2, '0')}:${newMinutes.toString().padStart(2, '0')}`;
}

// تحميل بيانات الجدول
function loadScheduleData() {
    console.log('جاري تحميل بيانات الجدول...');

    // إضافة مؤشر تحميل مؤقت
    const cells = document.querySelectorAll('.schedule-cell');
    cells.forEach(cell => {
        cell.style.opacity = '0.5';
    });

    fetch('{{ url_for("get_schedule") }}')
        .then(response => {
            console.log('تم استلام استجابة:', response);
            if (!response.ok) {
                throw new Error('استجابة الشبكة غير صحيحة: ' + response.statusText);
            }
            return response.json();
        })
        .then(data => {
            console.log('البيانات المستلمة من الخادم:', data);

            // إعادة الخلايا إلى حالتها الطبيعية
            cells.forEach(cell => {
                cell.style.opacity = '1';
            });

            if (data.status === 'success') {
                // مسح جميع الخلايا أولاً
                document.querySelectorAll('.schedule-cell').forEach(cell => {
                    cell.textContent = '';
                });

                // ملء الخلايا بالبيانات
                if (data.data && data.data.schedule && data.data.schedule.length > 0) {
                    console.log(`تم العثور على ${data.data.schedule.length} عنصر من بيانات الجدول للعرض`);

                    data.data.schedule.forEach(item => {
                        const cell = document.querySelector(`.schedule-cell[data-day="${item.day}"][data-hour="${item.hour}"]`);
                        if (cell) {
                            cell.textContent = item.class_name;
                            // إزالة تأثير الخلفية المؤقت لأن الخلايا ستكون مظللة بواسطة filled-cell
                            console.log(`تم ملء الخلية [يوم=${item.day}, ساعة=${item.hour}] بقيمة "${item.class_name}"`);
                        } else {
                            console.warn(`لم يتم العثور على خلية مطابقة لـ [يوم=${item.day}, ساعة=${item.hour}]`);
                        }
                    });

                    // تحديث قائمة الأقسام المسندة
                    updateAssignedClassesList();

                    console.log('تم تحميل جدول التوقيت بنجاح');
                } else {
                    console.log('لا توجد بيانات جدول للعرض');
                    // تفريغ قائمة الأقسام في حالة عدم وجود بيانات
                    updateAssignedClassesList();
                }

                    // استعادة بيانات التوقيت إذا كانت موجودة
                if (data.data && data.data.times && data.data.times.length > 0) {
                        console.log(`تم العثور على ${data.data.times.length} عنصر من بيانات التوقيت للعرض`);

                    // إعادة خلايا التوقيت إلى القيم الافتراضية أولاً
                    document.querySelectorAll('.time-cell').forEach(cell => {
                        const originalTime = cell.getAttribute('data-original-time');
                        if (originalTime) {
                            // استخدام الصيغة الجديدة للتوقيت: HH:00 - (HH+1):00
                            const hour = parseInt(originalTime.split(':')[0], 10);
                            const nextHour = (hour + 1) % 24;
                            const defaultValue = `${originalTime} - ${nextHour.toString().padStart(2, '0')}:00`;
                            cell.textContent = defaultValue;
                        }
                    });

                    // تطبيق القيم المخصصة من الخادم
                        data.data.times.forEach(item => {
                            const timeCell = document.querySelector(`.time-cell[data-original-time="${item.original_time}"]`);
                            if (timeCell) {
                                timeCell.textContent = item.time_range;
                                console.log(`تم تحديث خلية التوقيت [${item.original_time}] إلى "${item.time_range}"`);
                        } else {
                            console.warn(`لم يتم العثور على خلية توقيت مطابقة لـ [${item.original_time}]`);
                        }
                    });
                } else {
                    console.log('لا توجد بيانات توقيت مخصصة للعرض');
                }
            } else {
                console.error('خطأ في تحميل جدول التوقيت:', data.message);
                showAlert('خطأ في تحميل جدول التوقيت: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل جدول التوقيت:', error);
            // إعادة الخلايا إلى حالتها الطبيعية في حالة وقوع خطأ
            cells.forEach(cell => {
                cell.style.opacity = '1';
            });
            showAlert('خطأ في تحميل جدول التوقيت: ' + error.message, 'danger');
        });
}

// وظيفة لتحديث قائمة الأقسام المسندة بناءً على بيانات الجدول
function updateAssignedClassesList() {
    const cells = document.querySelectorAll('.schedule-cell');
    let classNames = new Set();
    let teachingHours = 0;

    // جمع جميع الأقسام المسندة من الخلايا وتظليل الخلايا التي تحتوي على بيانات
    cells.forEach(cell => {
        const content = cell.textContent.trim();
        if (content) {
            classNames.add(content);
            teachingHours++;
            // تظليل الخلايا التي تحتوي على بيانات
            cell.classList.add('filled-cell');
        } else {
            // إزالة التظليل من الخلايا الفارغة
            cell.classList.remove('filled-cell');
        }
    });

    // تحويل المجموعة إلى مصفوفة وترتيبها
    const classesArray = Array.from(classNames).sort();

    // تحديث عدد ساعات التدريس والأقسام المختلفة
    document.getElementById('teaching-hours-count').textContent = teachingHours;

    // تحديث أزرار الأقسام المسندة
    const assignedClassesButtons = document.getElementById('assigned-classes-buttons');
    assignedClassesButtons.innerHTML = '';

    // إنشاء زر لكل قسم مسند
    if (classesArray.length > 0) {
        const colors = ['primary', 'success', 'info', 'danger', 'warning', 'secondary'];
        classesArray.forEach((className, index) => {
            const colorClass = colors[index % colors.length];
            const button = document.createElement('span');
            button.className = `class-badge bg-${colorClass}`;
            button.textContent = className;
            assignedClassesButtons.appendChild(button);
        });
    } else {
        assignedClassesButtons.innerHTML = '<div class="text-muted">لم يتم تعيين أي أقسام حتى الآن</div>';
    }
}

// وظيفة لإعداد بيانات الجدول للإرسال إلى الخادم
function prepareScheduleData(form) {
    const scheduleData = [];
    const cells = document.querySelectorAll('.schedule-cell');

    cells.forEach(cell => {
        const content = cell.textContent.trim();
        if (content) {
            const day = parseInt(cell.getAttribute('data-day'));
            const hour = parseInt(cell.getAttribute('data-hour'));

            scheduleData.push({
                day: day,
                hour: hour,
                class_name: content
            });
        }
    });

    console.log('جمع بيانات التوقيت...');
    // جمع بيانات التوقيت دائمًا حتى لو كان الجدول فارغًا
    const timeData = [];
    const timeCells = document.querySelectorAll('.time-cell');
    console.log('عدد خلايا التوقيت المحددة:', timeCells.length);

    timeCells.forEach((cell, index) => {
        const timeText = cell.textContent.trim();
        const originalTime = cell.getAttribute('data-original-time');

        console.log(`معالجة خلية التوقيت ${index+1}:`, {
            element: cell,
            originalTime: originalTime,
            timeText: timeText
        });

        // إضافة جميع بيانات التوقيت لضمان حفظها دائمًا
        if (originalTime && timeText) {
        timeData.push({
            original_time: originalTime,
            time_range: timeText
        });
        } else {
            console.warn('تخطي خلية توقيت غير صالحة:', {
                originalTime: originalTime,
                timeText: timeText
            });
        }
    });

    // تجميع كل البيانات في كائن واحد
    const allData = {
        schedule: scheduleData,
        times: timeData
    };

    console.log('بيانات التوقيت المجمعة:', timeData);
    console.log('إجمالي عدد عناصر التوقيت المرسلة:', timeData.length);

    // تحويل البيانات إلى JSON وتعيينها في حقل مخفي
    const jsonData = JSON.stringify(allData);
    document.getElementById('hidden-schedule-data').value = jsonData;
    console.log('تم تعيين بيانات الجدول في حقل النموذج المخفي');

    return true;
}

// وظيفة لعرض رسائل التنبيه
function showAlert(message, type) {
    // إنشاء عنصر التنبيه
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.setAttribute('role', 'alert');

    // إضافة محتوى التنبيه
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
    `;

    // البحث عن أو إنشاء حاوية التنبيهات
    let alertContainer = document.getElementById('alert-container');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alert-container';
        alertContainer.className = 'position-fixed top-0 start-50 translate-middle-x p-3';
        alertContainer.style.zIndex = '1050';
        document.body.appendChild(alertContainer);
    }

    // إضافة التنبيه إلى الحاوية
    alertContainer.appendChild(alertDiv);

    // إزالة التنبيه بعد فترة زمنية
    setTimeout(() => {
        alertDiv.classList.remove('show');
        setTimeout(() => {
            alertDiv.remove();
        }, 150);
    }, 5000);
}

// معالج حدث تقديم نموذج جدول التوقيت
document.addEventListener('DOMContentLoaded', function() {
    const scheduleForm = document.getElementById('schedule-form');
    if (scheduleForm) {
        scheduleForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // عرض مؤشر التحميل
            const saveButton = document.getElementById('save-schedule-btn');
            const originalText = saveButton.innerHTML;
            saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري الحفظ...';
            saveButton.disabled = true;

            // تجهيز البيانات
            prepareScheduleData(this);

            // إرسال النموذج باستخدام fetch API
            const formData = new FormData(this);

            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(function(response) {
                if (!response.ok) {
                    throw new Error('استجابة الشبكة غير صحيحة: ' + response.statusText);
                }
                return response.json();
            })
            .then(function(data) {
                console.log('استجابة الخادم:', data);
                // إعادة زر الحفظ إلى حالته الطبيعية
                saveButton.innerHTML = originalText;
                saveButton.disabled = false;

                if (data.status === 'success') {
                    // رسالة نجاح
                    showAlert('تم حفظ جدول التوقيت بنجاح', 'success');

                    // إضافة تأثير بصري للزر للتأكيد
                    saveButton.classList.add('btn-success');
                    saveButton.classList.remove('btn-primary');
                    saveButton.innerHTML = '<i class="fas fa-check me-1"></i> تم الحفظ بنجاح';

                    // إعادة زر إلى حالته الأصلية بعد ثانيتين
                    setTimeout(function() {
                        saveButton.classList.remove('btn-success');
                        saveButton.classList.add('btn-primary');
                        saveButton.innerHTML = originalText;
                    }, 2000);

                    // إعادة تحميل البيانات من الخادم
                    setTimeout(loadScheduleData, 500);
                } else {
                    showAlert(data.message || 'حدث خطأ غير معروف أثناء الحفظ', 'danger');
                }
            })
            .catch(function(error) {
                console.error('خطأ أثناء حفظ الجدول:', error);
                // إعادة زر الحفظ إلى حالته الطبيعية
                saveButton.innerHTML = originalText;
                saveButton.disabled = false;
                showAlert('حدث خطأ أثناء إرسال البيانات: ' + error.message, 'danger');
            });

            // مؤقت احتياطي لإعادة الزر إلى حالته الطبيعية
            setTimeout(function() {
                if (saveButton.disabled) {
                    saveButton.innerHTML = originalText;
                    saveButton.disabled = false;
                }
            }, 10000);
        });
    }

    // إضافة معالج حدث زر تفريغ الجدول
    const clearButton = document.getElementById('clear-schedule-btn');
    if (clearButton) {
        clearButton.addEventListener('click', function() {
            // عرض مربع تأكيد قبل التفريغ
            if (confirm('هل أنت متأكد من رغبتك في تفريغ جميع بيانات الجدول؟')) {
                // تفريغ جميع الخلايا
                document.querySelectorAll('.schedule-cell').forEach(cell => {
                    cell.textContent = '';
                });

                // إزالة اليوم البيداغوجي
                resetPedagogyDayHighlight();
                const pedagogyDaySelect = document.getElementById('pedagogy-day-select');
                if (pedagogyDaySelect) {
                    pedagogyDaySelect.value = '';
                    localStorage.removeItem('pedagogy_day');
                }

                // تحديث قائمة الأقسام والإحصائيات
                updateAssignedClassesList();

                // إضافة تأثير بصري مؤقت للزر
                clearButton.classList.add('btn-danger');
                clearButton.classList.remove('btn-outline-danger');
                clearButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري الحفظ...';
                clearButton.disabled = true;

                console.log('تفريغ الجدول: جمع بيانات التوقيت...');
                // جمع بيانات التوقيت الحالية للحفاظ عليها
                const timeData = [];
                const timeCells = document.querySelectorAll('.time-cell');
                console.log('تفريغ الجدول: عدد خلايا التوقيت المحددة:', timeCells.length);

                timeCells.forEach((cell, index) => {
                    const timeText = cell.textContent.trim();
                    const originalTime = cell.getAttribute('data-original-time');

                    console.log(`تفريغ الجدول: معالجة خلية التوقيت ${index+1}:`, {
                        originalTime: originalTime,
                        timeText: timeText
                    });

                    if (originalTime && timeText) {
                        timeData.push({
                            original_time: originalTime,
                            time_range: timeText
                        });
                    } else {
                        console.warn('تفريغ الجدول: تخطي خلية توقيت غير صالحة:', {
                            originalTime: originalTime,
                            timeText: timeText
                        });
                    }
                });

                console.log('تفريغ الجدول: بيانات التوقيت التي سيتم إرسالها:', timeData);
                console.log('تفريغ الجدول: إجمالي عدد عناصر التوقيت:', timeData.length);

                // إرسال بيانات التوقيت إلى الخادم
                const csrfToken = document.getElementById('csrf-token').value;
                fetch('{{ url_for("update_schedule") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        csrf_token: csrfToken,
                        schedule_data: {
                            schedule: [],
                            times: timeData
                        }
                    })
                })
                .then(response => {
                    console.log('تفريغ الجدول: استجابة الخادم الأولية:', response);
                    return response.json();
                })
                .then(data => {
                    console.log('تفريغ الجدول: استجابة الخادم:', data);

                    // عرض رسالة نجاح
                    showAlert('تم تفريغ وحفظ جدول التوقيت بنجاح', 'success');

                    // إرجاع التنسيق الأصلي للزر
                    clearButton.classList.remove('btn-danger');
                    clearButton.classList.add('btn-outline-danger');
                    clearButton.innerHTML = '<i class="fas fa-trash-alt me-1"></i> تفريغ الجدول';
                    clearButton.disabled = false;

                    // إعادة تحميل البيانات من الخادم للتأكد من التحديث
                    setTimeout(loadScheduleData, 500);
                })
                .catch(error => {
                    console.error('خطأ أثناء تفريغ الجدول:', error);
                    showAlert('حدث خطأ أثناء تفريغ الجدول: ' + error.message, 'danger');

                    // إرجاع التنسيق الأصلي للزر
                    clearButton.classList.remove('btn-danger');
                    clearButton.classList.add('btn-outline-danger');
                    clearButton.innerHTML = '<i class="fas fa-trash-alt me-1"></i> تفريغ الجدول';
                    clearButton.disabled = false;
                });
            }
        });
    }

    // معالج حدث اختيار اليوم البيداغوجي
    const pedagogyDaySelect = document.getElementById('pedagogy-day-select');
    if (pedagogyDaySelect) {
        // استرجاع اليوم المحفوظ من localStorage إن وجد
        const savedPedagogyDay = localStorage.getItem('pedagogy_day');
        if (savedPedagogyDay) {
            pedagogyDaySelect.value = savedPedagogyDay;
            highlightPedagogyDay(savedPedagogyDay);
        }

        pedagogyDaySelect.addEventListener('change', function() {
            // إزالة التلوين من جميع الأعمدة أولاً
            resetPedagogyDayHighlight();

            const selectedDay = this.value;

            if (selectedDay) {
                // حفظ اليوم المختار في localStorage
                localStorage.setItem('pedagogy_day', selectedDay);

                // تلوين العمود المناسب
                highlightPedagogyDay(selectedDay);

                // عرض رسالة تأكيد
                showAlert(`تم تعيين يوم ${this.options[this.selectedIndex].text} كيوم بيداغوجي`, 'success');
            } else {
                // إزالة اليوم المحفوظ من localStorage
                localStorage.removeItem('pedagogy_day');
            }
        });
    }
});

// وظيفة لتلوين عمود اليوم البيداغوجي
function highlightPedagogyDay(dayIndex) {
    if (!dayIndex) return;

    // تحديد اليوم
    const dayValue = parseInt(dayIndex);
    const columnIndex = dayValue + 1; // +1 لأن العمود الأول هو للتوقيت

    // تلوين العنوان أولاً
    const headerCell = document.querySelector(`#schedule-table thead th:nth-child(${columnIndex + 1})`);
    if (headerCell) {
        headerCell.classList.add('pedagogy-day-header');
    }

    // دمج خلايا الفترة الصباحية للعمود
    const morningRows = document.querySelectorAll('#schedule-table tbody tr:nth-child(-n+4)');
    let firstMorningCell = null;
    morningRows.forEach((row, index) => {
        const cell = row.querySelector(`td:nth-child(${columnIndex + 1})`);
        if (cell && !cell.parentElement.classList.contains('bg-white')) {
            if (index === 0) {
                firstMorningCell = cell;
                firstMorningCell.classList.add('pedagogy-day-column');
                firstMorningCell.setAttribute('contenteditable', 'false');
                firstMorningCell.title = 'اليوم البيداغوجي (غير قابل للتحرير)';
                firstMorningCell.textContent = '';
                firstMorningCell.setAttribute('rowspan', '4');
            } else {
                // إخفاء باقي الخلايا
                cell.style.display = 'none';
            }
        }
    });

    // دمج خلايا الفترة المسائية للعمود
    const afternoonRows = document.querySelectorAll('#schedule-table tbody tr:nth-child(n+6)');
    let firstAfternoonCell = null;
    afternoonRows.forEach((row, index) => {
        const cell = row.querySelector(`td:nth-child(${columnIndex + 1})`);
        if (cell && !cell.parentElement.classList.contains('bg-white')) {
            if (index === 0) {
                firstAfternoonCell = cell;
                firstAfternoonCell.classList.add('pedagogy-day-column');
                firstAfternoonCell.setAttribute('contenteditable', 'false');
                firstAfternoonCell.title = 'اليوم البيداغوجي (غير قابل للتحرير)';
                firstAfternoonCell.textContent = '';
                firstAfternoonCell.setAttribute('rowspan', '3');
            } else {
                // إخفاء باقي الخلايا
                cell.style.display = 'none';
            }
        }
    });

    // تحديث الإحصائيات
    updateAssignedClassesList();
}

// وظيفة لإزالة تلوين جميع الأعمدة
function resetPedagogyDayHighlight() {
    // إزالة خاصية rowspan وإعادة محتوى الخلايا الرئيسية
    document.querySelectorAll('.pedagogy-day-column').forEach(cell => {
        cell.classList.remove('pedagogy-day-column');
        cell.setAttribute('contenteditable', 'true');
        cell.removeAttribute('title');
        cell.removeAttribute('rowspan');
        cell.textContent = '';
    });

    // إظهار جميع الخلايا المخفية وإعادتها إلى الحالة الطبيعية
    document.querySelectorAll('#schedule-table td[style*="display: none"]').forEach(cell => {
        cell.style.display = '';
        cell.setAttribute('contenteditable', 'true');
    });

    // إزالة التلوين من العناوين
    document.querySelectorAll('.pedagogy-day-header').forEach(header => {
        header.classList.remove('pedagogy-day-header');
    });
}
</script>

<style>
/* تنسيقات جدول التوقيت */
.schedule-cell {
    height: 40px;
    vertical-align: middle;
    transition: background-color 0.2s;
    border: 1px solid #dee2e6;
    font-size: 0.9rem;
}

.schedule-cell:hover {
    background-color: #f8f9fa;
}

.schedule-cell:focus {
    outline: none;
    background-color: #e9ecef;
    box-shadow: inset 0 0 0 2px #007bff;
}

/* تنسيقات الجدول المصغر */
.compact-schedule {
    font-size: 0.9rem;
}

.compact-schedule th,
.compact-schedule td {
    padding: 0.5rem;
}

/* تنسيقات الشريط الجانبي */
.sidebar-card {
    position: sticky;
    top: 1rem;
}

.sidebar-section-title {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
    font-size: 1rem;
    color: #0d6efd;
}

/* تنسيقات الإحصائيات */
.teaching-stats {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.stat-item:last-child {
    margin-bottom: 0;
}

.stat-label {
    font-weight: 500;
    color: #495057;
}

.stat-value {
    font-weight: 700;
    color: #0d6efd;
    background-color: #e9ecef;
    border-radius: 1rem;
    min-width: 2rem;
    text-align: center;
    padding: 0.1rem 0.5rem;
}

/* تنسيقات أزرار الأقسام */
.assigned-classes-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.class-badge {
    display: inline-block;
    padding: 0.4rem 0.75rem;
    font-size: 0.85rem;
    font-weight: 600;
    color: white;
    border-radius: 1rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.2s;
}

.class-badge:hover {
    transform: translateY(-2px);
}

/* تنسيقات اليوم البيداغوجي */
.pedagogy-day {
    align-items: center;
}

.select-container {
    position: relative;
    display: inline-block;
}

.select-icon {
    display: none;
}

.no-arrow {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
    background: #e9ecef !important;
}

.no-arrow::-ms-expand {
    display: none !important;
}

.pedagogy-day select {
    width: auto;
    font-size: 0.85rem;
    border-radius: 1rem;
    border: 1px solid #ced4da;
    padding: 0.25rem 0.75rem;
    color: #0d6efd;
    font-weight: 600;
}

.pedagogy-day select:focus {
    outline: none;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.pedagogy-day-column {
    background-color: rgba(135, 206, 250, 0.6) !important; /* اللون الأزرق الفاتح */
    transition: background-color 0.3s ease;
    vertical-align: middle;
    text-align: center;
    font-weight: bold;
    color: #0d6efd;
}

.pedagogy-day-column:hover {
    background-color: rgba(135, 206, 250, 0.8) !important; /* اللون الأزرق الفاتح عند التحويم */
}

.pedagogy-day-header {
    background-color: #0d6efd !important; /* اللون الأزرق للعنوان */
    color: white !important;
    position: relative;
}

.pedagogy-day-header::after {
    content: '📚';
    font-size: 0.75rem;
    position: absolute;
    top: 0.2rem;
    left: 0.2rem;
}

/* تنسيقات خلايا التوقيت */
.time-cell {
    width: 130px;
    min-width: 130px;
    font-weight: 600;
    text-align: center;
    cursor: text;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease;
}

.time-cell:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.time-cell:focus {
    background-color: rgba(0, 123, 255, 0.2);
    outline: none;
    box-shadow: inset 0 0 0 2px rgba(0, 123, 255, 0.5);
}

/* جعل عرض أعمدة الجدول متساوية */
.table th, .table td {
    width: 16.67%; /* يقسم العرض بالتساوي على 6 أعمدة (التوقيت + 5 أيام) */
}

/* تنسيقات خاصة بالهواتف */
@media (max-width: 768px) {
    /* تحسين تجاوب الجدول للهواتف */
    .mobile-table-container {
        margin: 0 -15px;
        width: calc(100% + 30px);
        overflow-x: auto;
        -webkit-overflow-scrolling: touch; /* تحسين التمرير على iOS */
    }

    /* تقليل حجم الخط في الجدول للهواتف */
    .compact-schedule {
        font-size: 0.8rem;
    }

    /* تقليل حجم خلايا التوقيت للهواتف */
    .time-cell {
        width: 100px;
        min-width: 100px;
        font-size: 0.75rem;
    }

    /* تقليل حجم خلايا الجدول للهواتف */
    .schedule-cell {
        height: 35px;
        padding: 0.25rem !important;
        font-size: 0.75rem;
    }

    /* تقليل حجم عناوين الجدول للهواتف */
    .time-header, .day-header {
        padding: 0.25rem !important;
        font-size: 0.75rem;
        white-space: nowrap;
    }

    /* تعديل حجم الشريط الجانبي للهواتف */
    .sidebar-card {
        margin-bottom: 1rem;
        position: static;
    }

    /* تقليل المسافات في الشريط الجانبي للهواتف */
    .sidebar-card .card-body {
        padding: 0.75rem;
    }

    /* تقليل حجم أزرار الأقسام للهواتف */
    .class-badge {
        padding: 0.3rem 0.6rem;
        font-size: 0.75rem;
    }

    /* تقليل حجم العناوين في الهواتف */
    .card-header h5 {
        font-size: 1rem;
    }

    /* تقليل حجم الإحصائيات في الهواتف */
    .stat-label {
        font-size: 0.8rem;
    }

    .stat-value {
        font-size: 0.8rem;
        padding: 0.1rem 0.4rem;
    }

    /* تحسين مظهر الأزرار في الهواتف */
    .btn {
        font-size: 0.85rem;
    }

    /* تقليل المسافات بين العناصر في الهواتف */
    .py-3 {
        padding-top: 0.5rem !important;
        padding-bottom: 0.5rem !important;
    }

    /* تحسين مظهر فترة الراحة في الجدول */
    #schedule-table tr.bg-white td {
        padding: 0.15rem !important;
        font-size: 0.7rem;
    }
}

/* تنسيق الخلايا التي تحتوي على بيانات */
.filled-cell {
    background-color: #e8f7ff !important; /* لون خلفية أزرق فاتح */
    box-shadow: inset 0 0 0 1px rgba(13, 110, 253, 0.2); /* ظل داخلي */
    font-weight: 600; /* خط عريض قليلاً */
    color: #0d6efd; /* لون النص أزرق */
}

/* تأثير التحويم على الخلايا المملوءة */
.filled-cell:hover {
    background-color: #d1ebff !important;
}

/* تخصيص تنسيق الإشعار */
.alert.py-1 {
    padding-top: 0.15rem !important;
    padding-bottom: 0.15rem !important;
    line-height: 1.2;
}
</style>
{% endblock %}
