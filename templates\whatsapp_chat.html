{% extends "base.html" %}

{% block head %}
<title>محادثة واتساب</title>
<style>
    /* تنسيقات عامة للصفحة */
    :root {
        /* ألوان الزرين */
        --teachers-blue: #0078d4;
        --teachers-blue-light: #e9f5ff;
        --groups-green: #107c41;
        --groups-green-light: #f0f7ee;

        /* ألوان الواتساب المعدلة */
        --wa-green: #107c41;
        --wa-light-green: #25D366;
        --wa-teal: #0078d4;
        --wa-light-gray: #f5f8fa;
        --wa-chat-bg: #e9f0f7;
        --wa-outgoing-bg: #e9f5ff;
        --wa-incoming-bg: #ffffff;
        --wa-panel-header-bg: #f5f8fa;
        --wa-panel-bg: #ffffff;
        --wa-icon-color: #0078d4;
        --wa-primary-text: #111b21;
        --wa-secondary-text: #667781;
        --wa-border-color: #e9edef;
        --compose-bg-color: #ffffff;
        --primary: var(--wa-panel-header-bg);

        /* إضافة متغيرات جديدة لأحجام الخط المختلفة */
        --font-size-xs: 10px;
        --font-size-sm: 12px;
        --font-size-md: 14px;
        --font-size-lg: 16px;
        --font-size-xl: 18px;
    }

    /* تعديلات الواجهة لجعل حقل الكتابة ينزلق فوق المحتوى */
    html, body {
        height: 100%;
        width: 100%;
        overflow: hidden;
        margin: 0;
        padding: 0;
    }

    main {
        height: 100%;
        width: 100%;
        position: relative;
        overflow: hidden;
    }

    #sidebar, #chat-window {
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    #chat-window-contents {
        flex: 1 1 auto;
        overflow-y: auto;
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch;
        padding-bottom: 10px; /* مساحة إضافية في الأسفل */
    }

    #chat-window-footer {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 100;
        background-color: var(--primary);
        transition: transform 0.3s ease;
    }

    /* للأجهزة المحمولة عند ظهور لوحة المفاتيح */
    body.keyboard-open #chat-window-footer {
        position: fixed;
    }

    /* للأجهزة اللوحية والهواتف في الوضع الأفقي */
    @media (min-width: 768px) {
        #chat-window-footer {
            position: relative;
        }
    }

    body {
        background-color: var(--wa-light-gray);
        height: 100vh;
        overflow: hidden;
        font-family: 'Segoe UI', 'Helvetica Neue', Helvetica, 'Lucida Grande', Arial, sans-serif;
        color: var(--wa-primary-text);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .whatsapp-container {
        display: flex;
        height: 80vh;
        max-width: 1400px;
        width: 95%;
        margin: 0 auto;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.16);
        background-color: var(--wa-panel-bg);
        position: relative;
        overflow: hidden;
        border-radius: 12px;
    }

    /* تنسيقات الشريط الجانبي */
    .sidebar {
        width: 30%;
        height: 100%;
        border-left: 1px solid var(--wa-border-color);
        display: flex;
        flex-direction: column;
        background-color: var(--wa-panel-bg);
        transition: all 0.3s ease;
        overflow: hidden; /* نحتفظ بهذه الخاصية للحاوية الرئيسية */
    }

    /* تنسيقات منطقة المحادثة */
    .chat-area {
        width: 70%;
        height: 100%;
        display: flex;
        flex-direction: column;
        background-color: var(--wa-chat-bg);
        background-image: url('https://web.whatsapp.com/img/bg-chat-tile-light_a4be512e7195b6b733d9110b408f075d.png');
        background-repeat: repeat;
        position: relative;
        background-attachment: fixed;
    }

    /* رأس المحادثة */
    .chat-header {
        display: flex;
        align-items: center;
        padding: 10px 16px;
        background-color: var(--wa-panel-header-bg);
        height: 60px;
        border-bottom: 1px solid var(--wa-border-color);
    }

    .back-to-chats {
        display: none;
        background: none;
        border: none;
        color: var(--wa-icon-color);
        font-size: 18px;
        cursor: pointer;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-left: 10px;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
    }

    .back-to-chats:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .chat-contact {
        display: flex;
        align-items: center;
        flex: 1;
    }

    .contact-avatar {
        margin-left: 15px;
    }

    .contact-info {
        display: flex;
        flex-direction: column;
    }

    .contact-name {
        margin: 0;
        font-size: var(--font-size-lg);
        font-weight: 500;
        color: var(--wa-primary-text);
    }

    .contact-status {
        margin: 0;
        font-size: var(--font-size-sm);
        color: var(--wa-secondary-text);
        display: flex;
        align-items: center;
    }

    .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-left: 5px;
        display: inline-block;
    }

    .status-indicator.online {
        background-color: var(--groups-green);
    }

    .status-indicator.offline {
        background-color: #bdbdbd;
    }

    /* إضافة مؤشر الحالة في القائمة الجانبية */
    .sidebar-status-indicator {
        width: 14px;
        height: 14px;
        border-radius: 50%;
        display: block;
        position: absolute;
        right: 0;
        bottom: 0;
        border: 2px solid white;
        z-index: 10;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .sidebar-status-indicator.online {
        background-color: var(--groups-green);
    }

    .sidebar-status-indicator.offline {
        background-color: #bdbdbd;
    }

    .chat-avatar {
        position: relative;
    }

    .chat-actions {
        display: flex;
        gap: 16px;
    }

    /* منطقة عرض الرسائل */
    .messages-container {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        display: flex;
        flex-direction: column;
        scrollbar-width: thin;
        scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
    }

    .messages-container::-webkit-scrollbar {
        width: 6px;
    }

    .messages-container::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
    }

    .chat-date {
        background-color: rgba(225, 245, 254, 0.92);
        color: var(--wa-secondary-text);
        font-size: 12.5px;
        padding: 6px 12px;
        border-radius: 8px;
        box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
        margin: 12px auto;
        text-align: center;
        width: fit-content;
        font-weight: 500;
        background: linear-gradient(to right, var(--teachers-blue-light), var(--groups-green-light));
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .messages {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .message {
        max-width: 65%;
        margin-bottom: 8px;
        display: flex;
        animation: message-pop 0.2s ease-out;
    }

    .message.incoming {
        align-self: flex-start;
    }

    .message.outgoing {
        align-self: flex-end;
    }

    .message-content {
        padding: 8px 9px 8px 11px;
        border-radius: 8px;
        box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .message.incoming .message-content {
        background-color: var(--wa-incoming-bg);
        border-top-right-radius: 0;
        border-right: 3px solid var(--teachers-blue);
        box-shadow: 0 2px 5px rgba(0, 120, 212, 0.1);
    }

    .message.outgoing .message-content {
        background-color: var(--wa-outgoing-bg);
        border-top-left-radius: 0;
        border-left: 3px solid var(--groups-green);
        box-shadow: 0 2px 5px rgba(16, 124, 65, 0.1);
    }

    .message-content p {
        margin: 0;
        font-size: var(--font-size-md);
        line-height: 19px;
        color: var(--wa-primary-text);
        word-wrap: break-word;
    }

    .message-time {
        font-size: var(--font-size-xs);
        color: var(--wa-secondary-text);
        margin-right: 4px;
        float: left;
        margin-top: 4px;
        margin-left: 4px;
    }

    .message-status {
        font-size: 11px;
        color: var(--wa-secondary-text);
        margin-right: 4px;
        float: left;
        margin-top: 4px;
    }

    .message-status i {
        color: #53bdeb;
    }

    @keyframes message-pop {
        0% {
            opacity: 0;
            transform: scale(0.9);
        }
        100% {
            opacity: 1;
            transform: scale(1);
        }
    }

    /* شريط إدخال الرسائل */
    .input-area {
        display: flex;
        align-items: center;
        padding: 10px 16px;
        background-color: var(--wa-panel-header-bg);
        position: relative;
        border-top: 1px solid var(--wa-border-color);
    }

    .emoji-btn, .attach-btn, .voice-btn, .send-btn {
        background: none;
        border: none;
        color: var(--wa-icon-color);
        font-size: 24px;
        cursor: pointer;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        border-radius: 50%;
    }

    .emoji-btn:hover, .attach-btn:hover, .voice-btn:hover, .send-btn:hover {
        color: var(--wa-teal);
        background-color: rgba(0, 0, 0, 0.05);
    }

    .send-btn {
        color: var(--wa-green);
    }

    .message-input-container {
        flex: 1;
        margin: 0 8px;
    }

    .message-input {
        width: 100%;
        padding: 9px 12px;
        border: none;
        border-radius: 8px;
        outline: none;
        font-size: 15px;
        background-color: white;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(11, 20, 26, 0.08);
    }

    .message-input:focus {
        box-shadow: 0 2px 4px rgba(11, 20, 26, 0.1);
    }

    #compose-chat-box {
        margin: 0px 10px;
        background-color: var(--compose-bg-color);
        padding-right: 12px;
        font-size: 15px;
        position: relative;
        z-index: 1;
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
    }

    #chat-window-footer {
        display: flex;
        height: 62px;
        width: 100%;
        padding: 10px 25px;
        background-color: var(--primary);
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 10;
        will-change: transform;
        transition: bottom 0.3s ease;
        box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.2);
    }

    /* حالة عدم اختيار محادثة */
    .no-chat-selected {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: var(--wa-secondary-text);
        text-align: center;
        background-color: var(--wa-panel-bg);
    }

    .no-chat-content {
        max-width: 460px;
        padding: 20px;
    }

    .no-chat-icon {
        font-size: 100px;
        color: var(--wa-light-gray);
        margin-bottom: 30px;
        background-color: #f0f2f5;
        width: 200px;
        height: 200px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* تنسيقات للاقتباس */
    .quote-container {
        margin: 0 auto 30px;
        width: 100%;
        max-width: 500px; /* زيادة العرض الأقصى للبطاقة */
        position: relative;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        background-color: #f5f5f5;
        overflow: hidden;
        display: flex;
        flex-direction: row-reverse; /* لجعل الشريط البرتقالي على اليمين */
    }

    .quote-sidebar {
        width: 60px;
        background-color: #e67e5a;
        flex-shrink: 0;
        position: relative; /* لوضع زر الإغلاق بداخله */
    }

    .quote-container:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.15);
    }

    .quote-container:hover .quote-sidebar {
        animation: pulseColor 1.5s infinite alternate;
    }

    @keyframes pulseColor {
        from {
            background-color: #e67e5a;
        }
        to {
            background-color: #ff9d7a;
        }
    }

    .quote-content {
        padding: 15px 20px;
        position: relative;
        z-index: 1;
        flex-grow: 1;
        background-color: #f5f5f5;
        overflow: hidden; /* منع تجاوز المحتوى */
        width: 100%; /* تأكيد على عرض المحتوى */
        display: flex;
        flex-direction: column;
        justify-content: center; /* توسيط العناصر عمودياً */
    }

    /* زر إغلاق بطاقة الآية */
    .close-quote-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.3);
        border: none;
        color: white;
        font-size: 20px;
        font-weight: bold;
        line-height: 1;
        text-align: center;
        cursor: pointer;
        display: flex; /* إظهار الزر دائماً */
        align-items: center;
        justify-content: center;
        padding: 0;
        transition: all 0.2s ease;
        z-index: 10;
    }

    /* تنسيقات إضافية لزر الإغلاق في الشاشات الكبيرة */
    @media (min-width: 769px) {
        .close-quote-btn {
            display: none; /* إخفاء الزر في الشاشات الكبيرة */
        }
    }

    .close-quote-btn:hover {
        background-color: rgba(255, 255, 255, 0.4);
        color: white;
    }

    /* تحديث نوع الخط لجميع عناصر الآية ليكون أكثر احترافية */
    .quote-title, .quote-main, .quote-source {
        font-family: 'Traditional Arabic', 'Amiri', serif;
    }

    /* تحديد حجم الخط بشكل قاطع باستخدام !important */
    .quote-title {
        font-size: 30px !important; /* حجم الخط بالضبط 30 بكسل */
        font-weight: bold;
        margin-bottom: 15px;
        color: #e67e5a;
        text-align: right;
        animation: fadeInRight 1s ease-out;
    }

    .quote-main {
        font-size: 30px !important; /* زيادة حجم الخط ليكون موحداً مع الشاشات الكبيرة */
        font-weight: bold;
        margin-bottom: 10px;
        margin-top: 15px; /* تقليل الفاصل بين العنوان والآية */
        line-height: 1.6;
        color: #333;
        text-align: center;
        padding: 5px 0;
        animation: fadeInUp 1.2s ease-out;
        transition: all 0.3s ease;
        text-shadow: 1px 1px 3px rgba(230, 126, 90, 0.3),
                     0 0 1px rgba(230, 126, 90, 0.2),
                     0 0 2px rgba(230, 126, 90, 0.1); /* ظل خفيف برتقالي متعدد للآية */
        -webkit-text-shadow: 1px 1px 3px rgba(230, 126, 90, 0.3),
                           0 0 1px rgba(230, 126, 90, 0.2),
                           0 0 2px rgba(230, 126, 90, 0.1); /* دعم لمتصفح Safari */
        white-space: nowrap; /* منع التفاف النص ليكون في سطر واحد */
        overflow-x: auto; /* إضافة شريط تمرير أفقي إذا كان النص طويلاً */
    }

    .quote-container:hover .quote-main {
        color: #000;
        text-shadow: 2px 2px 4px rgba(230, 126, 90, 0.4); /* تعزيز الظل البرتقالي عند التحويم */
        transform: scale(1.02);
    }

    .quote-source {
        font-size: 15px !important; /* حجم الخط بالضبط 15 بكسل */
        color: #777;
        text-align: left; /* محاذاة النص إلى اليسار */
        margin-top: 15px;
        padding-left: 10px; /* إضافة مسافة من اليسار */
        animation: fadeIn 1.5s ease-out;
    }

    /* تأكيد إضافي على حجم الخط للعناصر الفرعية */
    .quote-container p.quote-title {
        font-size: 30px !important;
        font-size: 1.875rem !important; /* حجم بديل بوحدة rem */
    }

    .quote-container p.quote-main {
        font-size: 30px !important;
        font-size: 1.875rem !important; /* حجم بديل بوحدة rem */
    }

    .quote-container p.quote-source {
        font-size: 15px !important;
        font-size: 0.9375rem !important; /* حجم بديل بوحدة rem */
    }

    /* تجاوز أي تنسيقات أخرى قد تؤثر على حجم الخط */
    .no-chat-content .quote-container p {
        font-size: inherit !important; /* يرث الحجم من القاعدة المحددة أعلاه */
    }

    /* تعطيل أي تنسيقات قد تؤثر على حجم الخط من الفئات الأخرى */
    .no-chat-selected .quote-container p,
    .messages-container .quote-container p {
        font-size: inherit !important;
    }

    /* حركات احترافية للآية */
    @keyframes fadeInRight {
        from {
            opacity: 0;
            transform: translateX(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    /* تنسيقات العناوين الترحيبية */
    .welcome-title {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 12px;
        color: var(--wa-teal);
        font-family: Arial, sans-serif;
        transition: all 0.3s ease;
        animation: fadeInUp 1s ease-out;
        position: relative;
        display: inline-block;
        letter-spacing: -0.5px;
        background: linear-gradient(120deg, var(--wa-teal) 0%, var(--wa-green) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
        padding: 0 5px;
        border-radius: 4px;
    }

    .welcome-title::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 0;
        background-color: rgba(0, 168, 132, 0.1);
        transition: height 0.3s ease;
        z-index: -1;
        border-radius: 4px;
    }

    .no-chat-content:hover .welcome-title::before {
        height: 100%;
    }

    .welcome-subtitle {
        font-size: 14px;
        line-height: 20px;
        margin-bottom: 0;
        color: var(--wa-secondary-text);
        font-family: Arial, sans-serif;
        transition: all 0.3s ease;
        animation: fadeInUp 1.2s ease-out;
        position: relative;
        display: inline-block;
        padding: 3px 10px;
        border-radius: 20px;
        background-color: rgba(0, 0, 0, 0.03);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .no-chat-content:hover .welcome-subtitle {
        background-color: rgba(0, 168, 132, 0.08);
        color: var(--wa-teal);
        transform: translateY(-2px);
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
    }

    /* تنسيقات إضافية للمظهر العصري */
    .no-chat-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        transition: all 0.3s ease;
    }

    .no-chat-content:hover {
        transform: scale(1.02);
    }

    .welcome-message {
        margin-top: 20px;
        padding: 15px 25px;
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        text-align: center;
        transition: all 0.3s ease;
        border-bottom: 3px solid transparent;
        border-image: linear-gradient(to right, var(--wa-teal), var(--wa-light-green));
        border-image-slice: 1;
    }

    .welcome-message:hover {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        transform: translateY(-5px);
    }

    /* للتوافق مع التنسيقات القديمة */
    .no-chat-selected h3:not(.welcome-title) {
        font-size: 32px;
        font-weight: 300;
        margin-bottom: 16px;
        color: var(--wa-primary-text);
    }

    .no-chat-selected p:not(.welcome-subtitle) {
        font-size: 14px;
        line-height: 20px;
        margin-bottom: 0;
        color: var(--wa-secondary-text);
    }

    /* تنسيقات الشريط الجانبي */
    .sidebar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 16px;
        background-color: var(--wa-panel-header-bg);
        height: 60px;
        border-bottom: 1px solid var(--wa-border-color);
    }

    .user-profile {
        display: flex;
        align-items: center;
    }

    .profile-image {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .sidebar-actions {
        display: flex;
        gap: 10px;
    }

    /* تنسيقات أزرار التنقل الجديدة */
    .nav-btn {
        background-color: var(--wa-light-gray);
        border: none;
        color: var(--wa-teal);
        font-size: 14px;
        cursor: pointer;
        padding: 8px 15px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        margin: 0 5px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .nav-btn i {
        margin-left: 8px;
        font-size: 16px;
    }

    .nav-btn span {
        font-weight: 500;
    }

    .nav-btn:hover {
        background-color: var(--wa-teal);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
    }

    .nav-btn.active {
        background-color: var(--wa-teal);
        color: white;
    }

    /* تنسيقات خاصة لكل زر */
    .teachers-btn {
        background-color: #e9f5ff;
        color: #0078d4;
    }

    .teachers-btn:hover, .teachers-btn.active {
        background-color: #0078d4;
        color: white;
    }

    .groups-btn {
        background-color: #f0f7ee;
        color: #107c41;
    }

    .groups-btn:hover, .groups-btn.active {
        background-color: #107c41;
        color: white;
    }

    .action-btn {
        background: none;
        border: none;
        color: var(--wa-icon-color);
        font-size: 18px;
        cursor: pointer;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
    }

    .action-btn:hover {
        background-color: rgba(0, 0, 0, 0.05);
        color: var(--wa-teal);
    }

    .search-container {
        padding: 8px 12px;
        background-color: var(--wa-panel-bg);
        border-bottom: 1px solid var(--wa-border-color);
    }

    .search-box {
        background-color: var(--wa-light-gray);
        border-radius: 8px;
        padding: 9px 12px;
        display: flex;
        align-items: center;
        transition: all 0.2s ease;
        position: relative;
    }

    .search-box:focus-within {
        background-color: var(--wa-panel-bg);
        box-shadow: 0 1px 3px rgba(11, 20, 26, 0.08);
        border: 1px solid #d1d7db;
        padding: 8px 11px;
    }

    .search-box:focus-within .search-icon {
        color: var(--wa-teal);
    }

    .search-box .clear-search {
        position: absolute;
        left: 12px;
        color: var(--wa-icon-color);
        cursor: pointer;
        font-size: 14px;
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .search-box:focus-within .clear-search.visible {
        opacity: 1;
    }

    .search-icon {
        color: var(--wa-icon-color);
        margin-left: 10px;
    }

    .search-input {
        background: none;
        border: none;
        outline: none;
        width: 100%;
        font-size: 15px;
        color: var(--wa-primary-text);
        padding: 0;
    }

    .search-input::placeholder {
        color: var(--wa-secondary-text);
    }

    .chat-list {
        flex: 1;
        overflow-y: auto;
        background-color: var(--wa-panel-bg);
        padding-bottom: 30px; /* إضافة مساحة في الأسفل لضمان رؤية جميع العناصر */
        -webkit-overflow-scrolling: touch; /* تحسين التمرير على أجهزة iOS */
        contain: content; /* تحسين أداء العرض مع الأعداد الكبيرة */
        will-change: transform; /* تحسين أداء التمرير */
    }

    /* تنسيقات قائمة المجموعات */
    .groups-container {
        display: none;
        flex-direction: column;
        height: calc(100% - 120px);
        background-color: var(--wa-panel-bg);
        flex: 1;
        overflow-y: auto;
    }

    .groups-header {
        display: flex;
        align-items: center;
        padding: 10px 16px;
        background-color: var(--wa-panel-header-bg);
        border-bottom: 1px solid var(--wa-border-color);
    }

    .groups-header .back-btn {
        background: none;
        border: none;
        color: var(--wa-icon-color);
        font-size: 18px;
        cursor: pointer;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-left: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .groups-header .back-btn:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .groups-header h5 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
    }

    .groups-list {
        flex: 1;
        overflow-y: auto;
        background-color: var(--wa-panel-bg);
    }

    .workplace-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid var(--wa-border-color);
        cursor: pointer;
        transition: all 0.2s ease;
        border-right: 3px solid transparent;
    }

    .workplace-item:hover {
        background-color: var(--groups-green-light);
        border-right: 3px solid rgba(16, 124, 65, 0.3);
    }

    .workplace-icon {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        background-color: var(--groups-green);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 15px;
        font-size: 20px;
        box-shadow: 0 2px 5px rgba(16, 124, 65, 0.2);
        transition: all 0.3s ease;
    }

    .workplace-item:hover .workplace-icon {
        transform: scale(1.05);
        box-shadow: 0 3px 8px rgba(16, 124, 65, 0.3);
    }

    .workplace-info {
        flex: 1;
    }

    .workplace-name {
        font-size: 16px;
        font-weight: 500;
        margin: 0;
        color: var(--wa-primary-text);
    }

    .workplace-count {
        font-size: 13px;
        color: var(--wa-secondary-text);
        margin: 0;
    }

    .workplace-group {
        display: none;
        flex-direction: column;
        height: 100%;
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        background-color: var(--wa-panel-bg);
        z-index: 25;
    }

    .workplace-header {
        display: flex;
        align-items: center;
        padding: 10px 16px;
        background-color: var(--wa-panel-header-bg);
        border-bottom: 1px solid var(--wa-border-color);
    }

    .workplace-header .back-btn {
        background: none;
        border: none;
        color: var(--wa-icon-color);
        font-size: 18px;
        cursor: pointer;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-left: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .workplace-header .back-btn:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .workplace-header h5 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
    }

    .workplace-users {
        flex: 1;
        overflow-y: auto;
        background-color: var(--wa-panel-bg);
    }

    .empty-message {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100px;
        color: var(--wa-secondary-text);
        font-size: 14px;
        text-align: center;
        padding: 20px;
    }

    .chat-item {
        display: flex;
        padding: 12px 16px;
        border-bottom: 1px solid var(--wa-border-color);
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
        contain: layout; /* تحسين أداء تخطيط العناصر */
        will-change: transform, opacity; /* تحسين أداء التحولات */
        border-right: 3px solid transparent;
    }

    .chat-item:hover {
        background-color: var(--teachers-blue-light);
        border-right: 3px solid rgba(0, 120, 212, 0.3);
    }

    .chat-item.active {
        background-color: var(--teachers-blue-light);
        border-right: 3px solid var(--teachers-blue);
    }

    .chat-avatar {
        margin-left: 15px;
    }

    .avatar-image {
        width: 49px;
        height: 49px;
        border-radius: 50%;
        object-fit: cover;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .chat-info {
        flex: 1;
        min-width: 0;
        padding-top: 2px;
    }

    .chat-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
    }

    .chat-name {
        margin: 0;
        font-size: var(--font-size-lg);
        font-weight: 500;
        color: var(--wa-primary-text);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .chat-time {
        font-size: 12px;
        color: var(--wa-secondary-text);
        white-space: nowrap;
        margin-right: 5px;
    }

    .chat-bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .chat-message {
        margin: 0;
        font-size: var(--font-size-md);
        color: var(--wa-secondary-text);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 80%;
    }

    .chat-badges {
        display: flex;
        align-items: center;
    }

    .unread-badge {
        background-color: var(--wa-light-green);
        color: white;
        font-size: 12px;
        min-width: 20px;
        height: 20px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 6px;
        font-weight: 500;
    }

    /* تنسيقات للشاشات المتوسطة */
    @media (max-width: 1200px) {
        .whatsapp-container {
            height: 85vh;
            width: 98%;
        }
    }

    /* تنسيقات للشاشات الصغيرة */
    @media (max-width: 768px) {
        body {
            justify-content: flex-start;
            padding-top: 10px;
        }

        .whatsapp-container {
            height: 90vh;
            width: 98%;
            margin: 0 auto;
            border-radius: 8px;
        }

        .sidebar {
            width: 100%;
            position: absolute;
            right: 0;
            top: 0;
            z-index: 10;
            transform: translateX(0);
            transition: transform 0.3s ease;
            max-height: 100vh; /* تحديد الارتفاع الأقصى */
            display: flex;
            flex-direction: column;
        }

        /* تحسين التمرير في الشاشات الصغيرة */
        .chat-list {
            padding-bottom: 80px; /* زيادة المساحة في الأسفل للأجهزة المحمولة */
            -webkit-overflow-scrolling: touch;
            contain: content; /* تحسين أداء العرض */
            will-change: transform; /* تحسين أداء التمرير */
            scroll-behavior: smooth; /* تمرير سلس */
        }

        /* تحسين أداء تحميل العناصر في الشاشات الصغيرة */
        .chat-item {
            contain: layout style; /* تحسين أداء تخطيط وتنسيق العناصر */
            transform: translateZ(0); /* تفعيل تسريع الأجهزة */
            backface-visibility: hidden; /* منع مشاكل العرض */
        }

        .sidebar.hidden {
            transform: translateX(100%);
        }

        .chat-area {
            width: 100%;
        }

        .back-to-chats {
            display: flex !important;
        }

        .message {
            max-width: 85%;
        }

        .chat-header {
            padding: 8px 12px;
            height: 50px;
        }

        .input-area {
            padding: 6px 10px;
            position: fixed; /* تثبيت منطقة الإدخال في الأسفل */
            bottom: 0;
            left: 0;
            right: 0;
            width: 100%;
            background-color: var(--wa-panel-header-bg);
            z-index: 1000;
            border-top: 1px solid var(--wa-border-color);
        }

        .emoji-btn, .attach-btn, .voice-btn, .send-btn {
            width: 32px;
            height: 32px;
            font-size: 18px;
        }

        .message-input {
            padding: 6px 8px;
            font-size: 15px; /* تكبير حجم الخط في مربع الإدخال */
        }

        .chat-item {
            padding: 8px 12px;
        }

        .avatar-image {
            width: 40px;
            height: 40px;
        }

        .chat-name {
            font-size: 16px; /* تكبير حجم الخط لأسماء المحادثات */
        }

        .chat-message {
            font-size: 14px; /* تكبير حجم الخط للرسائل في القائمة */
        }

        .no-chat-icon {
            width: 130px;
            height: 130px;
            font-size: 70px;
        }

        .welcome-title, .no-chat-selected h3 {
            font-size: 20px;
        }

        .welcome-subtitle {
            font-size: 14px; /* تكبير حجم الخط للعنوان الفرعي */
            line-height: 18px;
        }

        .welcome-message {
            padding: 10px 15px;
            margin-top: 12px;
        }

        /* تكبير حجم الخط في رأس المحادثة */
        .contact-name {
            font-size: 16px; /* تكبير حجم الخط لاسم جهة الاتصال */
        }

        .contact-status {
            font-size: 13px; /* تكبير حجم الخط لحالة جهة الاتصال */
        }

        /* تكبير حجم الخط في الرسائل */
        .message-content p {
            font-size: 15px; /* تكبير حجم الخط لمحتوى الرسائل */
            line-height: 19px;
        }

        .message-time {
            font-size: 12px; /* تكبير حجم الخط لوقت الرسائل */
        }

        /* تكبير حجم الخط في شريط البحث */
        .search-input {
            font-size: 15px; /* تكبير حجم الخط في مربع البحث */
        }

        /* إضافة مساحة في الأسفل لتجنب تداخل الرسائل مع منطقة الإدخال */
        .messages-container {
            padding-bottom: 60px;
        }

        /* تنسيقات لعرض بطاقة الآية الكريمة أسفل الشريط الجانبي في الشاشات الصغيرة فقط */
        .sidebar:not(.hidden) ~ .chat-area .no-chat-content {
            position: fixed;
            top: 100%;
            left: 0;
            right: 0;
            width: 100%;
            background-color: var(--wa-panel-bg);
            z-index: 20;
            padding: 15px;
            border-top: 1px solid var(--wa-border-color);
            box-shadow: 0 -5px 10px rgba(0, 0, 0, 0.1);
            transform: translateY(-100%);
            transition: transform 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        /* عندما يكون الشريط الجانبي مخفياً، نعيد بطاقة الآية إلى موضعها الأصلي */
        .sidebar.hidden + .chat-area .no-chat-content {
            position: static !important;
            transform: none !important;
            box-shadow: none !important;
            border-top: none !important;
            padding: 0 !important;
            background-color: transparent !important;
            height: auto !important;
            width: auto !important;
            margin: 0 !important;
        }

        /* إخفاء بطاقة الآية عند النقر على زر الإغلاق */
        .no-chat-content.hidden {
            transform: translateY(0) !important;
            display: none !important;
        }

        /* تأكيد على إظهار بطاقة الآية بشكل صحيح عند العودة للمحادثات */
        .sidebar:not(.hidden) .no-chat-selected .no-chat-content {
            display: flex !important;
        }

        /* تعديل حجم وموضع بطاقة الآية الكريمة للشاشات الصغيرة */
        .sidebar:not(.hidden) ~ .chat-area .quote-container {
            margin: 0 auto;
            max-width: 90%;
        }

        /* تعديلات إضافية لزر الإغلاق في الشاشات الصغيرة */
        .quote-sidebar .close-quote-btn {
            top: 5px;
            right: 5px;
            display: flex !important; /* تأكيد على إظهار الزر في الشاشات الصغيرة */
            background-color: rgba(255, 255, 255, 0.3);
        }

        /* تعديلات لزيادة عرض البطاقة في الشاشات الصغيرة */
        .sidebar:not(.hidden) ~ .chat-area .quote-container {
            max-width: 500px; /* زيادة العرض الأقصى للبطاقة في الشاشات الصغيرة */
            width: 95%; /* تحديد العرض النسبي */
        }

        /* تعديل الشريط البرتقالي في الشاشات الصغيرة */
        .sidebar:not(.hidden) ~ .chat-area .quote-sidebar {
            width: 50px; /* تقليل عرض الشريط البرتقالي */
        }

        /* تعديلات لمحتوى الآية في الشاشات الصغيرة */
        .sidebar:not(.hidden) ~ .chat-area .quote-content {
            overflow-y: hidden; /* منع تجاوز المحتوى عمودياً */
            overflow-x: auto; /* السماح بالتمرير الأفقي */
            padding: 20px 15px; /* تقليل الهوامش الجانبية */
        }

        /* تنسيق محتوى بطاقة الآية */
        .sidebar:not(.hidden) ~ .chat-area .quote-content {
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* تنسيق عنوان الآية (قال الله تعالى) */
        .sidebar:not(.hidden) ~ .chat-area .quote-title {
            text-align: right; /* محاذاة إلى اليمين */
            margin-bottom: 10px; /* زيادة المسافة أسفل العنوان */
            font-size: 22px !important; /* تقليل حجم الخط */
            padding-right: 5px; /* إضافة مسافة من اليمين */
        }

        /* تنسيق نص الآية */
        .sidebar:not(.hidden) ~ .chat-area .quote-main {
            text-align: center; /* توسيط النص */
            padding: 0 5px; /* تقليل الهوامش الجانبية */
            margin: 10px auto; /* هوامش عمودية وتوسيط أفقي */
            width: 100%; /* عرض كامل */
            direction: rtl; /* اتجاه النص من اليمين إلى اليسار */
            font-size: 22px !important; /* زيادة حجم الخط ليكون مناسباً */
            white-space: nowrap !important; /* منع التفاف النص ليكون في سطر واحد */
            overflow-x: auto !important; /* إضافة شريط تمرير أفقي إذا كان النص طويلاً */
            color: #333 !important; /* توحيد اللون مع الشاشات الكبيرة */
            line-height: 1.4; /* تحسين المسافة بين السطور */
        }

        /* تنسيق مصدر الآية */
        .sidebar:not(.hidden) ~ .chat-area .quote-source {
            text-align: left; /* محاذاة إلى اليسار */
            margin-top: 10px; /* زيادة المسافة أعلى المصدر */
            font-size: 12px !important; /* تقليل حجم الخط */
            padding-left: 5px; /* إضافة مسافة من اليسار */
        }
    }

    /* تنسيقات للشاشات الصغيرة جدًا */
    @media (max-width: 480px) {
        body {
            padding-top: 3px;
        }

        /* تحسين التمرير في الشاشات الصغيرة جدًا */
        .chat-list {
            padding-bottom: 120px; /* زيادة المساحة في الأسفل للأجهزة الصغيرة جدًا */
            -webkit-overflow-scrolling: touch;
        }

        .whatsapp-container {
            height: 97vh;
            width: 100%;
            margin: 0;
            border-radius: 0;
        }

        .message {
            max-width: 90%;
        }

        .chat-header {
            padding: 4px 8px;
            height: 45px;
        }

        .input-area {
            padding: 4px 8px;
            position: fixed; /* تثبيت منطقة الإدخال في الأسفل */
            bottom: 0;
            left: 0;
            right: 0;
            width: 100%;
            background-color: var(--wa-panel-header-bg);
            z-index: 1000;
            border-top: 1px solid var(--wa-border-color);
        }

        .emoji-btn, .attach-btn, .voice-btn, .send-btn {
            width: 28px;
            height: 28px;
            font-size: 16px;
        }

        .message-input-container {
            margin: 0 4px;
        }

        .message-input {
            padding: 5px 6px;
            font-size: 14px; /* تكبير حجم الخط في مربع الإدخال */
        }

        .chat-actions {
            gap: 8px;
        }

        /* تنسيقات انزلاق حقل الكتابة عند ظهور لوحة المفاتيح */
        body.keyboard-open #chat-window-contents {
            padding-bottom: 70px; /* مساحة لعدم اختفاء الرسائل تحت حقل الكتابة */
        }

        /* إصلاح مشكلة لوحة المفاتيح للأجهزة المختلفة */
        body.keyboard-open #chat-window-footer {
            position: fixed;
            z-index: 1000;
        }

        /* دعم خاص بأجهزة iOS */
        @supports (-webkit-touch-callout: none) {
            body.keyboard-open #chat-window-footer {
                transform: translateZ(0);
                -webkit-transform: translateZ(0);
            }
        }

        .no-chat-icon {
            width: 100px;
            height: 100px;
            font-size: 50px;
            margin-bottom: 15px;
        }

        .welcome-title, .no-chat-selected h3 {
            font-size: 18px;
            margin-bottom: 6px;
        }

        .welcome-subtitle, .no-chat-selected p {
            font-size: 12px; /* تكبير حجم الخط للعنوان الفرعي */
        }

        .welcome-message {
            padding: 8px 12px;
            margin-top: 8px;
            border-width: 2px;
        }

        /* تكبير حجم الخط في رأس المحادثة للشاشات الصغيرة جداً */
        .contact-name {
            font-size: 15px; /* تكبير حجم الخط لاسم جهة الاتصال */
        }

        .contact-status {
            font-size: 12px; /* تكبير حجم الخط لحالة جهة الاتصال */
        }

        /* تكبير حجم الخط في الرسائل للشاشات الصغيرة جداً */
        .message-content p {
            font-size: 14px; /* تكبير حجم الخط لمحتوى الرسائل */
            line-height: 18px;
        }

        .message-time {
            font-size: 11px; /* تكبير حجم الخط لوقت الرسائل */
        }

        /* تكبير حجم الخط في شريط البحث للشاشات الصغيرة جداً */
        .search-input {
            font-size: 14px; /* تكبير حجم الخط في مربع البحث */
        }

        /* تكبير حجم الخط في قائمة المحادثات للشاشات الصغيرة جداً */
        .chat-name {
            font-size: 15px; /* تكبير حجم الخط لأسماء المحادثات */
        }

        .chat-message {
            font-size: 13px; /* تكبير حجم الخط للرسائل في القائمة */
        }

        .chat-time {
            font-size: 11px; /* تكبير حجم الخط لوقت المحادثات */
        }

        /* تصغير حجم الصور والأيقونات للشاشات الصغيرة جداً */
        .avatar-image {
            width: 35px;
            height: 35px;
        }

        .action-btn {
            width: 28px;
            height: 28px;
            font-size: 14px;
        }

        /* تكبير حجم الخط في الاقتباس للشاشات الصغيرة جداً */
        .quote-title {
            font-size: 24px !important; /* تكبير حجم الخط لعنوان الاقتباس */
        }

        .quote-main {
            font-size: 24px !important; /* تكبير حجم الخط لمحتوى الاقتباس */
        }

        .quote-source {
            font-size: 13px !important; /* تكبير حجم الخط لمصدر الاقتباس */
        }

        /* تكبير حجم الخط في مؤشرات الرسائل غير المقروءة */
        .unread-badge {
            font-size: 11px; /* تكبير حجم الخط لمؤشر الرسائل غير المقروءة */
            min-width: 18px;
            height: 18px;
        }

        /* تحسين المسافات بين العناصر للشاشات الصغيرة جداً */
        .messages-container {
            padding: 15px;
            padding-bottom: 55px; /* إضافة مساحة في الأسفل لتجنب تداخل الرسائل مع منطقة الإدخال */
        }

        .chat-date {
            font-size: 12px; /* تكبير حجم الخط لتاريخ المحادثة */
            padding: 4px 8px;
            margin: 8px auto;
        }

        /* تكبير حجم الخط في رسائل الحالة */
        .loading-messages, .no-messages, .error-message {
            font-size: 12px; /* تكبير حجم الخط لرسائل الحالة */
            padding: 15px;
        }

        /* تعديل حجم وموضع بطاقة الآية الكريمة في الشاشات الصغيرة جداً */
        .sidebar:not(.hidden) ~ .chat-area .no-chat-content {
            padding: 8px 6px;
        }

        /* زيادة عرض بطاقة الآية الكريمة للشاشات الصغيرة جداً */
        .sidebar:not(.hidden) ~ .chat-area .quote-container {
            max-width: 450px; /* زيادة العرض الأقصى للبطاقة */
            width: 95%; /* تحديد العرض النسبي */
        }

        /* تغيير نوع الخط وتصغير حجمه في بطاقة الآية الكريمة للشاشات الصغيرة جداً */
        .sidebar:not(.hidden) ~ .chat-area .quote-title,
        .sidebar:not(.hidden) ~ .chat-area .quote-main,
        .sidebar:not(.hidden) ~ .chat-area .quote-source {
            font-family: 'Noto Kufi Arabic', 'Droid Arabic Kufi', 'Cairo', 'Tajawal', sans-serif !important; /* خط أكثر احترافية للهاتف */
        }

        .sidebar:not(.hidden) ~ .chat-area .quote-title {
            font-size: 18px !important; /* زيادة حجم الخط ليكون مقروءاً */
            text-align: right !important; /* محاذاة إلى اليمين */
            margin-bottom: 8px !important; /* زيادة المسافة أسفل العنوان */
            color: #e67e5a !important; /* الحفاظ على نفس اللون */
        }

        .sidebar:not(.hidden) ~ .chat-area .quote-main {
            font-size: 18px !important; /* زيادة حجم الخط ليكون مقروءاً */
            white-space: nowrap !important; /* منع التفاف النص ليكون في سطر واحد */
            overflow-x: auto !important; /* إضافة شريط تمرير أفقي إذا كان النص طويلاً */
            text-align: center !important; /* تأكيد على توسيط النص */
            line-height: 1.4 !important; /* تحسين المسافة بين السطور */
            color: #333 !important; /* توحيد اللون مع الشاشات الكبيرة */
            margin: 0 auto !important; /* توسيط أفقي */
            padding: 5px !important; /* إضافة هوامش داخلية */
        }

        .sidebar:not(.hidden) ~ .chat-area .quote-source {
            font-size: 12px !important; /* زيادة حجم الخط ليكون مقروءاً */
            text-align: left !important; /* محاذاة إلى اليسار */
            color: #777 !important; /* الحفاظ على نفس اللون */
            margin-top: 8px !important; /* إضافة مسافة أعلى المصدر */
        }
    }

    /* تنسيقات لتثبيت العناصر عند التركيز على حقل الإدخال في وضع الهاتف */
    body.input-focused {
        padding-top: 0 !important; /* إزالة التباعد العلوي عند إخفاء شريط التنقل */
    }

    /* تثبيت رأس الشريط الجانبي في الأعلى */
    .sidebar-header.fixed-top {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1030;
        background-color: var(--wa-panel-header-bg);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    /* تثبيت منطقة الإدخال في الأسفل */
    .input-area.fixed-bottom {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1020;
        background-color: var(--wa-panel-header-bg);
        box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
    }

    /* تعديل منطقة المحادثة عند التركيز على حقل الإدخال */
    body.input-focused .chat-area {
        margin-top: 60px; /* مساحة لرأس الشريط الجانبي */
        margin-bottom: 60px; /* مساحة لمنطقة الإدخال */
        height: calc(100vh - 120px); /* تعديل الارتفاع ليناسب المساحة المتاحة */
        overflow-y: auto;
    }

    /* تنسيقات للشاشات الصغيرة جداً جداً */
    @media (max-width: 360px) {
        .chat-header {
            padding: 3px 6px;
            height: 40px;
        }

        .input-area {
            padding: 3px 6px;
            position: fixed; /* تثبيت منطقة الإدخال في الأسفل */
            bottom: 0;
            left: 0;
            right: 0;
            width: 100%;
            background-color: var(--wa-panel-header-bg);
            z-index: 1000;
        }

        .emoji-btn, .attach-btn, .voice-btn, .send-btn {
            width: 24px;
            height: 24px;
            font-size: 14px;
        }

        .message-input {
            padding: 4px 5px;
            font-size: 13px; /* تكبير حجم الخط في مربع الإدخال */
        }

        .avatar-image {
            width: 30px;
            height: 30px;
        }

        .action-btn {
            width: 24px;
            height: 24px;
            font-size: 12px;
        }

        .quote-title {
            font-size: 20px !important; /* تكبير حجم الخط لعنوان الاقتباس */
        }

        .quote-main {
            font-size: 20px !important; /* تكبير حجم الخط لمحتوى الاقتباس */
        }

        .quote-source {
            font-size: 12px !important; /* تكبير حجم الخط لمصدر الاقتباس */
        }

        .messages-container {
            padding: 10px;
            padding-bottom: 50px; /* إضافة مساحة في الأسفل لتجنب تداخل الرسائل مع منطقة الإدخال */
        }

        /* تكبير حجم الخط في الرسائل للشاشات الصغيرة جداً جداً */
        .message-content p {
            font-size: 13px; /* تكبير حجم الخط لمحتوى الرسائل */
            line-height: 17px;
        }

        /* تكبير حجم الخط في قائمة المحادثات للشاشات الصغيرة جداً جداً */
        .chat-name {
            font-size: 14px; /* تكبير حجم الخط لأسماء المحادثات */
        }

        .chat-message {
            font-size: 12px; /* تكبير حجم الخط للرسائل في القائمة */
        }

        .chat-time {
            font-size: 10px; /* تكبير حجم الخط لوقت المحادثات */
        }

        /* تعديل حجم وموضع بطاقة الآية الكريمة في الشاشات الصغيرة جداً جداً */
        .sidebar:not(.hidden) ~ .chat-area .no-chat-content {
            padding: 10px 8px;
        }

        /* زيادة عرض بطاقة الآية الكريمة للشاشات الصغيرة جداً جداً */
        .sidebar:not(.hidden) ~ .chat-area .quote-container {
            max-width: 400px; /* زيادة العرض الأقصى للبطاقة */
            width: 95%; /* تحديد العرض النسبي */
        }

        /* تغيير نوع الخط وتصغير حجمه في بطاقة الآية الكريمة للشاشات الصغيرة جداً جداً */
        .sidebar:not(.hidden) ~ .chat-area .quote-title,
        .sidebar:not(.hidden) ~ .chat-area .quote-main,
        .sidebar:not(.hidden) ~ .chat-area .quote-source {
            font-family: 'Noto Kufi Arabic', 'Droid Arabic Kufi', 'Cairo', 'Tajawal', sans-serif !important; /* خط أكثر احترافية للهاتف */
        }

        .sidebar:not(.hidden) ~ .chat-area .quote-title {
            font-size: 16px !important; /* زيادة حجم الخط ليكون مقروءاً */
            text-align: right !important; /* محاذاة إلى اليمين */
            margin-bottom: 6px !important; /* زيادة المسافة أسفل العنوان */
            color: #e67e5a !important; /* الحفاظ على نفس اللون */
        }

        .sidebar:not(.hidden) ~ .chat-area .quote-main {
            font-size: 16px !important; /* زيادة حجم الخط ليكون مقروءاً */
            white-space: nowrap !important; /* منع التفاف النص ليكون في سطر واحد */
            overflow-x: auto !important; /* إضافة شريط تمرير أفقي إذا كان النص طويلاً */
            text-align: center !important; /* تأكيد على توسيط النص */
            padding: 3px !important; /* إضافة هوامش داخلية */
            line-height: 1.3 !important; /* تحسين المسافة بين السطور */
            margin: 5px auto !important; /* توسيط أفقي مع هوامش عمودية */
            color: #333 !important; /* توحيد اللون مع الشاشات الكبيرة */
        }

        .sidebar:not(.hidden) ~ .chat-area .quote-source {
            font-size: 10px !important; /* زيادة حجم الخط ليكون مقروءاً */
            text-align: left !important; /* محاذاة إلى اليسار */
            margin-top: 6px !important; /* إضافة مسافة أعلى المصدر */
            color: #777 !important; /* الحفاظ على نفس اللون */
        }

        /* تعديل هوامش المحتوى */
        .sidebar:not(.hidden) ~ .chat-area .quote-content {
            padding: 12px 8px !important; /* تقليل الهوامش أكثر */
        }

        /* تقليل حجم الشريط البرتقالي في بطاقة الآية الكريمة */
        .sidebar:not(.hidden) ~ .chat-area .quote-sidebar {
            width: 25px; /* تقليل عرض الشريط البرتقالي */
        }
    }

    /* تحسينات إضافية للتصميم */
    .message.outgoing .message-content::before {
        content: '';
        position: absolute;
        top: 0;
        left: -8px;
        width: 0;
        height: 0;
        border-top: 8px solid var(--wa-outgoing-bg);
        border-left: 8px solid transparent;
    }

    .message.incoming .message-content::before {
        content: '';
        position: absolute;
        top: 0;
        right: -8px;
        width: 0;
        height: 0;
        border-top: 8px solid var(--wa-incoming-bg);
        border-right: 8px solid transparent;
    }

    /* تأثيرات حركية */
    .message {
        animation: message-pop 0.3s ease-out;
    }

    @keyframes message-pop {
        0% {
            opacity: 0;
            transform: scale(0.8);
        }
        100% {
            opacity: 1;
            transform: scale(1);
        }
    }

    .chat-item {
        position: relative;
        overflow: hidden;
    }

    .chat-item::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.05);
        transform: translateX(-100%);
        transition: transform 0.3s;
    }

    .chat-item:active::after {
        transform: translateX(0);
    }

    /* تنسيقات إضافية للرسائل */
    .message.sending .message-status i {
        color: #999;
    }

    .message.error .message-status i {
        color: #e74c3c;
    }

    /* تنسيقات حالة القراءة */
    .message-status i.fa-check {
        color: #999;
    }

    .message-status i.fa-check-double {
        color: #53bdeb;
    }

    .message-status i.fa-clock {
        color: #999;
    }

    /* تنسيقات زر الحذف */
    .delete-message {
        position: absolute;
        top: 4px;
        left: 4px;
        width: 20px;
        height: 20px;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        opacity: 0;
        transition: all 0.2s ease;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        z-index: 2;
    }

    .message-content {
        position: relative;
    }

    .message-content:hover .delete-message {
        opacity: 1;
    }

    .delete-message i {
        font-size: 10px;
        color: #888;
        transition: all 0.2s ease;
    }

    .delete-message:hover {
        background-color: rgba(255, 255, 255, 1);
        transform: scale(1.1);
    }

    .delete-message:hover i {
        color: #e74c3c;
    }

    /* تنسيقات الرسائل المحذوفة */
    .deleted-message {
        background-color: rgba(0, 0, 0, 0.05) !important;
    }

    .deleted-message p {
        font-style: italic;
        color: #888 !important;
    }

    /* تحسينات إضافية للتصميم */
    .message.outgoing .message-content::before {
        content: '';
        position: absolute;
        top: 0;
        right: -8px;
        width: 0;
        height: 0;
        border-left: 8px solid var(--wa-outgoing-bg);
        border-top: 8px solid transparent;
    }

    .message.incoming .message-content::before {
        content: '';
        position: absolute;
        top: 0;
        left: -8px;
        width: 0;
        height: 0;
        border-right: 8px solid var(--wa-incoming-bg);
        border-top: 8px solid transparent;
    }

    .loading-messages {
        padding: 20px;
        color: var(--wa-secondary-text);
        font-size: 14px;
    }

    .loading-messages i {
        margin-left: 8px;
        color: var(--wa-teal);
    }

    .no-messages {
        text-align: center;
        padding: 30px;
        color: var(--wa-secondary-text);
        font-size: 14px;
    }

    .error-message {
        text-align: center;
        padding: 20px;
        color: #e74c3c;
        font-size: 14px;
    }

    .loading-messages {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100px;
        color: #54656f;
        font-size: 14px;
    }

    .loading-messages i {
        margin-left: 10px;
        font-size: 18px;
    }

    .error-message {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100px;
        color: #e74c3c;
        font-size: 14px;
        text-align: center;
        padding: 0 20px;
    }

    .no-messages {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100px;
        color: #54656f;
        font-size: 14px;
        text-align: center;
        padding: 0 20px;
    }
</style>
<!-- تضمين ملف JavaScript لإصلاح مشكلة عرض منطقة الإدخال على الهواتف المحمولة -->
<script src="{{ url_for('static', filename='js/whatsapp-mobile-fix.js') }}"></script>
{% endblock %}

{% block content %}
<div class="container-fluid p-0 mt-3">
    <div class="whatsapp-container">
        <!-- الشريط الجانبي للمحادثات -->
        <div class="sidebar" id="sidebar">
            <!-- رأس الشريط الجانبي -->
            <div class="sidebar-header">
                <div class="user-profile">
                    {% if current_user.profile_picture %}
                        {% if '/static/images/' in current_user.profile_picture %}
                            <img src="{{ current_user.profile_picture }}" alt="{{ current_user.teacher_name }}" class="profile-image">
                        {% else %}
                            <img src="{{ url_for('static', filename='uploads/' + current_user.profile_picture.replace('/static/uploads/', '')) }}" alt="{{ current_user.teacher_name }}" class="profile-image">
                        {% endif %}
                    {% else %}
                        <img src="/static/images/male-profile.png" alt="{{ current_user.teacher_name }}" class="profile-image">
                    {% endif %}
                </div>
                <div class="sidebar-actions">
                    <button class="nav-btn teachers-btn" id="teachersBtn">
                        <i class="fas fa-user-friends"></i>
                        <span>الأساتذة</span>
                    </button>
                    <button class="nav-btn groups-btn" id="groupsBtn">
                        <i class="fas fa-users"></i>
                        <span>المجموعات</span>
                    </button>
                </div>
            </div>

            <!-- شريط البحث -->
            <div class="search-container">
                <div class="search-box">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" placeholder="ابحث أو ابدأ محادثة جديدة" class="search-input">
                    <i class="fas fa-times clear-search" id="clearSearch"></i>
                </div>
            </div>

            <!-- قائمة المحادثات -->
            <div class="chat-list">
                {% if chat_data %}
                    {% for chat in chat_data %}
                    <div class="chat-item" data-user-id="{{ chat.user_id }}" data-workplace="{{ chat.workplace|default('') }}">
                        <div class="chat-avatar">
                            {% if chat.profile_picture %}
                                {% if '/static/images/' in chat.profile_picture %}
                                    <img src="{{ chat.profile_picture }}" alt="{{ chat.name }}" class="avatar-image">
                                {% else %}
                                    <img src="{{ url_for('static', filename='uploads/' + chat.profile_picture.replace('/static/uploads/', '')) }}" alt="{{ chat.name }}" class="avatar-image">
                                {% endif %}
                            {% else %}
                                <img src="/static/images/male-profile.png" alt="{{ chat.name }}" class="avatar-image">
                            {% endif %}
                            <!-- إضافة مؤشر الحالة -->
                            <span class="sidebar-status-indicator {% if chat.is_online %}online{% else %}offline{% endif %}"></span>
                        </div>
                        <div class="chat-info">
                            <div class="chat-top">
                                <h6 class="chat-name">{{ chat.name }}</h6>
                                <span class="chat-time">{{ chat.timestamp }}</span>
                            </div>
                            <div class="chat-bottom">
                                <p class="chat-message">{{ chat.last_message }}</p>
                                <div class="chat-badges">
                                    {% if chat.unread_count > 0 %}
                                    <span class="unread-badge">{{ chat.unread_count }}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    {% for user in users %}
                    <div class="chat-item" data-user-id="{{ user.id }}" data-workplace="{{ user.workplace|default('') }}">
                        <div class="chat-avatar">
                            {% if user.profile_picture %}
                                {% if '/static/images/' in user.profile_picture %}
                                    <img src="{{ user.profile_picture }}" alt="{{ user.teacher_name }}" class="avatar-image">
                                {% else %}
                                    <img src="{{ url_for('static', filename='uploads/' + user.profile_picture.replace('/static/uploads/', '')) }}" alt="{{ user.teacher_name }}" class="avatar-image">
                                {% endif %}
                            {% else %}
                                <img src="/static/images/male-profile.png" alt="{{ user.teacher_name }}" class="avatar-image">
                            {% endif %}
                            <!-- إضافة مؤشر الحالة -->
                            <span class="sidebar-status-indicator {% if user.is_online %}online{% else %}offline{% endif %}"></span>
                        </div>
                        <div class="chat-info">
                            <div class="chat-top">
                                <h6 class="chat-name">{{ user.teacher_name }}</h6>
                                <span class="chat-time"></span>
                            </div>
                            <div class="chat-bottom">
                                <p class="chat-message">ابدأ محادثة جديدة...</p>
                                <div class="chat-badges"></div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% endif %}
            </div>

            <!-- قائمة المجموعات حسب مكان العمل -->
            <div class="groups-container" id="groupsContainer">
                <div class="groups-list" id="groupsList">
                    <!-- سيتم إضافة قائمة أماكن العمل ديناميكيًا هنا -->
                </div>
            </div>

            <!-- قائمة المستخدمين في مكان عمل محدد -->
            <div class="workplace-group" id="workplaceGroup">
                <div class="workplace-header">
                    <button class="back-btn" id="backToGroupsBtn">
                        <i class="fas fa-arrow-right"></i>
                    </button>
                    <h5 id="workplaceTitle">مكان العمل</h5>
                </div>
                <div class="workplace-users" id="workplaceUsers">
                    <!-- سيتم إضافة المستخدمين ديناميكيًا هنا -->
                </div>
            </div>
        </div>

        <!-- منطقة المحادثة -->
        <div class="chat-area" id="chatArea">
            <!-- رأس المحادثة -->
            <div class="chat-header">
                <button class="back-to-chats" id="backToChats">
                    <i class="fas fa-arrow-right"></i>
                </button>
                <div class="chat-contact">
                    <div class="contact-avatar">
                        <img src="/static/images/male-profile.png" alt="اسم المستخدم" id="currentChatAvatar" class="avatar-image">
                    </div>
                    <div class="contact-info">
                        <h6 class="contact-name" id="currentChatName">اختر محادثة</h6>
                        <p class="contact-status" id="contactStatus">
                            <span class="status-indicator online"></span>
                            <span class="status-text">متصل الآن</span>
                        </p>
                    </div>
                </div>
                <div class="chat-actions">
                    <button class="action-btn">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-paperclip"></i>
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>

            <!-- منطقة عرض الرسائل -->
            <div class="messages-container" id="messagesContainer">
                <div class="chat-date">اليوم</div>

                <!-- رسائل المحادثة (ستتم إضافتها ديناميكيًا) -->
                <div class="no-chat-selected" id="noChatSelected">
                    <div class="no-chat-content">
                        <div class="quote-container" id="quoteContainer">
                            <div class="quote-sidebar">
                                <button class="close-quote-btn" id="closeQuoteBtn">×</button>
                            </div>
                            <div class="quote-content">
                                <p class="quote-title">قال الله تعالى:</p>
                                <p class="quote-main">(مَا يَلْفِظُ مِنْ قَوْلٍ إِلَّا لَدَيْهِ رَقِيبٌ عَتِيدٌ)</p>
                                <p class="quote-source">سورة ق - الآية 18</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="messages" id="messages" style="display: none;">
                    <!-- نموذج للرسائل -->
                    <div class="message incoming">
                        <div class="message-content">
                            <p>مرحبا، كيف حالك؟</p>
                            <span class="message-time">10:30</span>
                        </div>
                    </div>

                    <div class="message outgoing">
                        <div class="message-content">
                            <p>أنا بخير، شكرا لك! ماذا عنك؟</p>
                            <span class="message-time">10:31</span>
                            <span class="message-status">
                                <i class="fas fa-check-double"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- شريط إدخال الرسائل -->
            <div class="input-area" id="inputArea" style="display: none;">
                <button class="emoji-btn">
                    <i class="far fa-smile"></i>
                </button>
                <button class="attach-btn">
                    <i class="fas fa-paperclip"></i>
                </button>
                <div class="message-input-container">
                    <input type="text" placeholder="اكتب رسالة" class="message-input" id="messageInput">
                </div>
                <button class="voice-btn" id="voiceBtn">
                    <i class="fas fa-microphone"></i>
                </button>
                <button class="send-btn" id="sendBtn" style="display: none;">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // متغير لتتبع ما إذا كان المستخدم قد اختار محادثة في وضع الهاتف
        let chatSelectedInMobileMode = false;

        // العناصر الرئيسية
        const sidebar = document.getElementById('sidebar');
        const chatArea = document.getElementById('chatArea');
        const chatItems = document.querySelectorAll('.chat-item');
        const backToChats = document.getElementById('backToChats');
        const noChatSelected = document.getElementById('noChatSelected');
        const noChatContent = noChatSelected.querySelector('.no-chat-content');
        const quoteContainer = document.getElementById('quoteContainer');
        const closeQuoteBtn = document.getElementById('closeQuoteBtn');
        const messages = document.getElementById('messages');
        const inputArea = document.getElementById('inputArea');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const voiceBtn = document.getElementById('voiceBtn');
        const currentChatName = document.getElementById('currentChatName');
        const currentChatAvatar = document.getElementById('currentChatAvatar');

        // معالجة زر إغلاق بطاقة الآية
        if (closeQuoteBtn && noChatContent) {
            closeQuoteBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                noChatContent.classList.add('hidden');

                // تخزين حالة الإغلاق في التخزين المحلي
                // حتى تبقى البطاقة مخفية حتى يتم تحديث الصفحة
                localStorage.setItem('quoteHidden', 'true');
            });

            // التحقق من حالة البطاقة في التخزين المحلي عند تحميل الصفحة
            const quoteHidden = localStorage.getItem('quoteHidden');
            if (quoteHidden === 'true') {
                // إذا كانت البطاقة مخفية سابقاً، نبقيها مخفية
                noChatContent.classList.add('hidden');
            } else {
                // إذا لم تكن البطاقة مخفية سابقاً، نظهرها
                noChatContent.classList.remove('hidden');
            }
        }

        // عناصر المجموعات
        const groupsContainer = document.getElementById('groupsContainer');
        const groupsList = document.getElementById('groupsList');

        // أزرار التنقل الجديدة
        const teachersBtn = document.getElementById('teachersBtn');
        const groupsBtn = document.getElementById('groupsBtn');

        // عناصر مجموعة مكان العمل
        const workplaceGroup = document.getElementById('workplaceGroup');
        const workplaceTitle = document.getElementById('workplaceTitle');
        const workplaceUsers = document.getElementById('workplaceUsers');
        const backToGroupsBtn = document.getElementById('backToGroupsBtn');

        // متغيرات للتحديث التلقائي
        let currentChatId = null;
        let lastMessageId = null;
        let pollingInterval = null;
        let lastMessageCount = 0;
        let sentMessageIds = []; // لتخزين معرفات الرسائل المرسلة

        // إضافة وظيفة البحث في المحادثات
        const searchInput = document.querySelector('.search-input');
        const clearSearchBtn = document.getElementById('clearSearch');

        if (searchInput) {
            // دالة البحث
            function performSearch() {
                const searchTerm = searchInput.value.trim().toLowerCase();

                // تحديد ما إذا كنا في قائمة المحادثات أو قائمة المجموعات
                const isInGroups = groupsContainer.style.display === 'flex';

                // إظهار/إخفاء زر المسح
                if (searchTerm.length > 0) {
                    clearSearchBtn.classList.add('visible');
                } else {
                    clearSearchBtn.classList.remove('visible');
                }

                if (isInGroups) {
                    // البحث في المجموعات
                    const allWorkplaceItems = document.querySelectorAll('.workplace-item');

                    allWorkplaceItems.forEach(item => {
                        const workplaceName = item.querySelector('.workplace-name').textContent.toLowerCase();
                        const teacherCount = item.querySelector('.workplace-count').textContent.toLowerCase();

                        // البحث في اسم مكان العمل وعدد الأساتذة
                        if (workplaceName.includes(searchTerm) || teacherCount.includes(searchTerm)) {
                            item.style.display = 'flex';
                        } else {
                            item.style.display = 'none';
                        }
                    });

                    // التحقق من وجود نتائج
                    const visibleItems = Array.from(document.querySelectorAll('.workplace-item')).filter(item => item.style.display !== 'none');
                    const emptyMessage = document.querySelector('.groups-list .empty-message');

                    // إذا لم توجد نتائج وكان هناك بحث، عرض رسالة
                    if (visibleItems.length === 0 && searchTerm.length > 0) {
                        // إزالة الرسالة القديمة إذا وجدت
                        if (emptyMessage) {
                            emptyMessage.remove();
                        }

                        // إضافة رسالة جديدة
                        const newEmptyMessage = document.createElement('div');
                        newEmptyMessage.className = 'empty-message';
                        newEmptyMessage.textContent = 'لا توجد نتائج مطابقة للبحث';
                        groupsList.appendChild(newEmptyMessage);
                    } else if (visibleItems.length > 0 && emptyMessage) {
                        // إزالة رسالة "لا توجد نتائج" إذا وجدت نتائج
                        emptyMessage.remove();
                    }
                } else {
                    // البحث في المحادثات
                    const allChatItems = document.querySelectorAll('.chat-item');

                    allChatItems.forEach(item => {
                        const chatName = item.querySelector('.chat-name').textContent.toLowerCase();
                        const chatMessage = item.querySelector('.chat-message').textContent.toLowerCase();

                        // البحث في اسم المحادثة وآخر رسالة
                        if (chatName.includes(searchTerm) || chatMessage.includes(searchTerm)) {
                            item.style.display = 'flex';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                }
            }

            // إضافة مستمع حدث للبحث
            searchInput.addEventListener('input', performSearch);

            // إضافة مستمع حدث للتركيز على حقل البحث
            searchInput.addEventListener('focus', function(e) {
                // منع إخفاء الشريط الجانبي عند التركيز على حقل البحث في وضع الهاتف
                if (window.innerWidth <= 768) {
                    // التأكد من أن الشريط الجانبي مرئي
                    sidebar.classList.remove('hidden');
                    // منع الانتقال إلى صفحة الدردشة
                    e.stopPropagation();
                }
            });

            // إضافة مستمع حدث لزر المسح
            if (clearSearchBtn) {
                clearSearchBtn.addEventListener('click', function() {
                    searchInput.value = '';
                    performSearch();
                    searchInput.focus();
                });
            }
        }

        // إضافة بيانات وهمية للمحادثات إذا لم تكن موجودة
        if (chatItems.length === 0) {
            const dummyUsers = [
                { name: 'أحمد محمد', lastMessage: 'مرحبا، كيف حالك؟', time: '10:30', unread: 2 },
                { name: 'سارة علي', lastMessage: 'شكرا لك على المساعدة', time: '09:15', unread: 0 },
                { name: 'محمد خالد', lastMessage: 'سأكون هناك في الساعة 5', time: 'أمس', unread: 1 },
                { name: 'فاطمة أحمد', lastMessage: 'هل يمكننا مناقشة المشروع غدا؟', time: 'أمس', unread: 3 },
                { name: 'عمر حسن', lastMessage: 'تم إرسال الملفات المطلوبة', time: '22/04', unread: 0 }
            ];

            const chatList = document.querySelector('.chat-list');
            if (chatList) {
                dummyUsers.forEach(user => {
                    const chatItem = document.createElement('div');
                    chatItem.className = 'chat-item';
                    chatItem.innerHTML = `
                        <div class="chat-avatar">
                            <img src="/static/images/male-profile.png" alt="${user.name}" class="avatar-image">
                            <span class="sidebar-status-indicator offline"></span>
                        </div>
                        <div class="chat-info">
                            <div class="chat-top">
                                <h6 class="chat-name">${user.name}</h6>
                                <span class="chat-time">${user.time}</span>
                            </div>
                            <div class="chat-bottom">
                                <p class="chat-message">${user.lastMessage}</p>
                                <div class="chat-badges">
                                    ${user.unread > 0 ? `<span class="unread-badge">${user.unread}</span>` : ''}
                                </div>
                            </div>
                        </div>
                    `;
                    chatList.appendChild(chatItem);
                });

                // إعادة تحديد العناصر بعد إضافة البيانات الوهمية
                const newChatItems = document.querySelectorAll('.chat-item');
                if (newChatItems.length > 0) {
                    newChatItems.forEach(item => {
                        item.addEventListener('click', handleChatItemClick);
                    });
                }
            }
        }

        // دالة لتحديث المحادثات بشكل دوري
        function startPolling() {
            // إيقاف أي استطلاع سابق
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }

            // بدء استطلاع جديد كل 3 ثوانٍ
            pollingInterval = setInterval(() => {
                if (currentChatId) {
                    checkForNewMessages();
                    checkReadStatus();
                }
            }, 3000);
        }

        // دالة للتحقق من حالة قراءة الرسائل
        function checkReadStatus() {
            // التحقق فقط إذا كان هناك رسائل مرسلة
            if (sentMessageIds.length === 0) return;

            fetch('/api/check_read_status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message_ids: sentMessageIds
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    const messagesStatus = data.data;

                    // تحديث حالة كل رسالة
                    for (const msgId in messagesStatus) {
                        const status = messagesStatus[msgId];

                        // تحديث فقط الرسائل المرسلة من المستخدم الحالي والتي تم قراءتها
                        if (status.is_read && status.sender_id == {{ current_user.id }}) {
                            // البحث عن الرسالة في واجهة المستخدم
                            const messageElements = document.querySelectorAll('.message.outgoing');
                            messageElements.forEach(element => {
                                const messageIdAttr = element.getAttribute('data-message-id');
                                if (messageIdAttr && messageIdAttr == msgId) {
                                    // تحديث أيقونة الحالة
                                    const statusIcon = element.querySelector('.message-status i');
                                    if (statusIcon) {
                                        statusIcon.className = 'fas fa-check-double';
                                    }
                                }
                            });
                        }
                    }
                }
            })
            .catch(error => {
                console.error('خطأ في التحقق من حالة القراءة:', error);
            });
        }

        // دالة للتحقق من وجود رسائل جديدة أو محذوفة
        function checkForNewMessages() {
            fetch(`/api/get_whatsapp_messages/${currentChatId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        const newMessages = data.data.messages;

                        // الحصول على معرفات الرسائل الحالية في واجهة المستخدم
                        const currentMessageElements = document.querySelectorAll('.message[data-message-id]');
                        const currentMessageIds = Array.from(currentMessageElements).map(el =>
                            parseInt(el.getAttribute('data-message-id'))
                        );

                        // التحقق من الرسائل المحذوفة (موجودة في واجهة المستخدم ولكن ليست في الرد)
                        const newMessageIds = newMessages.map(msg => msg.id);
                        const deletedMessageIds = currentMessageIds.filter(id => !newMessageIds.includes(id));

                        // حذف الرسائل المحذوفة من واجهة المستخدم
                        if (deletedMessageIds.length > 0) {
                            let messagesDeleted = false;

                            deletedMessageIds.forEach(id => {
                                const messageElement = document.querySelector(`.message[data-message-id="${id}"]`);
                                if (messageElement) {
                                    // حذف الرسالة بتأثير متلاشي
                                    messageElement.style.transition = 'all 0.3s ease';
                                    messageElement.style.opacity = '0';
                                    messageElement.style.height = '0';
                                    messageElement.style.marginBottom = '0';
                                    messageElement.style.overflow = 'hidden';

                                    messagesDeleted = true;

                                    // حذف العنصر من DOM بعد انتهاء التأثير
                                    setTimeout(() => {
                                        messageElement.remove();
                                    }, 300);
                                }
                            });

                            // إذا تم حذف رسائل، قم بتحديث القائمة الجانبية بعد انتهاء التأثير
                            if (messagesDeleted) {
                                setTimeout(() => {
                                    updateSidebarLastMessage(currentChatId);
                                }, 350);
                            }
                        }

                        // التحقق مما إذا كانت هناك رسائل جديدة أو تغييرات
                        const hasChanges = newMessages.length !== lastMessageCount || deletedMessageIds.length > 0;

                        if (hasChanges) {
                            // تحديث الرسائل
                            updateMessages(newMessages);

                            // تحديث عدد الرسائل
                            lastMessageCount = newMessages.length;

                            // تحديث آخر معرف رسالة
                            if (newMessages.length > 0) {
                                lastMessageId = newMessages[newMessages.length - 1].id;
                            }

                            // تمرير إلى أسفل لعرض أحدث الرسائل
                            scrollToBottom();
                        }
                    }
                })
                .catch(error => {
                    console.error('خطأ في التحقق من الرسائل الجديدة:', error);
                });
        }

        // دالة لتحديث الرسائل في واجهة المستخدم
        function updateMessages(messagesData) {
            // الحصول على معرفات الرسائل الحالية في واجهة المستخدم
            const currentMessageElements = document.querySelectorAll('.message[data-message-id]');
            const currentMessageIds = Array.from(currentMessageElements).map(el =>
                parseInt(el.getAttribute('data-message-id'))
            );

            // تحديث قائمة الرسائل المرسلة
            sentMessageIds = [];

            // إذا كانت منطقة الرسائل فارغة، نقوم بإضافة جميع الرسائل
            if (messages.innerHTML.trim() === '' || currentMessageElements.length === 0) {
                messages.innerHTML = '';

                // عرض الرسائل الجديدة
                messagesData.forEach(msg => {
                    addMessageToUI(msg);

                    // إذا كانت الرسالة مرسلة من المستخدم الحالي، أضفها إلى قائمة الرسائل المرسلة
                    if (msg.is_outgoing) {
                        sentMessageIds.push(msg.id);
                    }
                });
            } else {
                // إضافة الرسائل الجديدة فقط
                messagesData.forEach(msg => {
                    // التحقق مما إذا كانت الرسالة موجودة بالفعل
                    if (!currentMessageIds.includes(msg.id)) {
                        addMessageToUI(msg);
                    } else {
                        // تحديث حالة الرسائل الموجودة (مثل حالة القراءة)
                        updateExistingMessage(msg);
                    }

                    // إذا كانت الرسالة مرسلة من المستخدم الحالي، أضفها إلى قائمة الرسائل المرسلة
                    if (msg.is_outgoing) {
                        sentMessageIds.push(msg.id);
                    }
                });
            }
        }

        // دالة لإضافة رسالة جديدة إلى واجهة المستخدم
        function addMessageToUI(msg) {
            const messageElement = document.createElement('div');
            messageElement.className = msg.is_outgoing ? 'message outgoing' : 'message incoming';

            // إضافة معرف الرسالة كسمة للعنصر
            messageElement.setAttribute('data-message-id', msg.id);

            // تحديد ما إذا كانت الرسالة محذوفة
            const isDeleted = msg.is_deleted || false;

            // إعداد أيقونة الحالة
            let statusIcon = '';
            if (msg.is_outgoing) {
                statusIcon = `<span class="message-status">
                    <i class="fas fa-${msg.is_read ? 'check-double' : 'check'}"></i>
                </span>`;
            }

            // إضافة زر الحذف للرسائل المرسلة غير المحذوفة
            let deleteButton = '';
            if (msg.is_outgoing && !isDeleted) {
                deleteButton = `<span class="delete-message" data-message-id="${msg.id}">
                    <i class="fas fa-times"></i>
                </span>`;
            }

            // تنسيق مختلف للرسائل المحذوفة
            const messageClass = isDeleted ? 'deleted-message' : '';

            messageElement.innerHTML = `
                <div class="message-content ${messageClass}">
                    <p>${msg.content}</p>
                    <span class="message-time">${msg.timestamp}</span>
                    ${statusIcon}
                    ${deleteButton}
                </div>
            `;

            messages.appendChild(messageElement);
        }

        // دالة لتحديث رسالة موجودة في واجهة المستخدم
        function updateExistingMessage(msg) {
            const messageElement = document.querySelector(`.message[data-message-id="${msg.id}"]`);
            if (messageElement) {
                // تحديث حالة القراءة
                if (msg.is_outgoing) {
                    const statusIcon = messageElement.querySelector('.message-status i');
                    if (statusIcon && msg.is_read) {
                        statusIcon.className = 'fas fa-check-double';
                    }
                }
            }
        }

        // دالة معالجة النقر على عنصر المحادثة
        function handleChatItemClick() {
            // إزالة الفئة النشطة من جميع العناصر
            document.querySelectorAll('.chat-item').forEach(chat => chat.classList.remove('active'));

            // إضافة الفئة النشطة للعنصر المحدد
            this.classList.add('active');

            // الحصول على معرف المستخدم
            const userId = this.getAttribute('data-user-id');

            // إظهار منطقة المحادثة وإخفاء رسالة "اختر محادثة"
            noChatSelected.style.display = 'none';

            // التأكد من إعادة تعيين تنسيقات بطاقة الآية
            const noChatContent = noChatSelected.querySelector('.no-chat-content');
            if (noChatContent) {
                // إعادة تعيين التنسيقات لمنع ظهور المساحة البيضاء لاحقاً
                noChatContent.style.position = '';
                noChatContent.style.transform = '';
                noChatContent.style.boxShadow = '';
                noChatContent.style.borderTop = '';
                noChatContent.style.padding = '';
                noChatContent.style.backgroundColor = '';
            }

            // إظهار منطقة الإدخال فقط عند اختيار محادثة
            if (inputArea) {
                inputArea.style.display = 'flex';
            }

            // في الشاشات الصغيرة، إخفاء الشريط الجانبي وإظهار منطقة المحادثة
            if (window.innerWidth <= 768) {
                sidebar.classList.add('hidden');
                // تحديث المتغير لتتبع أن المستخدم قد اختار محادثة في وضع الهاتف
                chatSelectedInMobileMode = true;
            }

            // إيقاف الاستطلاع الحالي إذا كان هناك محادثة مفتوحة
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }

            // تحميل رسائل المحادثة
            loadMessages(userId);
        }

        // إيقاف الاستطلاع عند مغادرة الصفحة
        window.addEventListener('beforeunload', function() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }
        });

        // إضافة معالج حدث النقر على زر الحذف
        document.addEventListener('click', function(event) {
            // التحقق مما إذا كان العنصر المنقور عليه هو زر الحذف أو أحد أبنائه
            const deleteButton = event.target.closest('.delete-message');
            if (deleteButton) {
                // الحصول على معرف الرسالة
                const messageId = deleteButton.getAttribute('data-message-id');
                if (messageId) {
                    // حذف الرسالة مباشرة بدون تأكيد
                    deleteMessage(messageId);
                }
            }
        });

        // دالة لحذف الرسالة
        function deleteMessage(messageId) {
            fetch(`/api/delete_whatsapp_message/${messageId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // حذف الرسالة تمامًا من واجهة المستخدم
                    const messageElement = document.querySelector(`.message[data-message-id="${messageId}"]`);
                    if (messageElement) {
                        // حذف الرسالة بتأثير متلاشي
                        messageElement.style.transition = 'all 0.3s ease';
                        messageElement.style.opacity = '0';
                        messageElement.style.height = '0';
                        messageElement.style.marginBottom = '0';
                        messageElement.style.overflow = 'hidden';

                        // حذف العنصر من DOM بعد انتهاء التأثير
                        setTimeout(() => {
                            messageElement.remove();

                            // تحديث آخر رسالة في القائمة الجانبية
                            updateSidebarLastMessage(currentChatId);
                        }, 300);
                    }
                } else {
                    console.error('فشل في حذف الرسالة:', data.message);
                    alert('حدث خطأ أثناء حذف الرسالة. يرجى المحاولة مرة أخرى.');
                }
            })
            .catch(error => {
                console.error('خطأ في حذف الرسالة:', error);
                alert('حدث خطأ أثناء حذف الرسالة. يرجى المحاولة مرة أخرى.');
            });
        }

        // دالة لتحديث آخر رسالة في القائمة الجانبية
        function updateSidebarLastMessage(userId) {
            // الحصول على آخر رسالة في المحادثة الحالية
            const messages = document.querySelectorAll('.message');
            let lastMessageText = 'لا توجد رسائل';

            if (messages.length > 0) {
                // الحصول على آخر رسالة
                const lastMessage = messages[messages.length - 1];
                const messageContent = lastMessage.querySelector('p');
                if (messageContent) {
                    lastMessageText = messageContent.textContent;
                }
            }

            // تحديث آخر رسالة في القائمة الجانبية
            const chatItem = document.querySelector(`.chat-item[data-user-id="${userId}"]`);
            if (chatItem) {
                const chatMessage = chatItem.querySelector('.chat-message');
                if (chatMessage) {
                    // تحديث نص الرسالة مع تأثير متلاشي
                    chatMessage.style.transition = 'opacity 0.3s ease';
                    chatMessage.style.opacity = '0';

                    setTimeout(() => {
                        chatMessage.textContent = lastMessageText;
                        chatMessage.style.opacity = '1';
                    }, 300);
                }
            }
        }

        // التبديل بين المحادثات
        chatItems.forEach(item => {
            item.addEventListener('click', handleChatItemClick);
        });

        // دالة لتحميل رسائل المحادثة
        function loadMessages(userId) {
            // تحديث معرف المحادثة الحالية
            currentChatId = userId;

            // إظهار مؤشر التحميل
            messages.innerHTML = '<div class="loading-messages"><i class="fas fa-spinner fa-spin"></i> جاري تحميل الرسائل...</div>';
            messages.style.display = 'flex';

            // جلب الرسائل من الخادم
            fetch(`/api/get_whatsapp_messages/${userId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // تحديث معلومات المستخدم
                        currentChatName.textContent = data.data.user.name;
                        if (data.data.user.profile_picture) {
                            currentChatAvatar.src = data.data.user.profile_picture;
                        } else {
                            currentChatAvatar.src = '/static/images/male-profile.png';
                        }

                        // التأكد من إظهار منطقة الإدخال
                        if (inputArea) {
                            inputArea.style.display = 'flex';
                        }

                        // الحصول على حالة المستخدم من الخادم
                        getUserStatus(userId);

                        // عرض الرسائل
                        messages.innerHTML = '';

                        if (data.data.messages.length === 0) {
                            // لا توجد رسائل
                            messages.innerHTML = '<div class="no-messages">لا توجد رسائل. ابدأ المحادثة الآن!</div>';
                            lastMessageCount = 0;
                            lastMessageId = null;
                        } else {
                            // عرض الرسائل
                            updateMessages(data.data.messages);

                            // تحديث عدد الرسائل وآخر معرف رسالة
                            lastMessageCount = data.data.messages.length;
                            if (data.data.messages.length > 0) {
                                lastMessageId = data.data.messages[data.data.messages.length - 1].id;
                            }
                        }

                        // تمرير إلى أسفل لعرض أحدث الرسائل
                        scrollToBottom();

                        // تحديث شارة الرسائل غير المقروءة
                        const unreadBadge = document.querySelector(`.chat-item[data-user-id="${userId}"] .unread-badge`);
                        if (unreadBadge) {
                            unreadBadge.style.display = 'none';
                        }

                        // بدء الاستطلاع الدوري للرسائل الجديدة
                        startPolling();
                    } else {
                        console.error('فشل في تحميل الرسائل:', data.message);
                        messages.innerHTML = '<div class="error-message">حدث خطأ أثناء تحميل الرسائل. يرجى المحاولة مرة أخرى.</div>';
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحميل الرسائل:', error);
                    messages.innerHTML = '<div class="error-message">حدث خطأ أثناء تحميل الرسائل. يرجى المحاولة مرة أخرى.</div>';
                });
        }

        // زر العودة إلى قائمة المحادثات (للشاشات الصغيرة)
        backToChats.addEventListener('click', function() {
            sidebar.classList.remove('hidden');

            // إخفاء منطقة الإدخال عند العودة لقائمة المحادثات
            if (inputArea) {
                inputArea.style.display = 'none';
            }

            // إظهار رسالة "اختر محادثة" بشكل صحيح ولكن بدون بطاقة الآية
            if (noChatSelected) {
                noChatSelected.style.display = 'flex';

                // إخفاء بطاقة الآية عند الرجوع
                const noChatContent = noChatSelected.querySelector('.no-chat-content');
                if (noChatContent) {
                    noChatContent.classList.add('hidden');

                    // تخزين حالة الإخفاء في التخزين المحلي
                    localStorage.setItem('quoteHidden', 'true');

                    // إعادة تعيين التنسيقات لمنع ظهور المساحة البيضاء
                    noChatContent.style.position = '';
                    noChatContent.style.transform = '';
                    noChatContent.style.boxShadow = '';
                    noChatContent.style.borderTop = '';
                    noChatContent.style.padding = '';
                    noChatContent.style.backgroundColor = '';
                }
            }

            // تفريغ حقل البحث وإظهار جميع العناصر في القائمة
            if (searchInput) {
                searchInput.value = '';
                // إظهار جميع عناصر المحادثة
                document.querySelectorAll('.chat-item').forEach(item => {
                    item.style.display = 'flex';
                });
                // إخفاء زر المسح
                if (clearSearchBtn) {
                    clearSearchBtn.classList.remove('visible');
                }
            }

            // إزالة تحديد جميع العناصر في القائمة
            document.querySelectorAll('.chat-item').forEach(item => {
                item.classList.remove('active');
            });

            // إعادة ضبط متغير تتبع اختيار المحادثة في وضع الهاتف
            chatSelectedInMobileMode = false;

            // إعادة ضبط معرف المحادثة الحالية
            currentChatId = null;
        });

        // تبديل بين زر الميكروفون وزر الإرسال عند الكتابة
        messageInput.addEventListener('input', function() {
            if (this.value.trim() !== '') {
                voiceBtn.style.display = 'none';
                sendBtn.style.display = 'flex';
            } else {
                voiceBtn.style.display = 'flex';
                sendBtn.style.display = 'none';
            }
        });

        // إضافة مستمع حدث للتركيز على حقل الإدخال
        messageInput.addEventListener('focus', function() {
            // التأكد من أن الشريط الجانبي يبقى مخفياً عند التركيز على حقل الإدخال في الشاشات الصغيرة
            if (window.innerWidth <= 768 && sidebar) {
                sidebar.classList.add('hidden');
            }
        });

        // إرسال رسالة جديدة
        sendBtn.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        function sendMessage() {
            const messageText = messageInput.value.trim();
            if (messageText === '') return;

            // الحصول على معرف المستخدم المحدد
            const activeChat = document.querySelector('.chat-item.active');
            if (!activeChat) {
                console.error('لم يتم تحديد محادثة');
                return;
            }

            const recipientId = activeChat.getAttribute('data-user-id');

            // إنشاء عنصر الرسالة الجديدة مؤقتًا
            const now = new Date();
            const timeString = now.getHours() + ':' + (now.getMinutes() < 10 ? '0' : '') + now.getMinutes();

            const messageElement = document.createElement('div');
            messageElement.className = 'message outgoing sending';
            messageElement.innerHTML = `
                <div class="message-content">
                    <p>${messageText}</p>
                    <span class="message-time">${timeString}</span>
                    <span class="message-status">
                        <i class="fas fa-clock"></i>
                    </span>
                </div>
            `;

            // إضافة الرسالة إلى المحادثة
            messages.appendChild(messageElement);

            // مسح حقل الإدخال
            messageInput.value = '';

            // إعادة زر الميكروفون
            voiceBtn.style.display = 'flex';
            sendBtn.style.display = 'none';

            // تمرير إلى أسفل لعرض الرسالة الجديدة
            scrollToBottom();

            // إرسال الرسالة إلى الخادم
            fetch('/api/send_whatsapp_message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipient_id: recipientId,
                    message: messageText
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // تحديث حالة الرسالة
                    messageElement.classList.remove('sending');
                    const statusIcon = messageElement.querySelector('.message-status i');
                    statusIcon.className = 'fas fa-check';

                    // إضافة معرف الرسالة إلى العنصر
                    const messageId = data.data.id;
                    messageElement.setAttribute('data-message-id', messageId);

                    // إضافة معرف الرسالة إلى قائمة الرسائل المرسلة
                    sentMessageIds.push(messageId);

                    // إضافة زر الحذف للرسالة
                    const messageContent = messageElement.querySelector('.message-content');
                    if (messageContent) {
                        const deleteButton = document.createElement('span');
                        deleteButton.className = 'delete-message';
                        deleteButton.setAttribute('data-message-id', messageId);
                        deleteButton.innerHTML = '<i class="fas fa-times"></i>';
                        messageContent.appendChild(deleteButton);
                    }

                    // تحديث قائمة المحادثات
                    updateChatList(recipientId, messageText, timeString);

                    // تحديث آخر رسالة في القائمة الجانبية
                    updateSidebarLastMessage(recipientId);
                } else {
                    console.error('فشل في إرسال الرسالة:', data.message);
                    // إظهار رسالة خطأ
                    messageElement.classList.add('error');
                    const statusIcon = messageElement.querySelector('.message-status i');
                    statusIcon.className = 'fas fa-exclamation-circle';
                }
            })
            .catch(error => {
                console.error('خطأ في إرسال الرسالة:', error);
                // إظهار رسالة خطأ
                messageElement.classList.add('error');
                const statusIcon = messageElement.querySelector('.message-status i');
                statusIcon.className = 'fas fa-exclamation-circle';
            });
        }

        // دالة لتحديث قائمة المحادثات
        function updateChatList(userId, lastMessage, timestamp) {
            const chatItem = document.querySelector(`.chat-item[data-user-id="${userId}"]`);
            if (chatItem) {
                // تحديث آخر رسالة
                const messageElement = chatItem.querySelector('.chat-message');
                if (messageElement) {
                    messageElement.textContent = lastMessage;
                }

                // تحديث الوقت
                const timeElement = chatItem.querySelector('.chat-time');
                if (timeElement) {
                    timeElement.textContent = timestamp;
                }

                // نقل المحادثة إلى أعلى القائمة
                const chatList = chatItem.parentElement;
                chatList.insertBefore(chatItem, chatList.firstChild);
            }
        }

        // تمرير إلى أسفل المحادثة
        function scrollToBottom() {
            const messagesContainer = document.getElementById('messagesContainer');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // التعامل مع تغيير حجم النافذة
        window.addEventListener('resize', function() {
            // فقط في حالة الشاشات الكبيرة (أكبر من 768 بكسل) نقوم بإظهار الشريط الجانبي
            if (window.innerWidth > 768) {
                sidebar.classList.remove('hidden');
            } else {
                // في حالة الشاشات الصغيرة، نتحقق مما إذا كان المستخدم قد اختار محادثة سابقاً
                if (chatSelectedInMobileMode) {
                    // إذا كان المستخدم قد اختار محادثة سابقاً، نبقي الشريط الجانبي مخفياً
                    sidebar.classList.add('hidden');
                } else {
                    // إذا لم يكن المستخدم قد اختار محادثة سابقاً، نتحقق من وجود محادثة نشطة
                    const activeChat = document.querySelector('.chat-item.active');
                    if (activeChat) {
                        // إذا كانت هناك محادثة نشطة، نبقي الشريط الجانبي مخفياً
                        sidebar.classList.add('hidden');
                        // تحديث المتغير
                        chatSelectedInMobileMode = true;
                    }
                }
            }
        });

        // ===== بداية كود تتبع حالة المستخدم =====
        // هذا الكود يتتبع نشاط المستخدم ويغير حالته إلى "غير متصل" بعد 3 دقائق من عدم النشاط
        // ويعيده إلى حالة "متصل" عندما يعود للتصفح أو يحرك الماوس أو يقوم بأي حركة

        // متغيرات لتتبع نشاط المستخدم
        let userActivity = true;
        let userActivityTimer = null;
        let lastActivityTime = new Date();
        const inactivityTimeout = 180000; // 3 دقائق من عدم النشاط

        // دالة لتنسيق وقت آخر ظهور
        function formatLastSeen(lastSeenDate) {
            if (!lastSeenDate) return 'غير متصل';

            // تحويل التاريخ من نص إلى كائن Date
            const lastSeen = new Date(lastSeenDate);
            const now = new Date();

            // حساب الفرق بالدقائق
            const diffMs = now - lastSeen;
            const diffMinutes = Math.floor(diffMs / 60000);

            // حساب الفرق بالساعات
            const diffHours = Math.floor(diffMinutes / 60);

            // حساب الفرق بالأيام
            const diffDays = Math.floor(diffHours / 24);

            // تنسيق النص حسب المدة
            if (diffMinutes < 1) {
                return 'الآن';
            } else if (diffMinutes < 60) {
                return `منذ ${diffMinutes} دقيقة`;
            } else if (diffHours < 24) {
                return `منذ ${diffHours} ساعة`;
            } else if (diffDays === 1) {
                return 'الأمس';
            } else if (diffDays < 7) {
                return `منذ ${diffDays} أيام`;
            } else {
                // تنسيق التاريخ للفترات الأطول
                const options = { year: 'numeric', month: 'long', day: 'numeric' };
                return lastSeen.toLocaleDateString('ar-SA', options);
            }
        }

        // دالة لتحديث واجهة المستخدم بحالة المستخدم الآخر
        function updateUserStatusUI(isOnline, lastSeen) {
            const statusIndicator = document.querySelector('.status-indicator');
            const statusText = document.querySelector('.status-text');

            if (statusIndicator && statusText) {
                if (isOnline) {
                    statusIndicator.classList.remove('offline');
                    statusIndicator.classList.add('online');
                    statusText.textContent = 'متصل الآن';
                } else {
                    statusIndicator.classList.remove('online');
                    statusIndicator.classList.add('offline');

                    // عرض آخر ظهور إذا كان متوفرًا
                    if (lastSeen) {
                        statusText.textContent = `آخر ظهور ${formatLastSeen(lastSeen)}`;
                    } else {
                        statusText.textContent = 'غير متصل';
                    }
                }
            }
        }

        // دالة للحصول على حالة مستخدم آخر
        function getUserStatus(userId) {
            fetch(`/api/get_user_status/${userId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // تحديث حالة المستخدم في رأس المحادثة
                        updateUserStatusUI(data.data.is_online, data.data.last_seen);

                        // تحديث حالة المستخدم في القائمة الجانبية
                        updateSidebarStatusUI(userId, data.data.is_online, data.data.last_seen);
                    }
                })
                .catch(error => {
                    console.error('خطأ في الحصول على حالة المستخدم:', error);
                });
        }

        // دالة لتحديث حالة المستخدم في القائمة الجانبية
        function updateSidebarStatusUI(userId, isOnline, lastSeen) {
            const chatItem = document.querySelector(`.chat-item[data-user-id="${userId}"]`);
            if (chatItem) {
                const statusIndicator = chatItem.querySelector('.sidebar-status-indicator');
                if (statusIndicator) {
                    if (isOnline) {
                        statusIndicator.classList.remove('offline');
                        statusIndicator.classList.add('online');
                        statusIndicator.setAttribute('title', 'متصل الآن');
                    } else {
                        statusIndicator.classList.remove('online');
                        statusIndicator.classList.add('offline');

                        // إضافة معلومات آخر ظهور كتلميح عند تمرير المؤشر
                        if (lastSeen) {
                            statusIndicator.setAttribute('title', `آخر ظهور ${formatLastSeen(lastSeen)}`);
                        } else {
                            statusIndicator.setAttribute('title', 'غير متصل');
                        }
                    }
                } else {
                    // إذا لم يكن مؤشر الحالة موجودًا، قم بإنشائه
                    const chatAvatar = chatItem.querySelector('.chat-avatar');
                    if (chatAvatar) {
                        const newStatusIndicator = document.createElement('span');
                        newStatusIndicator.className = `sidebar-status-indicator ${isOnline ? 'online' : 'offline'}`;

                        // إضافة معلومات الحالة كتلميح عند تمرير المؤشر
                        if (isOnline) {
                            newStatusIndicator.setAttribute('title', 'متصل الآن');
                        } else if (lastSeen) {
                            newStatusIndicator.setAttribute('title', `آخر ظهور ${formatLastSeen(lastSeen)}`);
                        } else {
                            newStatusIndicator.setAttribute('title', 'غير متصل');
                        }

                        chatAvatar.appendChild(newStatusIndicator);
                    }
                }
            }
        }

        // دالة لتحديث حالة جميع المستخدمين في القائمة الجانبية
        function updateAllUsersStatus() {
            const chatItems = document.querySelectorAll('.chat-item');
            chatItems.forEach(item => {
                const userId = item.getAttribute('data-user-id');
                if (userId) {
                    fetch(`/api/get_user_status/${userId}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.status === 'success') {
                                updateSidebarStatusUI(userId, data.data.is_online, data.data.last_seen);
                            }
                        })
                        .catch(error => {
                            console.error('خطأ في الحصول على حالة المستخدم:', error);
                        });
                }
            });
        }

        // دالة لتحديث وقت آخر نشاط للمستخدم الحالي
        function updateUserActivity() {
            lastActivityTime = new Date();

            // إذا كان المستخدم غير نشط سابقًا، قم بتحديث حالته إلى نشط
            if (!userActivity) {
                userActivity = true;
                updateCurrentUserStatus(true);

                // تحديث واجهة المستخدم لإظهار أن المستخدم الحالي متصل
                updateCurrentUserStatusUI(true);
                console.log('تم تغيير حالة المستخدم إلى متصل بسبب عودة النشاط');
            }

            // إعادة ضبط مؤقت عدم النشاط
            clearTimeout(userActivityTimer);
            userActivityTimer = setTimeout(checkUserInactivity, inactivityTimeout);
        }

        // دالة للتحقق من عدم نشاط المستخدم
        function checkUserInactivity() {
            const now = new Date();
            const timeSinceLastActivity = now - lastActivityTime;

            // إذا كان المستخدم غير نشط لأكثر من 3 دقائق
            if (timeSinceLastActivity >= inactivityTimeout && userActivity) {
                userActivity = false;
                updateCurrentUserStatus(false);

                // تحديث واجهة المستخدم لإظهار أن المستخدم الحالي غير متصل
                updateCurrentUserStatusUI(false);
                console.log('تم تغيير حالة المستخدم إلى غير متصل بسبب عدم النشاط لمدة 3 دقائق');
            }
        }

        // دالة لتحديث حالة المستخدم الحالي في الخادم
        function updateCurrentUserStatus(isOnline) {
            fetch('/api/update_user_status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    is_online: isOnline
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('تم تحديث حالة المستخدم:', data);

                // تحديث واجهة المستخدم بعد التحديث الناجح
                if (data.status === 'success') {
                    updateCurrentUserStatusUI(data.data.is_online);
                }
            })
            .catch(error => {
                console.error('خطأ في تحديث حالة المستخدم:', error);
            });
        }

        // دالة لتحديث واجهة المستخدم بحالة المستخدم الحالي
        function updateCurrentUserStatusUI(isOnline) {
            // يمكن إضافة أي تحديثات إضافية لواجهة المستخدم هنا إذا لزم الأمر
            console.log('حالة المستخدم الحالي:', isOnline ? 'متصل' : 'غير متصل');
        }

        // إضافة مستمعي الأحداث لتتبع نشاط المستخدم
        // هذه الأحداث تعتبر نشاطًا للمستخدم وتؤدي إلى تحديث وقت آخر نشاط
        document.addEventListener('mousemove', updateUserActivity);  // تحريك الماوس
        document.addEventListener('mousedown', updateUserActivity);  // النقر بالماوس
        document.addEventListener('keypress', updateUserActivity);   // الضغط على لوحة المفاتيح
        document.addEventListener('scroll', updateUserActivity);     // التمرير
        document.addEventListener('touchstart', updateUserActivity); // اللمس (للأجهزة اللوحية والهواتف)

        // بدء تتبع نشاط المستخدم
        updateUserActivity();

        // دالة لتحديث حالة المستخدمين بشكل دوري
        function startStatusPolling() {
            // تحديث حالة جميع المستخدمين كل 10 ثوانٍ
            setInterval(updateAllUsersStatus, 10000);
        }

        // تحديث حالة جميع المستخدمين عند تحميل الصفحة
        updateAllUsersStatus();

        // بدء تحديث حالة المستخدمين بشكل دوري
        startStatusPolling();

        // إيقاف تتبع النشاط عند مغادرة الصفحة
        window.addEventListener('beforeunload', function() {
            // تحديث حالة المستخدم إلى غير متصل قبل مغادرة الصفحة
            updateCurrentUserStatus(false);
            clearTimeout(userActivityTimer);
        });

        // ===== نهاية كود تتبع حالة المستخدم =====

        // ===== بداية كود المجموعات حسب مكان العمل =====

        // دالة لعرض قائمة أماكن العمل
        function showWorkplaceGroups() {
            // إخفاء قائمة المحادثات وإظهار قائمة المجموعات
            document.querySelector('.chat-list').style.display = 'none';
            workplaceGroup.style.display = 'none';
            groupsContainer.style.display = 'flex';

            // تجميع المستخدمين حسب مكان العمل
            const workplaceGroups = groupUsersByWorkplace();

            // عرض قائمة أماكن العمل
            renderWorkplaceList(workplaceGroups);
        }

        // دالة لتجميع المستخدمين حسب مكان العمل
        function groupUsersByWorkplace() {
            const workplaceGroups = {};
            const chatItems = document.querySelectorAll('.chat-item');

            // طباعة معلومات تصحيح الأخطاء
            console.log('عدد عناصر المحادثة:', chatItems.length);

            chatItems.forEach(item => {
                const userId = item.getAttribute('data-user-id');
                let workplace = item.getAttribute('data-workplace');

                // طباعة معلومات تصحيح الأخطاء
                console.log('معرف المستخدم:', userId, 'مكان العمل:', workplace);

                // التأكد من أن مكان العمل ليس فارغاً
                if (!workplace || workplace.trim() === '') {
                    workplace = 'غير محدد';
                }

                const userName = item.querySelector('.chat-name').textContent;
                const profilePic = item.querySelector('.avatar-image').src;
                const isOnline = item.querySelector('.sidebar-status-indicator').classList.contains('online');

                // إنشاء مجموعة لمكان العمل إذا لم تكن موجودة
                if (!workplaceGroups[workplace]) {
                    workplaceGroups[workplace] = [];
                }

                // إضافة المستخدم إلى المجموعة
                workplaceGroups[workplace].push({
                    id: userId,
                    name: userName,
                    profilePic: profilePic,
                    isOnline: isOnline
                });
            });

            // طباعة معلومات تصحيح الأخطاء
            console.log('مجموعات مكان العمل:', Object.keys(workplaceGroups));

            return workplaceGroups;
        }

        // دالة لعرض قائمة أماكن العمل
        function renderWorkplaceList(workplaceGroups) {
            // مسح القائمة الحالية
            groupsList.innerHTML = '';

            // طباعة معلومات تصحيح الأخطاء
            console.log('عرض قائمة أماكن العمل:', workplaceGroups);

            // ترتيب أماكن العمل أبجديًا
            const sortedWorkplaces = Object.keys(workplaceGroups).sort((a, b) => {
                // وضع "غير محدد" في النهاية دائمًا
                if (a === 'غير محدد') return 1;
                if (b === 'غير محدد') return -1;
                return a.localeCompare(b, 'ar');
            });

            console.log('أماكن العمل المرتبة:', sortedWorkplaces);

            // إنشاء عنصر لكل مكان عمل
            sortedWorkplaces.forEach(workplace => {
                const users = workplaceGroups[workplace];

                console.log(`مكان العمل: ${workplace}, عدد المستخدمين: ${users.length}`);

                // تخطي أماكن العمل التي ليس لديها مستخدمين
                if (users.length === 0) {
                    console.log(`تخطي ${workplace} لأنه لا يوجد مستخدمين`);
                    return;
                }

                // إنشاء عنصر مكان العمل
                const workplaceItem = document.createElement('div');
                workplaceItem.className = 'workplace-item';
                workplaceItem.setAttribute('data-workplace', workplace);

                // تحديد الأيقونة المناسبة
                let icon = 'fa-building';
                if (workplace.includes('مدرسة') || workplace.includes('متوسطة')) {
                    icon = 'fa-school';
                } else if (workplace.includes('ثانوية')) {
                    icon = 'fa-graduation-cap';
                }

                // صيغة عدد الأساتذة
                let teachersText = '';
                if (users.length === 1) {
                    teachersText = 'أستاذ واحد';
                } else if (users.length === 2) {
                    teachersText = 'أستاذان';
                } else if (users.length >= 3 && users.length <= 10) {
                    teachersText = `${users.length} أساتذة`;
                } else {
                    teachersText = `${users.length} أستاذ`;
                }

                workplaceItem.innerHTML = `
                    <div class="workplace-icon">
                        <i class="fas ${icon}"></i>
                    </div>
                    <div class="workplace-info">
                        <h6 class="workplace-name">${workplace}</h6>
                        <p class="workplace-count">${teachersText}</p>
                    </div>
                `;

                // إضافة معالج النقر
                workplaceItem.addEventListener('click', function() {
                    showWorkplaceUsers(workplace, users);
                });

                groupsList.appendChild(workplaceItem);
                console.log(`تمت إضافة ${workplace} إلى القائمة`);
            });

            // إذا كانت القائمة فارغة، عرض رسالة
            if (groupsList.children.length === 0) {
                const emptyMessage = document.createElement('div');
                emptyMessage.className = 'empty-message';
                emptyMessage.textContent = 'لا توجد مجموعات متاحة';
                groupsList.appendChild(emptyMessage);
                console.log('تم عرض رسالة: لا توجد مجموعات متاحة');
            }
        }

        // دالة لعرض المستخدمين في مكان عمل محدد
        function showWorkplaceUsers(workplace, users) {
            // تحديث عنوان مكان العمل
            workplaceTitle.textContent = workplace;

            // مسح قائمة المستخدمين الحالية
            workplaceUsers.innerHTML = '';

            // ترتيب المستخدمين أبجديًا حسب الاسم
            const sortedUsers = [...users].sort((a, b) => a.name.localeCompare(b.name, 'ar'));

            // إضافة المستخدمين إلى القائمة
            sortedUsers.forEach(user => {
                const chatItem = document.createElement('div');
                chatItem.className = 'chat-item';
                chatItem.setAttribute('data-user-id', user.id);
                chatItem.setAttribute('data-workplace', workplace);

                // تحديد رسالة الحالة
                let statusMessage = 'ابدأ محادثة جديدة...';
                if (user.isOnline) {
                    statusMessage = 'متصل الآن';
                }

                chatItem.innerHTML = `
                    <div class="chat-avatar">
                        <img src="${user.profilePic}" alt="${user.name}" class="avatar-image">
                        <span class="sidebar-status-indicator ${user.isOnline ? 'online' : 'offline'}"></span>
                    </div>
                    <div class="chat-info">
                        <div class="chat-top">
                            <h6 class="chat-name">${user.name}</h6>
                        </div>
                        <div class="chat-bottom">
                            <p class="chat-message">${statusMessage}</p>
                        </div>
                    </div>
                `;

                // إضافة معالج النقر
                chatItem.addEventListener('click', handleWorkplaceUserClick);

                workplaceUsers.appendChild(chatItem);
            });

            // إذا كانت القائمة فارغة، عرض رسالة
            if (workplaceUsers.children.length === 0) {
                const emptyMessage = document.createElement('div');
                emptyMessage.className = 'empty-message';
                emptyMessage.textContent = 'لا يوجد أساتذة في هذا المكان';
                workplaceUsers.appendChild(emptyMessage);
            }

            // إخفاء قائمة المجموعات وإظهار قائمة المستخدمين
            groupsContainer.style.display = 'none';
            workplaceGroup.style.display = 'flex';
        }

        // دالة معالجة النقر على مستخدم في مكان عمل
        function handleWorkplaceUserClick() {
            // إخفاء قائمة المستخدمين وإظهار قائمة المحادثات
            workplaceGroup.style.display = 'none';
            document.querySelector('.chat-list').style.display = 'block';

            // الحصول على معرف المستخدم
            const userId = this.getAttribute('data-user-id');

            // تحميل رسائل المحادثة
            loadMessages(userId);

            // إظهار منطقة المحادثة وإخفاء رسالة "اختر محادثة"
            noChatSelected.style.display = 'none';

            // التأكد من إعادة تعيين تنسيقات بطاقة الآية
            const noChatContent = noChatSelected.querySelector('.no-chat-content');
            if (noChatContent) {
                // إعادة تعيين التنسيقات لمنع ظهور المساحة البيضاء لاحقاً
                noChatContent.style.position = '';
                noChatContent.style.transform = '';
                noChatContent.style.boxShadow = '';
                noChatContent.style.borderTop = '';
                noChatContent.style.padding = '';
                noChatContent.style.backgroundColor = '';
            }

            inputArea.style.display = 'flex';

            // في الشاشات الصغيرة، إخفاء الشريط الجانبي وإظهار منطقة المحادثة
            if (window.innerWidth <= 768) {
                sidebar.classList.add('hidden');
                // تحديث المتغير لتتبع أن المستخدم قد اختار محادثة في وضع الهاتف
                chatSelectedInMobileMode = true;
            }
        }

        // إضافة معالج النقر على زر المجموعات
        if (groupsBtn) {
            groupsBtn.addEventListener('click', function() {
                // إضافة الفئة النشطة للزر
                groupsBtn.classList.add('active');
                teachersBtn.classList.remove('active');

                // عرض قائمة المجموعات
                showWorkplaceGroups();
            });
        }

        // إضافة معالج النقر على زر الأساتذة
        if (teachersBtn) {
            teachersBtn.addEventListener('click', function() {
                // إضافة الفئة النشطة للزر
                teachersBtn.classList.add('active');
                groupsBtn.classList.remove('active');

                // إخفاء قائمة المجموعات وإظهار قائمة المحادثات
                groupsContainer.style.display = 'none';
                workplaceGroup.style.display = 'none';
                document.querySelector('.chat-list').style.display = 'block';

                // تفريغ حقل البحث وإظهار جميع العناصر في القائمة
                if (searchInput) {
                    searchInput.value = '';
                    // إظهار جميع عناصر المحادثة
                    document.querySelectorAll('.chat-item').forEach(item => {
                        item.style.display = 'flex';
                    });
                    // إخفاء زر المسح
                    if (clearSearchBtn) {
                        clearSearchBtn.classList.remove('visible');
                    }
                }

                // إزالة تحديد جميع العناصر في القائمة
                document.querySelectorAll('.chat-item').forEach(item => {
                    item.classList.remove('active');
                });

                // إعادة ضبط معرف المحادثة الحالية
                currentChatId = null;
            });
        }

        // تفعيل زر الأساتذة افتراضيًا
        teachersBtn.classList.add('active');

        // لم نعد بحاجة إلى معالج النقر على زر العودة إلى المحادثات
        // لأننا حذفنا العنصر backToChatsBtn

        // إضافة معالج النقر على زر العودة إلى قائمة المجموعات
        if (backToGroupsBtn) {
            backToGroupsBtn.addEventListener('click', function() {
                // إخفاء قائمة المستخدمين وإظهار قائمة المجموعات
                workplaceGroup.style.display = 'none';
                groupsContainer.style.display = 'flex';

                // تفريغ حقل البحث وإظهار جميع العناصر في القائمة
                if (searchInput) {
                    searchInput.value = '';
                    // إظهار جميع عناصر المجموعات
                    document.querySelectorAll('.workplace-item').forEach(item => {
                        item.style.display = 'flex';
                    });
                    // إخفاء زر المسح
                    if (clearSearchBtn) {
                        clearSearchBtn.classList.remove('visible');
                    }
                }
            });
        }

        // ===== نهاية كود المجموعات حسب مكان العمل =====

        // إضافة مستمع حدث لتحديث الصفحة
        window.addEventListener('beforeunload', function() {
            // إعادة ضبط حالة بطاقة الآية عند تحديث الصفحة
            localStorage.removeItem('quoteHidden');
        });
    });
</script>
{% endblock %}
