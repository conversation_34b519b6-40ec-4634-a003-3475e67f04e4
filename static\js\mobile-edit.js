/**
 * ملف JavaScript لتعديل سلوك أزرار التحرير في وضع الهاتف
 */

document.addEventListener('DOMContentLoaded', function() {
    // التحقق مما إذا كان الجهاز هاتفاً محمولاً (عرض الشاشة أقل من 768 بكسل)
    const isMobile = window.innerWidth < 768;

    if (isMobile) {
        // تعديل وظيفة toggleEdit لتعمل بشكل مختلف على الهواتف
        window.originalToggleEdit = window.toggleEdit;

        // تعريف وظيفة toggleEdit الجديدة
        window.toggleEdit = function(userId) {
            const row = document.getElementById(`row-${userId}`);

            // التأكد من أن الصف مرئي
            row.style.display = 'table-row';

            // إضافة فئة "editing" إلى الصف
            row.classList.add('editing');

            // إخفاء عناصر العرض فقط في خلية مكان العمل
            const workplaceCell = row.querySelector('td:nth-child(3)');
            const workplaceText = workplaceCell.querySelector('.view-mode');
            workplaceText.style.display = 'none';

            // إظهار حقل تحرير مكان العمل
            const workplaceInput = workplaceCell.querySelector('.edit-mode');
            workplaceInput.style.display = 'block';
            workplaceInput.classList.remove('d-none');

            // إخفاء باقي عناصر العرض
            row.querySelectorAll('.view-mode').forEach(function(el) {
                el.style.display = 'none';
                el.classList.add('d-none');
            });

            // إظهار باقي حقول التحرير
            row.querySelectorAll('.edit-mode:not(.btn-group)').forEach(function(el) {
                el.style.display = 'block';
                el.classList.remove('d-none');
            });

            // إضافة أزرار الحفظ والإلغاء المخصصة للهاتف
            const actionsCell = row.querySelector('td:last-child');

            // إنشاء زر الحفظ المخصص للهاتف إذا لم يكن موجوداً
            if (!actionsCell.querySelector('.mobile-save-btn')) {
                // إنشاء حاوية للأزرار
                const btnContainer = document.createElement('div');
                btnContainer.className = 'mobile-btn-container';
                btnContainer.style.display = 'flex';
                btnContainer.style.justifyContent = 'space-between';
                btnContainer.style.marginTop = '10px';
                btnContainer.style.width = '100%';

                // إنشاء زر الحفظ
                const saveBtn = document.createElement('button');
                saveBtn.className = 'btn btn-sm btn-success mobile-save-btn';
                saveBtn.innerHTML = '<i class="fas fa-check me-1"></i> حفظ';
                saveBtn.style.width = '48%';
                saveBtn.onclick = function() {
                    saveChanges(userId);
                };

                // إنشاء زر الإلغاء
                const cancelBtn = document.createElement('button');
                cancelBtn.className = 'btn btn-sm btn-secondary mobile-cancel-btn';
                cancelBtn.innerHTML = '<i class="fas fa-times me-1"></i> إلغاء';
                cancelBtn.style.width = '48%';
                cancelBtn.onclick = function() {
                    // استخدام وظيفة cancelEdit المخصصة للهاتف
                    mobileCancelEdit(userId);
                };

                // إضافة الأزرار إلى الحاوية
                btnContainer.appendChild(saveBtn);
                btnContainer.appendChild(cancelBtn);

                // إضافة الحاوية إلى الخلية
                actionsCell.appendChild(btnContainer);
            }
        };

        // إضافة وظيفة mobileCancelEdit للهواتف
        window.mobileCancelEdit = function(userId) {
            const row = document.getElementById(`row-${userId}`);

            // إزالة فئة "editing" من الصف
            row.classList.remove('editing');

            // إخفاء حقول التحرير
            row.querySelectorAll('.edit-mode').forEach(function(el) {
                el.style.display = 'none';
                el.classList.add('d-none');
            });

            // إظهار عناصر العرض
            row.querySelectorAll('.view-mode').forEach(function(el) {
                el.style.display = '';
                el.classList.remove('d-none');
            });

            // إزالة حاوية الأزرار المخصصة للهاتف
            const btnContainer = row.querySelector('.mobile-btn-container');
            if (btnContainer) {
                btnContainer.remove();
            }
        };

        // تعديل وظيفة saveChanges لتعمل بشكل مختلف على الهواتف
        window.originalSaveChanges = window.saveChanges;

        window.saveChanges = function(userId) {
            const formData = new FormData();
            const fields = {
                'teacher_name': document.getElementById(`edit-name-${userId}`).value.trim(),
                'workplace': document.getElementById(`edit-workplace-${userId}`).value.trim(),
                'email': document.getElementById(`edit-email-${userId}`).value.trim(),
                'phone_number': document.getElementById(`edit-phone-number-${userId}`).value.trim()
            };

            // التحقق من البيانات
            if (!fields.teacher_name || !fields.workplace || !fields.email || !fields.phone_number) {
                showMessage('جميع الحقول مطلوبة', 'danger');
                return;
            }

            // التحقق من تنسيق رقم الهاتف
            if (!/^\d{10}$/.test(fields.phone_number)) {
                showMessage('رقم الهاتف يجب أن يتكون من 10 أرقام', 'danger');
                return;
            }

            // التحقق من تنسيق البريد الإلكتروني
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(fields.email)) {
                showMessage('يرجى إدخال بريد إلكتروني صحيح', 'danger');
                return;
            }

            // الحصول على زر الحفظ المخصص للهاتف
            const row = document.getElementById(`row-${userId}`);
            const saveButton = row.querySelector('.mobile-save-btn');

            if (!saveButton) {
                showMessage('حدث خطأ أثناء محاولة الحفظ', 'danger');
                return;
            }

            const originalButtonText = saveButton.innerHTML;

            // تعطيل الزر
            saveButton.disabled = true;

            // إضافة رمز CSRF إلى البيانات
            const csrfToken = document.getElementById('csrf-token').value;
            formData.append('csrf_token', csrfToken);

            Object.keys(fields).forEach(function(key) {
                formData.append(key, fields[key]);
            });

            fetch(`/edit_user/${userId}`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': csrfToken
                },
                credentials: 'same-origin'
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.message || 'حدث خطأ أثناء حفظ التغييرات');
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.status === 'success') {
                    // تحديث البيانات في الصفحة
                    const nameElement = document.getElementById(`name-${userId}`);
                    nameElement.innerHTML = `<a href="/profile/${userId}" class="teacher-name-link">${fields.teacher_name}</a>`;
                    document.getElementById(`workplace-${userId}`).textContent = fields.workplace;
                    document.getElementById(`email-${userId}`).textContent = fields.email;
                    document.getElementById(`phone-${userId}`).textContent = fields.phone_number;

                    // إزالة فئة "editing" من الصف
                    row.classList.remove('editing');

                    // إخفاء نمط التعديل
                    row.querySelectorAll('.edit-mode').forEach(function(el) {
                        el.style.display = 'none';
                        el.classList.add('d-none');
                    });

                    // إظهار نمط العرض
                    row.querySelectorAll('.view-mode').forEach(function(el) {
                        el.style.display = '';
                        el.classList.remove('d-none');
                    });

                    // إزالة حاوية الأزرار المخصصة للهاتف
                    row.querySelector('.mobile-btn-container').remove();

                    // إظهار رسالة نجاح
                    showMessage('تم حفظ التغييرات بنجاح', 'success');

                    // التأكد من البقاء في قسم إدارة الأساتذة
                    window.showSection('teachers-section');
                } else {
                    throw new Error(data.message);
                }
            })
            .catch(error => {
                // إظهار رسالة الخطأ
                showMessage(error.message, 'danger');

                // إعادة الزر إلى حالته الأصلية
                saveButton.innerHTML = originalButtonText;
                saveButton.disabled = false;
            });
        };

        // إضافة تنسيقات CSS للأزرار المخصصة للهاتف وإصلاح مشكلة اختفاء الصف
        const style = document.createElement('style');
        style.textContent = `
            .mobile-save-btn, .mobile-cancel-btn {
                margin-top: 10px;
                width: 45%;
                padding: 6px 10px !important;
                font-size: 0.8rem !important;
            }

            @media (max-width: 576px) {
                .mobile-save-btn, .mobile-cancel-btn {
                    width: 48%;
                    padding: 5px 8px !important;
                    font-size: 0.75rem !important;
                }
            }

            /* إصلاح مشكلة اختفاء الصف عند التحرير */
            #teachers-section tr.editing {
                display: table-row !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            /* التأكد من ظهور جميع الخلايا في الصف */
            #teachers-section tr.editing td {
                display: table-cell !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            /* إخفاء نص مكان العمل فقط وليس الخلية بأكملها */
            #teachers-section tr.editing td:nth-child(3) .view-mode {
                display: none !important;
            }

            /* إظهار حقل تحرير مكان العمل */
            #teachers-section tr.editing td:nth-child(3) .edit-mode {
                display: block !important;
            }
        `;
        document.head.appendChild(style);
    }

    // إضافة مستمع لتغيير حجم النافذة
    window.addEventListener('resize', function() {
        const isMobile = window.innerWidth < 768;

        // إعادة تعريف الوظائف عند تغيير حجم النافذة
        if (isMobile && window.originalToggleEdit) {
            window.toggleEdit = window.mobileToggleEdit || window.toggleEdit;
        } else if (!isMobile && window.originalToggleEdit) {
            window.toggleEdit = window.originalToggleEdit;
            window.saveChanges = window.originalSaveChanges;
        }
    });
});
