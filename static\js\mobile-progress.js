/**
 * ملف JavaScript لتحسين عرض صفحة تقدم البرنامج على الهواتف المحمولة
 */

document.addEventListener('DOMContentLoaded', function() {
    // التحقق من أن الصفحة هي صفحة تقدم البرنامج
    if (window.location.pathname.includes('/progress')) {
        // التحقق من أن الشاشة صغيرة (هاتف محمول)
        if (window.innerWidth <= 576) {
            setupMobileProgressButtons();
        }

        // إعادة تهيئة الأزرار عند تغيير حجم الشاشة
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 576) {
                setupMobileProgressButtons();
            }
        });
    }
});

/**
 * إعداد أزرار صفحة تقدم البرنامج للهواتف المحمولة
 */
function setupMobileProgressButtons() {
    // التحقق من أن الأزرار لم يتم إضافتها بالفعل
    if (document.body.classList.contains('mobile-buttons-added')) {
        return;
    }

    // الحصول على الأزرار الأصلية
    const yearlyProgressBtn = document.getElementById('yearly-progress-btn');
    const addProgressBtn = document.getElementById('btn-add-progress');

    // التحقق من وجود الأزرار
    if (!yearlyProgressBtn || !addProgressBtn) {
        return;
    }

    // الحصول على حاوية البطاقة
    const cardBody = document.querySelector('.container-fluid.py-4 .col-md-8 .card-body');

    if (!cardBody) {
        return;
    }

    // إنشاء زر التدرج السنوي الجديد
    const newYearlyBtn = document.createElement('button');
    newYearlyBtn.className = 'mobile-progress-button yearly-btn';
    newYearlyBtn.innerHTML = '<i class="fas fa-calendar-alt me-1"></i> التدرج السنوي';

    // إنشاء زر إضافة حصة الجديد
    const newAddBtn = document.createElement('button');
    newAddBtn.className = 'mobile-progress-button add-btn';
    newAddBtn.innerHTML = '<i class="fas fa-plus me-1"></i> إضافة حصة';

    // إضافة الأزرار إلى حاوية البطاقة
    cardBody.appendChild(newYearlyBtn);
    cardBody.appendChild(newAddBtn);

    // ربط الأحداث بالأزرار الجديدة
    newYearlyBtn.addEventListener('click', function() {
        yearlyProgressBtn.click();
    });

    newAddBtn.addEventListener('click', function() {
        addProgressBtn.click();
    });

    // إضافة فئة للجسم للإشارة إلى أن الأزرار تمت إضافتها
    document.body.classList.add('mobile-buttons-added');

    // تعديل أزرار الإجراءات في الجدول لإخفاء النص وإظهار الأيقونات فقط
    modifyActionButtons();

    console.log('تم إعداد أزرار صفحة تقدم البرنامج للهواتف المحمولة');
}

/**
 * تعديل أزرار الإجراءات في جدول التقدم لإخفاء النص وإظهار الأيقونات فقط
 */
function modifyActionButtons() {
    // البحث عن أزرار التعديل والحذف في الجدول
    const editButtons = document.querySelectorAll('#progress-table .btn-edit-progress');
    const deleteButtons = document.querySelectorAll('#progress-table .btn-delete-progress');

    // تعديل أزرار التعديل
    editButtons.forEach(button => {
        // حفظ محتوى الزر الأصلي
        const originalHTML = button.innerHTML;

        // استخراج الأيقونة فقط
        if (originalHTML.includes('<i class="fas fa-edit"></i>')) {
            button.innerHTML = '<i class="fas fa-edit"></i>';
        }
    });

    // تعديل أزرار الحذف
    deleteButtons.forEach(button => {
        // حفظ محتوى الزر الأصلي
        const originalHTML = button.innerHTML;

        // استخراج الأيقونة فقط
        if (originalHTML.includes('<i class="fas fa-trash-alt"></i>')) {
            button.innerHTML = '<i class="fas fa-trash-alt"></i>';
        }
    });

    // إضافة مستمع للتغييرات في DOM لتعديل الأزرار الجديدة
    const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // البحث عن أزرار جديدة تمت إضافتها
                const newEditButtons = document.querySelectorAll('#progress-table .btn-edit-progress:not(.modified)');
                const newDeleteButtons = document.querySelectorAll('#progress-table .btn-delete-progress:not(.modified)');

                // تعديل أزرار التعديل الجديدة
                newEditButtons.forEach(button => {
                    if (button.innerHTML.includes('<i class="fas fa-edit"></i>')) {
                        button.innerHTML = '<i class="fas fa-edit"></i>';
                        button.classList.add('modified');
                    }
                });

                // تعديل أزرار الحذف الجديدة
                newDeleteButtons.forEach(button => {
                    if (button.innerHTML.includes('<i class="fas fa-trash-alt"></i>')) {
                        button.innerHTML = '<i class="fas fa-trash-alt"></i>';
                        button.classList.add('modified');
                    }
                });
            }
        });
    });

    // بدء مراقبة التغييرات في جدول التقدم
    const progressTable = document.getElementById('progress-table');
    if (progressTable) {
        observer.observe(progressTable, { childList: true, subtree: true });
    }
}
