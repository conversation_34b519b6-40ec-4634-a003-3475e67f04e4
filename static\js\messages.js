// Add event listeners for delete buttons
document.addEventListener('DOMContentLoaded', function() {
    // Use event delegation to handle delete buttons
    document.body.addEventListener('click', function(event) {
        const deleteButton = event.target.closest('.delete-message-btn');
        if (deleteButton) {
            event.preventDefault();
            event.stopPropagation();
            const messageId = deleteButton.getAttribute('data-message-id');
            handleDeleteMessage(messageId);
        }
    });
});

function handleDeleteMessage(messageId) {
    // تم إزالة رسالة التأكيد وحذف الرسالة مباشرة
    deleteMessage(messageId);
}

function deleteMessage(messageId) {
    // تم إزالة رسالة "جاري الحذف"

    const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    if (!token) {
        showAlert('خطأ في الأمان: لم يتم العثور على رمز CSRF', 'error');
        return;
    }

    fetch(`/delete_message/${messageId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': token
        },
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            if (response.status === 403) {
                return response.json().then(data => {
                    throw new Error(data.message || 'غير مصرح لك بحذف هذه الرسالة');
                });
            }
            throw new Error(`خطأ في الخادم: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.status === 'success') {
            const messageElement = document.querySelector(`.message-item[data-message-id="${messageId}"]`);
            if (messageElement) {
                messageElement.remove();
            }
        } else {
            showAlert(data.message || 'حدث خطأ أثناء حذف الرسالة', 'error');
        }
    })
    .catch(error => {
        console.error('Error deleting message:', error);
        showAlert(error.message || 'حدث خطأ أثناء حذف الرسالة', 'error');
    });
}

// Helper function to get CSRF token from meta tag
function getCsrfToken() {
    const tokenElement = document.querySelector('meta[name="csrf-token"]');
    if (!tokenElement) {
        console.error('CSRF token meta tag not found');
        return null;
    }
    const token = tokenElement.getAttribute('content');
    if (!token) {
        console.error('CSRF token is empty');
        return null;
    }
    return token;
}

// Helper function to show alerts
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.style.position = 'fixed';
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '1050';
    alertDiv.style.padding = '8px 16px';
    alertDiv.style.borderRadius = '4px';
    alertDiv.style.fontSize = '14px';
    alertDiv.style.maxWidth = '250px';
    alertDiv.style.textAlign = 'center';
    alertDiv.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
    alertDiv.innerHTML = message;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        alertDiv.style.animation = 'fadeOut 0.5s ease-in-out forwards';
        setTimeout(() => alertDiv.remove(), 500);
    }, 2500);
}

// تحديث اسم الملف عند اختياره
function updateFileName(input) {
    const fileName = input.files[0] ? input.files[0].name : '';
    const fileNameElement = document.getElementById('file-name');
    if (fileNameElement) {
        if (fileName) {
            fileNameElement.innerHTML = `
                <div class="d-flex align-items-center">
                    <span class="text-success">الملف المرفق: ${fileName}</span>
                    <button type="button" class="btn btn-link text-danger p-0 ms-2"
                            onclick="removeFile('${input.id}')"
                            style="font-size: 18px;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>`;
            fileNameElement.classList.add('text-success');
        } else {
            fileNameElement.innerHTML = '';
            fileNameElement.classList.remove('text-success');
        }
    }
}

// دالة لإلغاء الملف المرفق
function removeFile(inputId) {
    const input = document.getElementById(inputId);
    if (input) {
        input.value = '';
        updateFileName(input);
    }
}

// دالة لتحويل الروابط والبريد الإلكتروني في نص الرسائل إلى روابط قابلة للنقر
function convertLinksInMessages() {
    // الحصول على جميع فقرات محتوى الرسائل
    const messageParagraphs = document.querySelectorAll('.message-content p');

    // تعبير منتظم للبحث عن الروابط
    const urlRegex = /(https?:\/\/[^\s]+)|(www\.[^\s]+)|([a-zA-Z0-9][-a-zA-Z0-9]*\.[a-zA-Z0-9][-a-zA-Z0-9]*\.[a-zA-Z]{2,}[^\s]*)|([a-zA-Z0-9][-a-zA-Z0-9]*\.[a-zA-Z]{2,}[^\s]*)/g;

    // تعبير منتظم للبحث عن البريد الإلكتروني
    const emailRegex = /([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9._-]+)/g;

    // معالجة كل فقرة
    messageParagraphs.forEach(paragraph => {
        let html = paragraph.innerHTML;

        // تجنب تحويل الروابط التي تم تحويلها بالفعل
        if (html.indexOf('<a href') !== -1) return;

        // تحويل الروابط
        html = html.replace(urlRegex, function(url) {
            let href = url;
            if (url.startsWith('www.')) {
                href = 'http://' + url;
            } else if (!url.startsWith('http://') && !url.startsWith('https://') && url.includes('.')) {
                // إذا كان الرابط لا يبدأ بـ http:// أو https:// ويحتوي على نقطة (مثل google.com)
                href = 'http://' + url;
            }
            return `<a href="${href}" target="_blank" class="text-primary" style="text-decoration: underline; word-break: break-all; display: inline-block;">${url}</a>`;
        });

        // تحويل البريد الإلكتروني
        html = html.replace(emailRegex, function(email) {
            return `<a href="mailto:${email}" class="text-primary" style="text-decoration: underline; word-break: break-all; display: inline-block;">${email}</a>`;
        });

        // تحديث محتوى الفقرة
        paragraph.innerHTML = html;
    });
}