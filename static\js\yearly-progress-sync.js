/**
 * وظائف التزامن والتحديث لجدول التدرجات السنوية
 */

// وظيفة لتحميل بيانات التدرجات السنوية المحدثة من الخادم
function loadYearlyProgressData(year) {
    // إذا لم يتم تمرير السنة، نستخدم السنة المحددة حالياً
    if (!year) {
        year = parseInt(document.getElementById('year-selector').value);
    }

    // تغيير حالة واجهة المستخدم لإظهار أنه يتم تحميل البيانات
    const table = document.getElementById('yearly-progress-table');
    if (!table) return;
    
    // إضافة مؤشر التحميل
    table.innerHTML = '<tr><td colspan="5" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>جاري تحميل البيانات...</td></tr>';
    
    // استخدام API للحصول على بيانات محدثة من الخادم
    fetch(`/get-yearly-progress/${year}`)
        .then(response => response.json())
        .then(response => {
            if (response.status === 'success') {
                const progressItems = response.data;
                
                // حذف جميع الصفوف الحالية
                table.innerHTML = '';
                
                // ترتيب الأشهر
                const monthOrder = [
                    'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر',
                    'جانفي', 'فيفري', 'مارس', 'أفريل', 'ماي'
                ];
                
                // التحقق من وجود بيانات
                if (progressItems && progressItems.length > 0) {
                    // ترتيب البيانات حسب الشهر ثم رقم الأسبوع
                    progressItems.sort((a, b) => {
                        const monthIndexA = monthOrder.indexOf(a.month);
                        const monthIndexB = monthOrder.indexOf(b.month);
                        
                        if (monthIndexA !== monthIndexB) {
                            return monthIndexA - monthIndexB;
                        }
                        
                        return a.week - b.week;
                    });
                    
                    // حذف جميع الصفوف الحالية
                    const rows = table.querySelectorAll('tr:not(.add-row-container)');
                    rows.forEach(row => {
                        row.remove();
                    });
                    
                    // إضافة الصفوف بالبيانات المحفوظة بنفس التنسيق السابق
                    progressItems.forEach((item) => {
                        // تحديد اللون بناءً على الشهر
                        let color = 'rgba(255, 193, 7, 0.1)'; // لون افتراضي
                        let bgColor = 'rgba(255, 193, 7, 0.3)';
                        
                        // تحديد اللون حسب الشهر
                        switch (item.month) {
                            case 'سبتمبر':
                                color = 'rgba(255, 193, 7, 0.1)';
                                bgColor = 'rgba(255, 193, 7, 0.3)';
                                break;
                            case 'أكتوبر':
                                color = 'rgba(40, 167, 69, 0.1)';
                                bgColor = 'rgba(40, 167, 69, 0.3)';
                                break;
                            case 'نوفمبر':
                                color = 'rgba(23, 162, 184, 0.1)';
                                bgColor = 'rgba(23, 162, 184, 0.3)';
                                break;
                            case 'ديسمبر':
                                color = 'rgba(220, 53, 69, 0.1)';
                                bgColor = 'rgba(220, 53, 69, 0.3)';
                                break;
                            case 'جانفي':
                                color = 'rgba(111, 66, 193, 0.1)';
                                bgColor = 'rgba(111, 66, 193, 0.3)';
                                break;
                            case 'فيفري':
                                color = 'rgba(32, 201, 151, 0.1)';
                                bgColor = 'rgba(32, 201, 151, 0.3)';
                                break;
                            case 'مارس':
                                color = 'rgba(253, 126, 20, 0.1)';
                                bgColor = 'rgba(253, 126, 20, 0.3)';
                                break;
                            case 'أفريل':
                                color = 'rgba(13, 202, 240, 0.1)';
                                bgColor = 'rgba(13, 202, 240, 0.3)';
                                break;
                            case 'ماي':
                                color = 'rgba(102, 16, 242, 0.1)';
                                bgColor = 'rgba(102, 16, 242, 0.3)';
                                break;
                        }
                        
                        // إنشاء صف جديد - نضع رقم الأسبوع من البيانات مؤقتاً، ثم نعيد ترقيمه لاحقاً
                        const row = document.createElement('tr');
                        row.setAttribute('data-month', item.month);
                        row.setAttribute('data-week', item.week.toString());
                        row.style.backgroundColor = color;
                        
                        // إضافة محتوى الصف (نفس التنسيق السابق)
                        row.innerHTML = `
                            <td class="week-number">${item.week}</td>
                            <td contenteditable="true"></td>
                            <td contenteditable="true"></td>
                            <td>
                                <button type="button" class="btn btn-sm btn-outline-primary merge-btn" onclick="mergeCells(this)">
                                    <i class="fas fa-link"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger delete-row-btn" onclick="deleteRow(this)">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success add-row-btn" onclick="addRow(this)" data-month="${item.month}" data-color="${color}" data-bg-color="${bgColor}">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </td>
                        `;
                        
                        // إضافة الصف إلى الجدول
                        table.appendChild(row);
                        
                        // الحصول على خلايا الحصة في الصف
                        const lessonCells = Array.from(row.cells).filter(cell => cell.hasAttribute('contenteditable'));
                        
                        if (lessonCells.length >= 2) {
                            if (item.isMerged) {
                                // إذا كانت الخلايا مدمجة، استخدم الحصة الأولى فقط
                                lessonCells[0].setAttribute('colspan', '2');
                                lessonCells[0].textContent = item.firstLesson;
                                lessonCells[1].style.display = 'none';
                                
                                // تحديث زر الدمج
                                const mergeBtn = row.querySelector('.merge-btn');
                                if (mergeBtn) {
                                    mergeBtn.innerHTML = '<i class="fas fa-unlink"></i>';
                                    mergeBtn.onclick = function() { unMergeCells(this); };
                                    mergeBtn.title = 'فصل الخلايا';
                                }
                            } else {
                                // إذا لم تكن الخلايا مدمجة، استخدم كلتا الخليتين
                                lessonCells[0].textContent = item.firstLesson;
                                lessonCells[1].textContent = item.secondLesson;
                            }
                        }
                    });
                    
                    // تحديث هيكل الجدول بعد تحميل البيانات
                    updateMonthRowspans();
                    
                    // إعادة ترقيم الأسابيع بشكل متسلسل
                    renumberWeeks();
                } else {
                    // إعادة تعيين الجدول لوضعه الافتراضي إذا لم تكن هناك بيانات للسنة المحددة
                    resetTableToDefault();
                }
            } else {
                // إذا كان هناك خطأ، نظهر رسالة الخطأ
                table.innerHTML = `<tr><td colspan="5" class="text-center text-danger"><i class="fas fa-exclamation-triangle me-2"></i>${response.message || 'حدث خطأ أثناء تحميل البيانات'}</td></tr>`;
            }
        })
        .catch(error => {
            console.error('Error loading yearly progress:', error);
            table.innerHTML = '<tr><td colspan="5" class="text-center text-danger"><i class="fas fa-exclamation-triangle me-2"></i>حدث خطأ أثناء الاتصال بالخادم</td></tr>';
        });
}

// وظيفة حفظ التدرجات السنوية المحسنة
function saveYearlyProgress() {
    // الحصول على السنة المحددة من القائمة المنسدلة
    const selectedYear = parseInt(document.getElementById('year-selector').value);
    
    // إعادة ترقيم الأسابيع للتأكد من أنها متسلسلة قبل الحفظ
    renumberWeeks();
    
    // تغيير حالة الزر لإظهار أن الحفظ قيد التقدم
    const saveButton = document.getElementById('save-progress-btn');
    const originalButtonText = saveButton.innerHTML;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري الحفظ...';
    saveButton.disabled = true;
    
    // الحصول على بيانات الجدول
    const table = document.getElementById('yearly-progress-table');
    const rows = table.querySelectorAll('tr:not(.add-row-container)');
    
    // التحقق من وجود صفوف في الجدول
    if (rows.length === 0) {
        // إذا لم تكن هناك صفوف، لا داعي للمتابعة
        saveButton.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i> لا توجد بيانات للحفظ';
        setTimeout(() => {
            saveButton.innerHTML = originalButtonText;
            saveButton.disabled = false;
        }, 2000);
        return;
    }
    
    const progressData = [];
    
    // حفظ معلومات هيكل الجدول
    const tableStructure = {
        deletedWeeks: [],
        maxWeek: 0
    };
    
    // تحديد أقصى رقم أسبوع
    rows.forEach(row => {
        const weekNumber = parseInt(row.getAttribute('data-week'));
        if (weekNumber > tableStructure.maxWeek) {
            tableStructure.maxWeek = weekNumber;
        }
    });
    
    // تحديد الأسابيع المحذوفة (الأرقام المفقودة)
    for (let i = 1; i <= tableStructure.maxWeek; i++) {
        const weekExists = Array.from(rows).some(row => parseInt(row.getAttribute('data-week')) === i);
        if (!weekExists) {
            tableStructure.deletedWeeks.push(i);
        }
    }
    
    // معالجة كل صف في الجدول
    rows.forEach(row => {
        // استرجاع البيانات من الصف
        const weekCell = row.querySelector('.week-number');
        const month = row.getAttribute('data-month');
        const weekNumber = parseInt(weekCell.textContent); // استخدام النص المعروض لرقم الأسبوع
        const contentCells = row.querySelectorAll('[contenteditable="true"]');
        
        if (contentCells.length === 0) return; // تخطي الصفوف التي لا تحتوي على خلايا قابلة للتحرير
        
        // التحقق مما إذا كانت الخلايا مدمجة
        const isMerged = contentCells[0].hasAttribute('colspan');
        
        let firstLesson = contentCells[0].textContent.trim();
        let secondLesson = isMerged ? "" : (contentCells.length > 1 ? contentCells[1].textContent.trim() : "");
        
        // إضافة البيانات إلى المصفوفة
        progressData.push({
            week: weekNumber,
            month: month,
            firstLesson: firstLesson,
            secondLesson: secondLesson,
            isMerged: isMerged
        });
    });
    
    // تأكد من ترتيب البيانات حسب رقم الأسبوع
    progressData.sort((a, b) => a.week - b.week);
    
    // التحقق من وجود رمز CSRF في الصفحة
    const csrfToken = document.querySelector('input[name="csrf_token"]').value;
    
    // إرسال البيانات إلى الخادم باستخدام طلب Fetch API
    fetch('/save-yearly-progress', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            year: selectedYear, // استخدام السنة المحددة
            data: progressData,
            tableStructure: tableStructure // إضافة معلومات هيكل الجدول
        })
    })
    .then(response => response.json())
    .then(data => {
        // إعادة الزر إلى حالته الأصلية بعد نجاح الحفظ
        saveButton.innerHTML = '<i class="fas fa-check me-1"></i> تم الحفظ';
        
        // إعادة تحميل البيانات بعد الحفظ للتأكد من التزامن
        if (data.status === 'success') {
            // تخزين السنة الحالية
            const currentYear = selectedYear;
            
            // إعادة تحميل البيانات مباشرة بعد الحفظ للتأكد من التزامن الفوري
            loadYearlyProgressData(currentYear);
        }
        
        // تغيير الزر مرة أخرى بعد ثانيتين
        setTimeout(() => {
            saveButton.innerHTML = originalButtonText;
            saveButton.disabled = false;
        }, 2000);
    })
    .catch(error => {
        console.error('Error saving yearly progress:', error);
        saveButton.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i> فشل الحفظ';
        
        // تغيير الزر مرة أخرى بعد ثانيتين
        setTimeout(() => {
            saveButton.innerHTML = originalButtonText;
            saveButton.disabled = false;
        }, 2000);
    });
}

// عند تحميل المستند، نضيف مستمعي الأحداث
document.addEventListener('DOMContentLoaded', function() {
    // إضافة مستمع حدث لزر الحفظ
    const saveButton = document.getElementById('save-progress-btn');
    if (saveButton) {
        saveButton.addEventListener('click', saveYearlyProgress);
    }
    
    // إضافة مستمع حدث لتغيير السنة الدراسية
    const yearSelector = document.getElementById('year-selector');
    if (yearSelector) {
        yearSelector.addEventListener('change', function() {
            const selectedYear = parseInt(this.value);
            loadYearlyProgressData(selectedYear);
        });
    }
}); 