{% extends "base.html" %}
{% block head %}
<link href="{{ url_for('static', filename='background.css') }}" rel="stylesheet">
<!-- إضافة ملف CSS للهواتف المحمولة -->
<link href="{{ url_for('static', filename='css/auth-mobile.css') }}" rel="stylesheet">
<script>
    // تحسين سرعة تحميل الصفحة بإضافة الفئة مباشرة
    document.addEventListener('DOMContentLoaded', function() {
        document.body.classList.add('auth-page');
    });
</script>
{% endblock %}
{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h3 class="text-center text-white mb-0">الجمهورية الجزائرية الديمقراطية الشعبية</h3>
            <h4 class="text-center text-white mb-0">وزارة التربية الوطنية</h4>
        </div>

        <div class="login-body">
            <div class="welcome-text-container">
                <div class="welcome-text">
                    <span class="animated-word">حساب</span>
                    <span class="animated-word">جديد</span>
                    <span class="animated-word">في</span>
                    <span class="animated-word">فضاء</span>
                    <span class="animated-word">أساتذة</span>
                    <span class="animated-word">العلوم</span>
                    <span class="animated-word">الفيزيائية</span>
                </div>
            </div>

            <form method="POST" action="{{ url_for('register') }}">
                <!-- إضافة رمز CSRF للحماية من هجمات التزوير -->
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="mb-3">
                    <input type="text" class="form-control text-right" id="teacher_name" name="teacher_name"
                           placeholder="اسم الأستاذ(ة)" value="{{ teacher_name or '' }}" required
                           oninvalid="this.setCustomValidity('يرجى إدخال اسم الأستاذ')"
                           oninput="this.setCustomValidity('')">
                </div>

                <div class="mb-3">
                    <input type="email" class="form-control text-right" id="email" name="email"
                           placeholder="البريد الإلكتروني" value="{{ email or '' }}" required
                           oninvalid="this.setCustomValidity('يرجى إدخال بريد إلكتروني صحيح')"
                           oninput="this.setCustomValidity('')">
                </div>

                <div class="mb-3">
                    <div class="workplace-container position-relative">
                        <input type="text" class="form-control text-right" id="workplace" name="workplace"
                               placeholder="مكان العمل" value="{{ workplace or '' }}" required
                               oninvalid="this.setCustomValidity('يرجى إدخال مكان العمل')"
                               oninput="this.setCustomValidity('')" autocomplete="off">
                        <div id="workplace-dropdown" class="workplace-dropdown d-none">
                            <div class="workplace-dropdown-items" id="workplace-results">
                                <!-- ستتم إضافة الخيارات هنا بواسطة JavaScript -->
                            </div>
                            <div class="workplace-add-new d-none" id="workplace-add-new">
                                <button type="button" class="btn btn-add-workplace">
                                    <i class="fas fa-plus-circle"></i>
                                    <span>إضافة مكان عمل جديد</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="phone-input-container">
                        <input type="text" class="form-control text-right phone-number" id="phone_number" name="phone_number"
                               placeholder="رقم الهاتف" value="{{ phone_number or '' }}"
                               pattern="[0-9]{8}" maxlength="8" required
                               oninvalid="this.setCustomValidity('يرجى إدخال 8 أرقام')"
                               oninput="this.setCustomValidity('')">
                        <select class="form-control phone-prefix" name="phone_prefix" required>
                            <option value="05" {% if phone_prefix == '05' %}selected{% endif %}>05</option>
                            <option value="06" {% if phone_prefix == '06' %}selected{% endif %}>06</option>
                            <option value="07" {% if phone_prefix == '07' %}selected{% endif %}>07</option>
                        </select>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="password-field-container">
                        <input type="password" class="form-control text-right password-input" id="password" name="password"
                               placeholder="كلمة المرور" required
                               oninvalid="this.setCustomValidity('يرجى إدخال كلمة المرور')"
                               oninput="this.setCustomValidity('')">
                        <button type="button" id="toggle-password" class="toggle-password" aria-label="عرض/إخفاء كلمة المرور">
                            <i class="password-icon fa fa-eye-slash"></i>
                        </button>
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center mt-3">
                    <a href="{{ url_for('login') }}" class="register-link">لديك حساب بالفعل؟ <span class="login-here">تسجيل الدخول</span></a>
                    <button type="submit" class="btn btn-login">تسجيل</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* إخفاء شريط التنقل */
.navbar {
    display: none !important;
}

body {
    background-color: #f5f5f5;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 15px;
}

.login-card {
    width: 100%;
    max-width: 400px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    background-color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.5);
    opacity: 0;
    transform: translateY(-20px);
    animation: cardDropIn 0.5s ease-out forwards;
    will-change: opacity, transform; /* تحسين أداء الرسوم المتحركة */
}

/* تعديلات للشاشات الصغيرة */
@media (max-width: 576px) {
    .login-container {
        padding: 10px;
    }

    .login-card {
        max-width: 100%;
    }

    .login-body {
        padding: 15px 20px;
    }

    .welcome-text {
        font-size: 20px;
    }

    .animated-word {
        margin: 0 2px;
    }

    .login-header h3 {
        font-size: 18px;
    }

    .login-header h4 {
        font-size: 16px;
    }

    .phone-input-container {
        flex-direction: column;
        gap: 5px;
    }

    .phone-prefix {
        width: 100%;
    }
}

/* تبسيط الرسوم المتحركة لتحسين الأداء */
@keyframes cardDropIn {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    background: #1e88e5;
    padding: 15px 20px;
    text-align: center;
}

.login-body {
    padding: 15px 25px;
}

.welcome-text-container {
    text-align: center;
    margin-bottom: 5px;
}

.form-control {
    border: 1px solid #ddd;
    padding: 10px 15px;
    margin-bottom: 10px;
    border-radius: 5px;
    text-align: right;
}

.form-control:focus {
    border-color: #1976d2;
    box-shadow: 0 0 0 0.2rem rgba(25, 118, 210, 0.25);
}

.btn-login {
    background-color: #1e88e5;
    color: white;
    border: none;
    padding: 8px 30px;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.3s;
}

.btn-login:hover {
    background-color: #1565c0;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.register-link {
    color: #000000;
    text-decoration: none;
    font-size: 14px;
}

.login-here {
    color: #1976d2;
}

.text-right {
    text-align: right;
}

.login-header h3 {
    font-size: 18px;
    margin-bottom: 5px;
    white-space: nowrap;
}

.login-header h4 {
    font-size: 16px;
    padding-top: 2px;
}

.form-control::placeholder {
    opacity: 0.7;
}

.phone-input-container {
    display: flex;
    flex-direction: row;
    gap: 8px;
    margin-bottom: 10px;
}

.phone-number {
    flex: 3;
    text-align: right;
    direction: rtl;
}

.phone-prefix {
    flex: 1;
    text-align: center;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: left 0.75rem center;
    background-size: 16px 12px;
}

/* أنماط مشتركة لضمان التطابق التام */
.phone-number, .phone-prefix {
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 0;
    font-size: 1rem;
    height: auto;
    line-height: 1.5;
}

.form-select {
    background-position: left 0.75rem center;
}

.form-select:focus {
    border-color: #1976d2;
    box-shadow: 0 0 0 0.2rem rgba(25, 118, 210, 0.25);
}

/* Modificación de la bienvenida */
.welcome-text {
    font-size: 22px;
    font-weight: 800;
    margin-bottom: 5px;
    border-bottom: none;
    padding-bottom: 0;
    letter-spacing: 0.5px;
    font-family: Arial, sans-serif;
    display: flex;
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
}

.animated-word {
    display: inline-block;
    color: transparent;
    background: linear-gradient(to right, #1565c0, #0d47a1, #1976d2);
    -webkit-background-clip: text;
    background-clip: text;
    text-shadow: 0 0 3px rgba(13, 71, 161, 0.4);
    opacity: 0;
    white-space: nowrap;
    margin: 0 3px;
    position: relative;
    will-change: opacity, transform; /* تحسين أداء الرسوم المتحركة */
}

.animated-word:nth-child(1) {
    animation: wordAnimation 3s ease-in-out 0s infinite;
}
.animated-word:nth-child(2) {
    animation: wordAnimation 3s ease-in-out 0.1s infinite;
}
.animated-word:nth-child(3) {
    animation: wordAnimation 3s ease-in-out 0.2s infinite;
}
.animated-word:nth-child(4) {
    animation: wordAnimation 3s ease-in-out 0.3s infinite;
}
.animated-word:nth-child(5) {
    animation: wordAnimation 3s ease-in-out 0.4s infinite;
}
.animated-word:nth-child(6) {
    animation: wordAnimation 3s ease-in-out 0.5s infinite;
}
.animated-word:nth-child(7) {
    animation: wordAnimation 3s ease-in-out 0.6s infinite;
}

/* تبسيط الرسوم المتحركة للكلمات لتحسين الأداء */
@keyframes wordAnimation {
    0%, 100% {
        opacity: 0;
        transform: translateY(5px);
        filter: blur(3px);
    }
    15%, 50% {
        opacity: 1;
        transform: translateY(0);
        filter: blur(0);
    }
    65% {
        opacity: 0;
        transform: translateY(-5px);
        filter: blur(3px);
    }
}

.password-field-container {
    position: relative;
    display: flex;
    align-items: center;
}

.toggle-password {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    color: #777;
    font-size: 16px;
    transition: color 0.3s;
    z-index: 5;
}

.toggle-password:hover {
    color: #1976d2;
}

.toggle-password:focus {
    outline: none;
}

.password-input {
    margin-bottom: 0;
    padding-left: 40px; /* لإفساح المجال لرمز العين */
}

/* تنسيقات حقل مكان العمل والقائمة المنسدلة */
.workplace-container {
    position: relative;
}

.workplace-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    left: 0;
    z-index: 1000;
    background-color: white;
    border-radius: 0 0 5px 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    max-height: 300px;
    overflow-y: auto;
    margin-top: 2px;
    border: 1px solid #ddd;
    transition: all 0.3s ease;
}

.workplace-dropdown-items {
    max-height: 250px;
    overflow-y: auto;
}

.workplace-dropdown-item {
    padding: 10px 15px;
    cursor: pointer;
    transition: all 0.2s;
    text-align: right;
    border-bottom: 1px solid #f5f5f5;
}

.workplace-dropdown-item:hover {
    background-color: #f0f7ff;
}

.workplace-dropdown-item.active {
    background-color: #e3f2fd;
}

.workplace-add-new {
    padding: 10px;
    text-align: center;
    border-top: 1px solid #eee;
    background-color: #f5f9ff;
}

.btn-add-workplace {
    color: #1976d2;
    background: none;
    border: none;
    width: 100%;
    padding: 8px;
    font-size: 14px;
    border-radius: 4px;
    transition: all 0.3s;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-add-workplace i {
    font-size: 16px;
    margin-left: 8px;
    color: #4caf50;
    transition: transform 0.3s ease;
}

.btn-add-workplace:hover {
    background-color: #e3f2fd;
    color: #0d47a1;
}

.btn-add-workplace:hover i {
    transform: scale(1.2) rotate(90deg);
}

.workplace-highlight {
    font-weight: bold;
    color: #1976d2;
}

.workplace-item-location {
    color: #777;
    font-size: 0.85em;
    margin-right: 5px;
}

.workplace-container {
    position: relative;
}

.workplace-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    left: 0;
    z-index: 1000;
    background-color: white;
    border-radius: 0 0 5px 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    max-height: 300px;
    overflow-y: auto;
    margin-top: 2px;
    border: 1px solid #ddd;
    transition: all 0.3s ease;
}

.btn-add-workplace-inline {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #1976d2;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    z-index: 5;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    background-color: #f0f7ff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn-add-workplace-inline i {
    color: #4caf50;
    transition: transform 0.3s ease;
}

.btn-add-workplace-inline:hover {
    background-color: #e3f2fd;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-50%) scale(1.1);
}

.btn-add-workplace-inline:hover i {
    transform: rotate(90deg);
    color: #2e7d32;
}

.btn-add-workplace-inline:active {
    transform: translateY(-50%) scale(0.95);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.workplace-dropdown-items {
    max-height: 250px;
    overflow-y: auto;
}

/* تعديلات إضافية للشاشات الصغيرة جدًا */
@media (max-width: 380px) {
    .d-flex.justify-content-between.align-items-center {
        flex-direction: column;
        gap: 15px;
    }

    .register-link {
        order: 2;
        margin-top: 10px;
    }

    .btn-login {
        order: 1;
        width: 100%;
    }

    .form-control {
        padding: 10px;
    }

    .workplace-dropdown {
        max-height: 200px;
    }

    .workplace-dropdown-items {
        max-height: 150px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const emailInput = document.getElementById('email');
    const phoneInput = document.getElementById('phone_number');
    const passwordInput = document.getElementById('password');
    const togglePasswordButton = document.getElementById('toggle-password');
    const passwordIcon = document.querySelector('.password-icon');
    const workplaceInput = document.getElementById('workplace');
    const workplaceDropdown = document.getElementById('workplace-dropdown');
    const workplaceResults = document.getElementById('workplace-results');
    const workplaceAddNew = document.getElementById('workplace-add-new');

    console.log('تهيئة عناصر الصفحة:');
    console.log('- حقل مكان العمل:', workplaceInput ? 'موجود' : 'غير موجود');
    console.log('- قائمة أماكن العمل المنسدلة:', workplaceDropdown ? 'موجودة' : 'غير موجودة');
    console.log('- نتائج البحث في أماكن العمل:', workplaceResults ? 'موجودة' : 'غير موجودة');
    console.log('- زر إضافة مكان عمل جديد:', workplaceAddNew ? 'موجود' : 'غير موجود');

    // قائمة أماكن العمل
    const workplaces = [
        "أول نوفمبر 54 - المسيلة",
        "مي زيادة - المسيلة",
        "العقيد الحواس - المسيلة",
        "الحسن بن الهيثم - المسيلة",
        "زين الدين بن معطي - المسيلة",
        "أحمد شوقي - المسيلة",
        "أبو الخير الإشبيلي الشجار - المسيلة",
        "عيسو صالح - المسيلة",
        "مداقين محمد بن خيراني - المسيلة",
        "لعلا محمد - شلال",
        "والي بن صوشة - أولاد ماضي",
        "علي بن أبي طالب - سيدي عيسى",
        "المهدي بن بركة - سيدي عيسى",
        "عكريمي بن خضرة - سيدي عيسى",
        "ربيعي حمود - سيدي عيسى",
        "عباس عيشة - سيدي عيسى",
        "مادي عيسى - سيدي عيسى",
        "حيدر بلقاسم - سيدي عيسى",
        "يحياوي عبد الرحمان - سيدي عيسى",
        "عبداوي عبد الرحمان البصيري - عين الحجل",
        "حملاوي بولعراس - عين الحجل",
        "بلطرش ثامر - عين الحجل",
        "عبدلي محمد - عين الحجل",
        "تريكي عيسى - عين الحجل",
        "الفارابي - سيدي هجرس",
        "ضيف السلامي - بوطي السايح",
        "عبد الحميد بن باديس - خطوطي سد الجير",
        "السعيد الورتلاني - بني يلمان",
        "شيشي بوبكر - بني يلمان"
    ];

    // وظيفة فلترة قائمة أماكن العمل
    function filterWorkplaces(query) {
        query = query.trim().toLowerCase();
        workplaceResults.innerHTML = '';

        if (query === '') {
            // إذا كان الاستعلام فارغًا، أظهر كل الخيارات
            workplaces.forEach(place => {
                addWorkplaceItem(place, '');
            });
            return workplaces.length;
        }

        // فلترة الأماكن التي تحتوي على كلمة البحث
        const filteredPlaces = workplaces.filter(place =>
            place.toLowerCase().indexOf(query) !== -1
        );

        // إنشاء عناصر القائمة المنسدلة
        filteredPlaces.forEach(place => {
            // تمييز الجزء المطابق للبحث
            const highlightedPlace = highlightMatch(place, query);
            addWorkplaceItem(place, highlightedPlace);
        });

        return filteredPlaces.length;
    }

    // إضافة عنصر لقائمة الأماكن
    function addWorkplaceItem(originalPlace, highlightedContent) {
        const item = document.createElement('div');
        item.className = 'workplace-dropdown-item';
        item.dataset.value = originalPlace;

        if (highlightedContent) {
            item.innerHTML = highlightedContent;
        } else {
            item.textContent = originalPlace;
        }

        item.addEventListener('click', function() {
            workplaceInput.value = originalPlace;
            workplaceDropdown.classList.add('d-none');
            console.log('تم اختيار مكان العمل:', originalPlace);

            // إضافة تأثير بصري لتأكيد الاختيار
            workplaceInput.style.borderColor = '#1976d2';
            workplaceInput.style.backgroundColor = '#e3f2fd';

            // إعادة الحقل إلى المظهر الطبيعي بعد فترة
            setTimeout(() => {
                workplaceInput.style.borderColor = '';
                workplaceInput.style.backgroundColor = '';
            }, 1000);

            // التركيز على الحقل التالي
            document.getElementById('phone_number').focus();
        });

        workplaceResults.appendChild(item);
    }

    // تمييز النص المطابق للبحث
    function highlightMatch(text, query) {
        const regex = new RegExp('(' + query + ')', 'gi');
        return text.replace(regex, '<span class="workplace-highlight">$1</span>');
    }

    // إعداد مستمعي الأحداث
    function setupEventListeners() {
        if (!workplaceInput || !workplaceDropdown || !workplaceResults) {
            console.error('لم يتم العثور على بعض العناصر المطلوبة');
            return;
        }

        // إظهار القائمة المنسدلة عند التركيز على حقل مكان العمل
        workplaceInput.addEventListener('focus', function() {
            const numResults = filterWorkplaces(this.value);
            if (workplaceDropdown) {
                workplaceDropdown.classList.remove('d-none');
                console.log('تم إظهار القائمة المنسدلة عند التركيز');
            }
        });

        // تحديث القائمة المنسدلة عند الكتابة
        workplaceInput.addEventListener('input', function() {
            const query = this.value.trim();

            if (!workplaceDropdown || !workplaceResults || !workplaceAddNew) {
                console.error('لم يتم العثور على عناصر القائمة المنسدلة');
                return;
            }

            const numResults = filterWorkplaces(query);

            // إظهار القائمة المنسدلة
            workplaceDropdown.classList.remove('d-none');
            console.log('تم إظهار القائمة المنسدلة عند الكتابة');

            // تحديث حالة زر الإضافة في القائمة المنسدلة
            updateAddNewWorkplaceButton(query, numResults);
        });

        // إضافة مكان عمل جديد (من القائمة المنسدلة)
        const addWorkplaceButton = document.querySelector('.btn-add-workplace');
        if (addWorkplaceButton) {
            addWorkplaceButton.addEventListener('click', handleAddNewWorkplace);
        }

        // إخفاء القائمة المنسدلة عند النقر خارجها
        document.addEventListener('click', function(event) {
            if (workplaceInput && workplaceDropdown &&
                !workplaceInput.contains(event.target) &&
                !workplaceDropdown.contains(event.target)) {
                workplaceDropdown.classList.add('d-none');
            }
        });

        // التنقل في القائمة باستخدام لوحة المفاتيح
        workplaceInput.addEventListener('keydown', handleKeyboardNavigation);
    }

    // تحديث حالة زر إضافة مكان عمل جديد في القائمة المنسدلة
    function updateAddNewWorkplaceButton(query, numResults) {
        if (!workplaceAddNew) return;

        if (numResults === 0 && query !== '') {
            console.log('لا توجد نتائج، سيتم إظهار زر الإضافة في القائمة');

            // تحديث نص زر القائمة المنسدلة
            const addButtonText = document.querySelector('.btn-add-workplace span');
            if (addButtonText) {
                addButtonText.textContent = `إضافة "${query}" كمكان عمل جديد`;
            }

            // إظهار زر الإضافة في القائمة المنسدلة
            workplaceAddNew.style.display = 'block';
            workplaceAddNew.classList.remove('d-none');
        } else {
            // إخفاء زر الإضافة في القائمة المنسدلة
            workplaceAddNew.style.display = 'none';
            workplaceAddNew.classList.add('d-none');
        }
    }

    // معالجة إضافة مكان عمل جديد
    function handleAddNewWorkplace() {
        const newWorkplace = workplaceInput.value.trim();

        // التحقق من وجود قيمة
        if (!newWorkplace) {
            alert('يرجى إدخال اسم مكان العمل أولاً');
            workplaceInput.focus();
            return;
        }

        // التحقق من أن المكان غير موجود بالفعل في القائمة
        if (workplaces.includes(newWorkplace)) {
            // إذا كان موجودًا بالفعل، اختر هذا المكان
            workplaceInput.value = newWorkplace;
            workplaceDropdown.classList.add('d-none');
            return;
        }

        // إضافة مكان العمل الجديد مباشرة بدون تأكيد
        console.log('تمت إضافة مكان عمل جديد:', newWorkplace);

        // إضافة القيمة إلى القائمة ليتم اختيارها عند التقديم
        workplaces.push(newWorkplace);

        // قبول المكان الجديد وإغلاق القائمة
        workplaceInput.value = newWorkplace;
        workplaceDropdown.classList.add('d-none');

        // إضافة تأثير بصري للتأكيد
        workplaceInput.style.borderColor = '#4caf50';
        workplaceInput.style.backgroundColor = '#f1f8e9';

        // إعادة الحقل إلى المظهر الطبيعي بعد فترة
        setTimeout(() => {
            workplaceInput.style.borderColor = '';
            workplaceInput.style.backgroundColor = '';
        }, 1500);

        // تركيز على الحقل التالي
        document.getElementById('phone_number').focus();
    }

    // التعامل مع مفاتيح التنقل في القائمة المنسدلة
    function handleKeyboardNavigation(e) {
        if (!workplaceResults) return;

        const items = workplaceResults.querySelectorAll('.workplace-dropdown-item');
        const activeItem = workplaceResults.querySelector('.workplace-dropdown-item.active');

        if (items.length === 0) return;

        if (e.key === 'ArrowDown') {
            e.preventDefault();
            if (!activeItem) {
                items[0].classList.add('active');
            } else {
                const currentIndex = Array.from(items).indexOf(activeItem);
                activeItem.classList.remove('active');
                const nextIndex = (currentIndex + 1) % items.length;
                items[nextIndex].classList.add('active');
                items[nextIndex].scrollIntoView({ block: 'nearest' });
            }
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            if (!activeItem) {
                items[items.length - 1].classList.add('active');
            } else {
                const currentIndex = Array.from(items).indexOf(activeItem);
                activeItem.classList.remove('active');
                const prevIndex = (currentIndex - 1 + items.length) % items.length;
                items[prevIndex].classList.add('active');
                items[prevIndex].scrollIntoView({ block: 'nearest' });
            }
        } else if (e.key === 'Enter' && activeItem) {
            e.preventDefault();
            workplaceInput.value = activeItem.dataset.value;
            workplaceDropdown.classList.add('d-none');
        } else if (e.key === 'Escape') {
            workplaceDropdown.classList.add('d-none');
        }
    }

    // وظيفة إظهار وإخفاء كلمة المرور
    togglePasswordButton.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);

        // تغيير أيقونة العين
        if (type === 'text') {
            passwordIcon.classList.remove('fa-eye-slash');
            passwordIcon.classList.add('fa-eye');
        } else {
            passwordIcon.classList.remove('fa-eye');
            passwordIcon.classList.add('fa-eye-slash');
        }
    });

    // التحقق من البريد الإلكتروني
    emailInput.addEventListener('input', function() {
        let email = this.value.toLowerCase();
        if (email && !email.endsWith('@gmail.com')) {
            this.setCustomValidity('يجب أن يكون البريد الإلكتروني من نوع Gmail');
        } else {
            this.setCustomValidity('');
        }
    });

    // التحقق من رقم الهاتف
    phoneInput.addEventListener('input', function() {
        if (this.value && !/^\d+$/.test(this.value)) {
            this.setCustomValidity('يرجى إدخال أرقام فقط');
        } else if (this.value && this.value.length !== 8 && this.value.length > 0) {
            this.setCustomValidity('يجب إدخال 8 أرقام بالضبط');
        } else {
            this.setCustomValidity('');
        }
    });

    // تحميل الصفحة وتهيئة المتغيرات
    console.log('تم تحميل الصفحة وتهيئة العناصر:');
    console.log('workplaceInput:', workplaceInput);
    console.log('workplaceDropdown:', workplaceDropdown);
    console.log('workplaceResults:', workplaceResults);
    console.log('workplaceAddNew:', workplaceAddNew);

    // إخفاء القائمة المنسدلة في البداية
    if (workplaceDropdown) {
        workplaceDropdown.classList.add('d-none');
    }

    // معالجة إرسال النموذج للتأكد من صحة البيانات
    const registrationForm = document.querySelector('form[action*="register"]');
    if (registrationForm) {
        registrationForm.addEventListener('submit', function(event) {
            // التحقق من أن مكان العمل تم إدخاله
            const workplaceValue = workplaceInput.value.trim();
            if (!workplaceValue) {
                event.preventDefault();
                alert('يرجى إدخال أو اختيار مكان العمل');
                workplaceInput.focus();
                return false;
            }

            console.log('إرسال النموذج مع مكان العمل:', workplaceValue);
            return true;
        });
    }

    // إضافة مستمعي الأحداث
    setupEventListeners();
});
</script>
{% endblock %}
