/* نمط الملف الشخصي */
.info-card {
    transition: all 0.3s ease;
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1) !important;
}

.info-item {
    margin-bottom: 12px;
    padding-bottom: 12px;
    border-bottom: 1px dashed rgba(0,0,0,0.1);
}

.info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.info-label {
    font-size: 0.9rem;
    margin-bottom: 4px;
}

.info-value {
    font-weight: 500;
}

.admin-img {
    width: 120px;
    height: 120px;
    object-fit: cover;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 50px;
    font-weight: bold;
    margin: 0 auto;
}

.highlight-update {
    animation: highlightEffect 2s ease;
}

@keyframes highlightEffect {
    0% { background-color: rgba(25, 135, 84, 0.2); }
    100% { background-color: transparent; }
}

/* إضافة تأكيد على حقول كلمة المرور */
#password, #password_confirm {
    display: block !important;
    width: 100% !important;
    visibility: visible !important;
    opacity: 1 !important;
    padding-left: 40px !important; /* إضافة مساحة في اليسار لأيقونة العين */
    padding-right: initial !important; /* إلغاء المساحة في اليمين */
}

/* تنسيق أزرار إظهار/إخفاء كلمة المرور */
.password-toggle-btn {
    cursor: pointer;
    background: transparent;
    color: #6c757d;
    transition: all 0.2s ease;
    z-index: 10;
    padding: 0 !important;
    margin: 0 !important;
    border: none;
    left: 0 !important;
    right: auto !important;
    width: 40px;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.password-toggle-btn:hover {
    color: #0d6efd;
}

.password-toggle-btn:focus {
    box-shadow: none;
    outline: none;
}

.password-toggle-btn i {
    font-size: 16px;
}

/* تحسين مظهر نموذج التعديل */
.section-edit {
    padding: 15px;
    border-radius: 5px;
    background-color: #f8f9fa;
    margin-top: 10px;
}

/* Navigation Tabs Styling */
.nav-tabs {
    background-color: transparent !important;
    padding: 10px 10px 0;
    border-radius: 5px 5px 0 0;
    display: flex;
    margin-bottom: 20px;
    margin-top: -10px;
    justify-content: center;
}

.nav-tabs .nav-item {
    margin-right: 5px;
    text-align: center;
    margin-top: -15px;
}

.nav-tabs .nav-link {
    color: white !important;
    border: none !important;
    border-radius: 15px !important;
    padding: 10px 20px;
    transition: all 0.3s ease;
    text-align: center;
    min-width: 150px;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    background-color: #007bff !important;
}

.nav-tabs .nav-link:hover {
    background-color: #0056b3 !important;
    color: white !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.nav-tabs .nav-link.active {
    background-color: #198754 !important;
    color: white !important;
    font-weight: bold;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* إضافة تأثيرات إضافية */
.nav-tabs .nav-link:focus {
    outline: none;
    box-shadow: none;
}

/* تحسين المظهر على الشاشات الصغيرة */
@media (max-width: 768px) {
    .nav-tabs {
        flex-wrap: wrap;
    }

    .nav-tabs .nav-item {
        margin-bottom: 5px;
    }
}

/* تنسيق حاوية الأزرار - أفقية لجميع أحجام الشاشات */
.vertical-buttons-container {
    position: absolute;
    z-index: 1000;
    display: flex;
    flex-direction: row; /* أفقي دائماً */
    gap: 10px;
}

/* تنسيق الأزرار داخل الحاوية */
.vertical-buttons-container .btn {
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

/* تنسيق للشاشات الكبيرة - أفقي */
@media (min-width: 769px) {
    .vertical-buttons-container {
        top: 20px;
        left: 20px;
        gap: 15px; /* زيادة المسافة بين الأزرار */
    }

    .vertical-buttons-container .btn {
        width: 50px;
        height: 50px;
    }
}

/* تنسيق للشاشات الصغيرة - أفقي */
@media (max-width: 768px) {
    .vertical-buttons-container {
        top: 15px;
        left: 10px;
        display: flex;
        flex-direction: row; /* أفقي للشاشات الصغيرة */
        gap: 10px;
        position: absolute;
        z-index: 1050;
    }

    .vertical-buttons-container .btn {
        width: 35px;
        height: 35px;
        font-size: 0.8rem;
    }
}