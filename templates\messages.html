{% extends "base.html" %}

{% block head %}
<!-- إضافة مكتبة Select2 للقائمة المنسدلة القابلة للبحث -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js" defer></script>

<style>
/* تنسيقات خاصة بصفحة الرسائل للهواتف المحمولة */
@media (max-width: 576px) {
    /* تغيير ترتيب العناصر في صفحة الرسائل */
    .container.mt-4 .row {
        display: flex !important;
        flex-direction: column !important;
    }

    /* جعل نموذج إرسال الرسالة في الأسفل */
    .container.mt-4 .row > .col-md-4 {
        order: 2 !important;
    }

    /* جعل صناديق الرسائل في الأعلى */
    .container.mt-4 .row > .col-md-8 {
        order: 1 !important;
        margin-bottom: 20px !important;
    }
}

/* تنسيقات Select2 */
.select2-container--default .select2-selection--multiple,
.select2-container--default .select2-selection--single {
    border-radius: 0.25rem;
    border: 1px solid #ced4da;
    min-height: 45px; /* زيادة ارتفاع القائمة المنسدلة */
    padding: 5px 8px;
}

.select2-container--default.select2-container--focus .select2-selection--multiple,
.select2-container--default.select2-container--focus .select2-selection--single {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* تعديل ارتفاع القائمة المنسدلة الفردية */
.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 35px;
}

/* تعديل موضع السهم في القائمة المنسدلة الفردية */
.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 43px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #0d6efd;
    border: none;
    color: white;
    border-radius: 4px;
    padding: 2px 8px;
    margin-top: 4px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: white;
    margin-right: 5px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #f8f9fa;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #0d6efd;
}

.select2-container--default .select2-search--inline .select2-search__field {
    margin-top: 7px;
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered {
    padding: 0 8px;
}

/* تنسيق زر الرسائل الجماعية */
.group-message-toggle {
    cursor: pointer;
    transition: all 0.3s ease;
}

.group-message-toggle:hover {
    color: #0d6efd;
}

.group-message-info {
    background-color: rgba(13, 110, 253, 0.1);
    border-right: 3px solid #0d6efd;
    padding: 8px 12px;
    border-radius: 4px;
    margin-bottom: 10px;
    font-size: 0.9rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container mt-4" dir="rtl">
    <div class="row">
        <!-- إرسال رسالة جديدة -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">إرسال رسالة جديدة</h5>
                </div>
                <div class="card-body">
                    <form id="message-form" action="{{ url_for('send_message') }}" method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <div class="mb-3">
                            {% if current_user.is_admin %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label mb-0">المستلم</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="group-message-toggle">
                                    <label class="form-check-label group-message-toggle" for="group-message-toggle">
                                        <i class="fas fa-users me-1"></i> رسالة جماعية
                                    </label>
                                </div>
                            </div>
                            <div id="group-message-info" class="group-message-info mb-2" style="display: none;">
                                <i class="fas fa-info-circle me-1"></i>
                                يمكنك إرسال رسالة إلى عدة مستلمين في نفس الوقت
                            </div>
                            <!-- حاوية القائمة المنسدلة الفردية -->
                            <div id="single-recipient-container">
                                <select id="single-recipient" class="form-select recipient-select" name="recipient_id" required>
                                    <option value="" disabled selected>ابحث عن المستلم أو اختره من القائمة</option>
                                    {% for user in users %}
                                    <option value="{{ user.id }}" data-profile-url="{{ url_for('profile', user_id=user.id) }}">{{ user.teacher_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <!-- قائمة منسدلة قابلة للبحث للرسائل الجماعية -->
                            <div id="multiple-recipients-container" style="display: none;">
                                <select id="multiple-recipients" class="form-select recipient-select" name="recipient_ids[]" multiple="multiple">
                                    {% for user in users %}
                                    <option value="{{ user.id }}" data-profile-url="{{ url_for('profile', user_id=user.id) }}">{{ user.teacher_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            {% else %}
                            <select class="form-select" name="recipient_id" required oninvalid="this.setCustomValidity('يرجى اختيار المستلم')" oninput="this.setCustomValidity('')">
                                <option value="" disabled selected>اختر مفتش المادة من القائمة المنسدلة</option>
                                {% for user in users %}
                                <option value="{{ user.id }}" data-profile-url="{{ url_for('profile', user_id=user.id) }}">{{ user.teacher_name }}</option>
                                {% endfor %}
                            </select>
                            {% endif %}
                        </div>
                        <div class="mb-3">
                            <label for="content" class="form-label">الرسالة</label>
                            <textarea class="form-control" name="content" rows="4" required id="content" oninvalid="this.setCustomValidity('يرجى كتابة نص الرسالة')" oninput="this.setCustomValidity('')"></textarea>
                        </div>
                        <div class="mb-3">
                            <input type="file" class="form-control d-none" name="attachment" id="attachment" onchange="showSelectedFile(this)" multiple>
                            <button type="button" class="btn btn-outline-secondary w-100" onclick="document.getElementById('attachment').click()">
                                <i class="fas fa-paperclip"></i>
                                <span class="me-2">ارفاق ملف</span>
                            </button>
                            <div id="file-type-info" class="text-danger mt-2" style="font-size: 0.75rem; display: none;">
                                <i class="fas fa-exclamation-circle me-1"></i>
                                صيغة الملف غير صالحة
                            </div>
                            <div id="selected-file-info" class="mt-2"></div>
                            <div class="form-text text-muted">
                                <small>
                                    <i class="fas fa-info-circle me-1"></i>
                                    الملفات المسموح بها:
                                    <br>
                                    <span class="ms-3">• مستندات: PDF, DOC, DOCX, XLS, XLSX</span>
                                    <br>
                                    <span class="ms-3">• الحد الأقصى لحجم الملف: 10 ميجابايت</span>
                                </small>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-paper-plane"></i> إرسال
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- الرسائل المستلمة -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">الرسائل المستلمة</h5>
                        <span class="badge bg-light text-dark ms-2" id="unread-count"></span>
                    </div>
                </div>
                <div class="card-body">
                    <div id="received-form">
                        {% with messages=received_messages, is_received=True %}
                            {% include 'message_list.html' %}
                        {% endwith %}
                    </div>
                </div>
            </div>

            <!-- الرسائل المرسلة -->
            <div class="card">
                <div class="card-header bg-info text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">الرسائل المرسلة</h5>
                    </div>
                </div>
                <div class="card-body">
                    <div id="sent-form">
                        {% with messages=sent_messages, is_received=False %}
                            {% include 'message_list.html' %}
                        {% endwith %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إضافة الصوت للإشعارات -->
<audio id="notification-sound" src="https://assets.mixkit.co/active_storage/sfx/2869/2869-preview.mp3"></audio>

<!-- رسائل تنبيه -->
{% if get_flashed_messages() %}
<div class="row">
    <div class="col-12">
        {% for message in get_flashed_messages() %}
        <div class="alert alert-{{ message.split('|')[0] }} alert-dismissible fade show" role="alert">
            {{ message.split('|')[1] }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}

<script>
let lastCount = 0;

function showMessage(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.style.position = 'fixed';
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '1050';
    alertDiv.style.padding = '8px 16px';
    alertDiv.style.borderRadius = '4px';
    alertDiv.style.fontSize = '14px';
    alertDiv.style.maxWidth = '250px';
    alertDiv.style.textAlign = 'center';
    alertDiv.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
    alertDiv.innerHTML = message;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        alertDiv.style.animation = 'fadeOut 0.5s ease-in-out forwards';
        setTimeout(() => alertDiv.remove(), 500);
    }, 2500);
}

function updateUnreadCount() {
    fetch('{{ url_for("get_unread_count") }}')
        .then(response => response.json())
        .then(data => {
            const badge = document.getElementById('unread-count');
            if (data.count > 0) {
                badge.textContent = `${data.count} رسالة غير مقروءة`;
                badge.style.display = 'inline';
                badge.style.cursor = 'pointer';

                if (data.count > lastCount) {
                    const audio = document.getElementById('notification-sound');
                    audio.play().catch(e => console.log('تم منع تشغيل الصوت'));
                    // تحديث الرسائل مباشرة عند وصول رسالة جديدة
                    updateReceivedMessages();
                }
            } else {
                badge.style.display = 'none';
            }
            lastCount = data.count;
        });
}

// دالة لتحديث الرسائل
async function updateReceivedMessages() {
    try {
        const [messageResponse, unreadResponse] = await Promise.all([
            fetch('{{ url_for("get_messages") }}'),
            fetch('{{ url_for("get_unread_count") }}')
        ]);

        const [messageData, unreadData] = await Promise.all([
            messageResponse.json(),
            unreadResponse.json()
        ]);

        // تحديث الرسائل المستلمة
        const receivedForm = document.getElementById('received-form');
        if (receivedForm) {
            receivedForm.innerHTML = messageData.received;
        }

        // تحديث الرسائل المرسلة
        const sentForm = document.getElementById('sent-form');
        if (sentForm) {
            sentForm.innerHTML = messageData.sent;
        }

        // تحويل الروابط والبريد الإلكتروني في الرسائل إلى روابط قابلة للنقر
        convertLinksInMessages();

        // تحديث عدد الرسائل غير المقروءة في الصفحة
        const badge = document.getElementById('unread-count');
        if (unreadData.count > 0) {
            badge.textContent = `${unreadData.count} رسالة غير مقروءة`;
            badge.style.display = 'inline';
            badge.style.cursor = 'pointer';
        } else {
            badge.style.display = 'none';
        }

        // تحديث عدد الرسائل في شريط التنقل
        const navBadge = document.getElementById('nav-unread-count');
        if (navBadge) {
            if (unreadData.count > 0) {
                navBadge.textContent = unreadData.count;
                navBadge.style.display = 'inline';
            } else {
                navBadge.style.display = 'none';
            }
        }

        // تشغيل الصوت إذا زاد عدد الرسائل
        if (lastCount !== null && unreadData.count > lastCount) {
            const audio = document.getElementById('notification-sound');
            audio.play().catch(e => console.log('تم منع تشغيل الصوت'));
        }

        lastCount = unreadData.count;
        window.lastNavCount = unreadData.count; // مزامنة مع شريط التنقل
    } catch (error) {
        console.error('Error updating messages:', error);
    }
}

// جعل الدالة متاحة عالمياً
window.updateReceivedMessages = updateReceivedMessages;

// إضافة مستمع النقر على الإشعار
document.getElementById('unread-count').addEventListener('click', function() {
    updateReceivedMessages();
});

// تحديث الرسائل كل 5 ثواني
setInterval(updateReceivedMessages, 5000);

// تحديث فوري عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateReceivedMessages();

    // تحويل الروابط والبريد الإلكتروني في نص الرسائل إلى روابط قابلة للنقر
    convertLinksInMessages();

    // إضافة مراقب للتغييرات في DOM لتحويل الروابط في الرسائل الجديدة
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' &&
                (mutation.target.id === 'received-form' || mutation.target.id === 'sent-form')) {
                convertLinksInMessages();
            }
        });
    });

    // بدء مراقبة التغييرات في حاويات الرسائل
    observer.observe(document.getElementById('received-form'), { childList: true });
    observer.observe(document.getElementById('sent-form'), { childList: true });

    // تهيئة القائمة المنسدلة القابلة للبحث للرسائل الفردية
    if (document.getElementById('single-recipient')) {
        $('#single-recipient').select2({
            placeholder: 'ابحث عن المستلم أو اختره من القائمة',
            allowClear: true,
            width: '100%',
            language: {
                noResults: function() {
                    return "لا توجد نتائج مطابقة";
                },
                searching: function() {
                    return "جاري البحث...";
                }
            }
        }).on('select2:select', function() {
            // إخفاء حقل البحث بعد اختيار المستلم
            setTimeout(function() {
                $('.select2-search__field').css('width', '0');
            }, 100);
        }).on('select2:open', function() {
            // إعادة إظهار حقل البحث عند فتح القائمة
            setTimeout(function() {
                $('.select2-search__field').css('width', '100%');
            }, 100);
        });
    }

    // لا نقوم بتهيئة القائمة المنسدلة المتعددة هنا
    // سيتم تهيئتها فقط عند تفعيل وضع الرسالة الجماعية

    // التبديل بين وضع الرسالة الفردية والرسالة الجماعية
    const groupMessageToggle = document.getElementById('group-message-toggle');
    if (groupMessageToggle) {
        groupMessageToggle.addEventListener('change', function() {
            const singleRecipientContainer = document.getElementById('single-recipient-container');
            const singleRecipient = document.getElementById('single-recipient');
            const multipleRecipientsContainer = document.getElementById('multiple-recipients-container');
            const multipleRecipients = document.getElementById('multiple-recipients');
            const groupMessageInfo = document.getElementById('group-message-info');

            if (this.checked) {
                // وضع الرسالة الجماعية
                singleRecipientContainer.style.display = 'none';
                singleRecipient.removeAttribute('required');
                singleRecipient.name = 'recipient_id_disabled';

                // تدمير كائن Select2 للقائمة الفردية
                if ($('#single-recipient').data('select2')) {
                    $('#single-recipient').select2('destroy');
                }

                multipleRecipientsContainer.style.display = 'block';
                multipleRecipients.setAttribute('required', 'required');

                // إعادة تهيئة القائمة المنسدلة المتعددة
                $('#multiple-recipients').select2({
                    placeholder: '',
                    allowClear: true,
                    width: '100%',
                    language: {
                        noResults: function() {
                            return "لا توجد نتائج مطابقة";
                        },
                        searching: function() {
                            return "جاري البحث...";
                        }
                    }
                }).on('select2:select', function() {
                    // إخفاء حقل البحث بعد اختيار المستلم
                    setTimeout(function() {
                        $('.select2-search__field').css('width', '0');
                    }, 100);
                }).on('select2:unselect', function() {
                    // إعادة إظهار حقل البحث عند إلغاء اختيار مستلم
                    setTimeout(function() {
                        $('.select2-search__field').css('width', '100%');
                    }, 100);
                }).on('select2:open', function() {
                    // إعادة إظهار حقل البحث عند فتح القائمة
                    setTimeout(function() {
                        $('.select2-search__field').css('width', '100%');
                    }, 100);
                });

                groupMessageInfo.style.display = 'block';
            } else {
                // وضع الرسالة الفردية
                singleRecipientContainer.style.display = 'block';
                singleRecipient.setAttribute('required', 'required');
                singleRecipient.name = 'recipient_id';

                // إعادة تعيين قيمة القائمة المنسدلة الفردية لتكون فارغة
                $('#single-recipient').val(null);

                // إعادة تهيئة القائمة المنسدلة الفردية
                $('#single-recipient').select2({
                    placeholder: 'ابحث عن المستلم أو اختره من القائمة',
                    allowClear: true,
                    width: '100%',
                    language: {
                        noResults: function() {
                            return "لا توجد نتائج مطابقة";
                        },
                        searching: function() {
                            return "جاري البحث...";
                        }
                    }
                }).on('select2:select', function() {
                    // إخفاء حقل البحث بعد اختيار المستلم
                    setTimeout(function() {
                        $('.select2-search__field').css('width', '0');
                    }, 100);
                }).on('select2:open', function() {
                    // إعادة إظهار حقل البحث عند فتح القائمة
                    setTimeout(function() {
                        $('.select2-search__field').css('width', '100%');
                    }, 100);
                });

                multipleRecipientsContainer.style.display = 'none';
                multipleRecipients.removeAttribute('required');

                // إعادة تعيين القائمة المنسدلة المتعددة
                if ($('#multiple-recipients').data('select2')) {
                    $('#multiple-recipients').val(null).trigger('change');
                    $('#multiple-recipients').select2('destroy');
                }

                groupMessageInfo.style.display = 'none';
            }
        });
    }
});

// تحديث عند إرسال رسالة
document.addEventListener('DOMContentLoaded', function() {
    const messageForm = document.getElementById('message-form');

    if (messageForm) {
        messageForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            // التحقق من اختيار المستلم (فردي أو جماعي)
            const isGroupMessage = document.getElementById('group-message-toggle') && document.getElementById('group-message-toggle').checked;

            if (isGroupMessage) {
                // التحقق من اختيار مستلمين للرسالة الجماعية
                const multipleRecipients = $('#multiple-recipients').val();
                if (!multipleRecipients || multipleRecipients.length === 0) {
                    showMessage('يرجى اختيار مستلم واحد على الأقل', 'danger');
                    return;
                }
            } else {
                // التحقق من اختيار مستلم للرسالة الفردية
                const recipientSelect = this.querySelector('select[name="recipient_id"]');
                if (!recipientSelect || !recipientSelect.value) {
                    showMessage('يرجى اختيار مستلم من القائمة', 'danger');
                    return;
                }
            }

            const content = document.querySelector('textarea[name="content"]');
            if (!content.value.trim()) {
                showMessage('يرجى كتابة نص الرسالة', 'danger');
                return false;
            }

            const fileInput = document.getElementById('attachment');

            // التحقق من ملفات المرفقات قبل الإرسال
            if (fileInput.files.length > 0) {
                let hasInvalidFile = false;
                let hasOversizedFile = false;

                for (let i = 0; i < fileInput.files.length; i++) {
                    const file = fileInput.files[i];
                    const fileName = file.name;
                    const fileSize = file.size / (1024 * 1024); // الحجم بالميجابايت
                    const ext = fileName.split('.').pop().toLowerCase();

                    // التحقق من امتداد الملف
                    if (!['pdf', 'doc', 'docx', 'xls', 'xlsx'].includes(ext)) {
                        hasInvalidFile = true;
                        break;
                    }

                    // التحقق من حجم الملف
                    if (fileSize > 10) {
                        hasOversizedFile = true;
                        break;
                    }
                }

                if (hasInvalidFile) {
                    showMessage('صيغة الملف غير صالحة', 'danger');
                    return false;
                }

                if (hasOversizedFile) {
                    showMessage('يجب أن لا يتجاوز حجم الملف 10 ميجابايت', 'danger');
                    return false;
                }
            }

            const submitButton = this.querySelector('button[type="submit"]');
            const originalButtonHTML = submitButton.innerHTML;

            try {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';

                const formData = new FormData(this);
                const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                // تحسين رفع الملفات المتعددة
                if (fileInput.files.length > 0) {
                    // إزالة الملف القديم من FormData (إذا تم إضافته تلقائيًا)
                    formData.delete('attachment');

                    // إضافة كل ملف مرفق
                    for (let i = 0; i < fileInput.files.length; i++) {
                        formData.append('attachment', fileInput.files[i]);
                    }
                }

                // تحديد المسار بناءً على نوع الرسالة (فردية أو جماعية)
                const isGroupMessage = document.getElementById('group-message-toggle') && document.getElementById('group-message-toggle').checked;
                const endpoint = isGroupMessage ? '/send_group_message' : '/send_message';

                // إضافة معلومات إضافية للرسائل الجماعية
                if (isGroupMessage) {
                    // إزالة حقل recipient_id_disabled إذا كان موجودًا
                    formData.delete('recipient_id_disabled');

                    // إضافة علامة للإشارة إلى أن هذه رسالة جماعية
                    formData.append('is_group_message', 'true');

                    // تحديث نص زر الإرسال
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري إرسال الرسائل الجماعية...';
                }

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': token
                    },
                    body: formData,
                    credentials: 'same-origin'
                });

                const result = await response.json();

                if (result.status === 'success') {
                    // مسح النموذج
                    this.reset();
                    // مسح اسم الملف المرفق
                    document.getElementById('selected-file-info').innerHTML = '';
                    // تحديث الرسائل
                    await updateReceivedMessages();

                    // إعادة تعيين القوائم المنسدلة
                    if (document.getElementById('single-recipient')) {
                        $('#single-recipient').val(null).trigger('change');
                    }
                    if (document.getElementById('multiple-recipients')) {
                        $('#multiple-recipients').val(null).trigger('change');
                    }

                    // إعادة تعيين وضع الرسالة الجماعية
                    const groupMessageToggle = document.getElementById('group-message-toggle');
                    if (groupMessageToggle && groupMessageToggle.checked) {
                        groupMessageToggle.checked = false;
                        // إعادة عرض القائمة المنسدلة الفردية
                        document.getElementById('single-recipient-container').style.display = 'block';
                        document.getElementById('single-recipient').setAttribute('required', 'required');
                        document.getElementById('single-recipient').name = 'recipient_id';

                        // إعادة تعيين قيمة القائمة المنسدلة الفردية لتكون فارغة
                        $('#single-recipient').val(null);

                        // إعادة تهيئة القائمة المنسدلة الفردية
                        $('#single-recipient').select2({
                            placeholder: 'ابحث عن المستلم أو اختره من القائمة',
                            allowClear: true,
                            width: '100%',
                            language: {
                                noResults: function() {
                                    return "لا توجد نتائج مطابقة";
                                },
                                searching: function() {
                                    return "جاري البحث...";
                                }
                            }
                        }).on('select2:select', function() {
                            // إخفاء حقل البحث بعد اختيار المستلم
                            setTimeout(function() {
                                $('.select2-search__field').css('width', '0');
                            }, 100);
                        }).on('select2:open', function() {
                            // إعادة إظهار حقل البحث عند فتح القائمة
                            setTimeout(function() {
                                $('.select2-search__field').css('width', '100%');
                            }, 100);
                        });

                        // إخفاء القائمة المنسدلة المتعددة
                        document.getElementById('multiple-recipients-container').style.display = 'none';
                        document.getElementById('multiple-recipients').removeAttribute('required');
                        // إعادة تعيين القائمة المنسدلة المتعددة
                        if ($('#multiple-recipients').data('select2')) {
                            $('#multiple-recipients').val(null).trigger('change');
                            $('#multiple-recipients').select2('destroy');
                        }
                        // إخفاء معلومات الرسالة الجماعية
                        document.getElementById('group-message-info').style.display = 'none';
                    }

                    // عرض رسالة النجاح على الزر نفسه
                    const successButton = submitButton;

                    // تحديد نص رسالة النجاح بناءً على نوع الرسالة
                    const isGroupMessage = result.group_message === true;
                    const successMessage = isGroupMessage ?
                        `<i class="fas fa-check-circle"></i> تم إرسال ${result.sent_count || ''} رسالة بنجاح` :
                        '<i class="fas fa-check-circle"></i> تم إرسال الرسالة بنجاح';

                    successButton.innerHTML = successMessage;
                    successButton.classList.remove('btn-primary');
                    successButton.classList.add('btn-success');

                    // إعادة الزر إلى حالته الأصلية بعد فترة
                    setTimeout(() => {
                        successButton.innerHTML = originalButtonHTML;
                        successButton.classList.remove('btn-success');
                        successButton.classList.add('btn-primary');
                    }, 3000);
                } else {
                    showMessage(result.message || 'حدث خطأ أثناء إرسال الرسالة', 'danger');
                }
            } catch (error) {
                console.error('Error:', error);
                showMessage('حدث خطأ أثناء إرسال الرسالة', 'danger');
            } finally {
                submitButton.disabled = false;
                if (result?.status !== 'success') {
                    submitButton.innerHTML = originalButtonHTML;
                }
            }
        });
    }
});

// دالة لإنشاء عنصر الرسالة
function createMessageElement(message, isReceived) {
    const date = new Date(message.timestamp);
    const formattedDate = date.toLocaleString('ar', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });

    const recipientOrSenderName = isReceived ?
        `من: ${message.sender.teacher_name}` :
        `إلى: ${message.recipient.teacher_name}`;

    // تحديد نوع الملف وأيقونته
    let attachmentIcon = '';
    let fileTypeText = '';
    let isForwarded = false;

    if (message.attachment_name) {
        const ext = message.attachment_name.split('.').pop().toLowerCase();

        // التحقق مما إذا كان الملف معاد توجيهه
        if (message.attachment && message.attachment.includes('forwarded')) {
            isForwarded = true;
        }

        if (ext === 'pdf') {
            attachmentIcon = '<i class="fas fa-file-pdf text-danger ms-1"></i>';
            fileTypeText = 'ملف PDF';
        } else if (['doc', 'docx'].includes(ext)) {
            attachmentIcon = '<i class="fas fa-file-word text-primary ms-1"></i>';
            fileTypeText = 'ملف Word';
        } else if (['xls', 'xlsx'].includes(ext)) {
            attachmentIcon = '<i class="fas fa-file-excel text-success ms-1"></i>';
            fileTypeText = 'ملف Excel';
        } else {
            attachmentIcon = '<i class="fas fa-file text-secondary ms-1"></i>';
            fileTypeText = 'ملف';
        }
    }

    return `
    <div class="message-item mb-3 p-3 border rounded${isReceived && !message.is_read ? ' bg-light' : ''}" data-message-id="${message.id}">
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <strong>${recipientOrSenderName}</strong>
                <small class="text-muted d-block">${formattedDate}</small>
            </div>
            <div class="d-flex align-items-center">
                ${isReceived && !message.is_read ? '<span class="badge bg-primary me-2">جديد</span>' : ''}
                <button type="button" class="btn btn-link text-info p-0 me-2" onclick="showForwardModal(${message.id})" title="إعادة توجيه الرسالة">
                    <i class="fas fa-share"></i>
                </button>
                <button type="button" class="btn btn-link text-danger p-0" onclick="deleteMessage(${message.id})" title="حذف الرسالة">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </div>
        </div>
        <div class="message-content mt-3">
            <p class="mb-2">${message.content}</p>
            ${message.attachment_name ? `
            <div class="attachment-section">
                <div class="d-flex align-items-center p-2 bg-light rounded border">
                    <i class="fas fa-paperclip text-secondary me-1"></i>
                    <div class="d-flex flex-column flex-grow-1">
                        <a href="/download_attachment/${message.id}" class="text-primary text-decoration-none">
                            <span class="attachment-name fw-bold">${message.attachment_name}</span>
                        </a>
                        <small class="text-muted">
                            ${attachmentIcon} ${fileTypeText}
                            ${isForwarded ? '<span class="badge bg-info ms-2">معاد توجيهه</span>' : ''}
                        </small>
                    </div>
                    <a href="/download_attachment/${message.id}" class="btn btn-sm btn-outline-primary ms-2" title="تحميل الملف">
                        <i class="fas fa-download"></i>
                    </a>
                </div>
            </div>
            ` : ''}
        </div>
        ${isReceived && !message.is_read ? `
        <div class="text-end mt-2">
            <a href="/mark_as_read/${message.id}" class="btn btn-sm btn-success mark-as-read">
                <i class="fas fa-check me-1"></i>
                تم القراءة
            </a>
        </div>
        ` : ''}
    </div>
    `;
}

// تحديث اسم الملف عند اختياره
function showSelectedFile(input) {
    const selectedFiles = input.files;
    const selectedFileInfo = document.getElementById('selected-file-info');
    const fileTypeInfo = document.getElementById('file-type-info');

    // إعادة تعيين الملفات المختارة
    selectedFileInfo.innerHTML = '';
    selectedFileInfo.className = 'mt-2';

    // إخفاء تنبيه نوع الملف بشكل افتراضي
    fileTypeInfo.style.display = 'none';

    // التحقق من الملفات المختارة
    let hasInvalidFile = false;
    let hasOversizedFile = false;
    let validFiles = [];
    let errorHTML = '';

    if (selectedFiles && selectedFiles.length > 0) {
        for (let i = 0; i < selectedFiles.length; i++) {
            const file = selectedFiles[i];
            const fileName = file.name;
            const fileSize = file.size / (1024 * 1024); // الحجم بالميجابايت
            const ext = fileName.split('.').pop().toLowerCase();

            // التحقق من نوع الملف
            if (!['pdf', 'doc', 'docx', 'xls', 'xlsx'].includes(ext)) {
                hasInvalidFile = true;
                continue;
            }

            // التحقق من حجم الملف
            if (fileSize > 10) {
                hasOversizedFile = true;
                continue;
            }

            validFiles.push(file);

            // تحديد نوع الملف وأيقونته
            let fileIcon = '<i class="fas fa-file text-secondary me-1" style="font-size: 0.85rem;"></i>';

            if (ext === 'pdf') {
                fileIcon = '<i class="fas fa-file-pdf text-danger me-1" style="font-size: 0.85rem;"></i>';
            } else if (['doc', 'docx'].includes(ext)) {
                fileIcon = '<i class="fas fa-file-word text-primary me-1" style="font-size: 0.85rem;"></i>';
            } else if (['xls', 'xlsx'].includes(ext)) {
                fileIcon = '<i class="fas fa-file-excel text-success me-1" style="font-size: 0.85rem;"></i>';
            }

            const fileElement = document.createElement('div');
            fileElement.className = 'd-flex align-items-center p-2 border rounded-3 bg-light mb-2 shadow-sm';
            fileElement.style.fontSize = '0.85rem';

            fileElement.innerHTML = `
                ${fileIcon}
                <span class="me-1 text-dark">${fileName}</span>
                <span class="text-muted small ms-1">(${(fileSize).toFixed(2)} MB)</span>
                <button type="button" class="text-danger p-0 border-0 bg-transparent me-auto" style="font-size: 0.85rem;" onclick="clearFileInput()">
                    <i class="fas fa-times-circle"></i>
                </button>
            `;

            selectedFileInfo.appendChild(fileElement);
        }

        // إظهار تنبيه نوع الملف إذا كان هناك ملف غير صالح
        if (hasInvalidFile) {
            fileTypeInfo.style.display = 'block';
        }

        if (hasOversizedFile) {
            errorHTML += '<div class="text-danger mt-1"><i class="fas fa-exclamation-circle me-1"></i>يجب أن لا يتجاوز حجم الملف 10 ميجابايت.</div>';

            // عرض رسائل الخطأ لحجم الملف فقط
            if (errorHTML) {
                const errorContainer = document.createElement('div');
                errorContainer.className = 'invalid-feedback d-block mt-2';
                errorContainer.innerHTML = errorHTML;
                selectedFileInfo.appendChild(errorContainer);
            }
        }

        // إذا لم يكن هناك ملفات صالحة، قم بإعادة تعيين حقل الإدخال
        if (validFiles.length === 0 && !hasInvalidFile && !hasOversizedFile) {
            clearFileInput();
        }
    }
}

function clearFileInput() {
    const input = document.getElementById('attachment');
    input.value = '';

    // إعادة تعيين وإخفاء رسائل الخطأ والملفات المختارة
    const selectedFileInfo = document.getElementById('selected-file-info');
    const fileTypeInfo = document.getElementById('file-type-info');

    if (selectedFileInfo) {
        selectedFileInfo.innerHTML = '';
        selectedFileInfo.className = 'mt-2';
    }

    // إخفاء تنبيه نوع الملف
    if (fileTypeInfo) {
        fileTypeInfo.style.display = 'none';
    }
}

function showForwardModal(messageId) {
    document.getElementById('forward-message-id').value = messageId;
    document.getElementById('forward-content').value = '';
    document.getElementById('forward-recipient').selectedIndex = 0;

    // الحصول على معلومات المرفق إذا وجد
    const messageElement = document.querySelector(`.message-item[data-message-id="${messageId}"]`);
    const attachmentName = messageElement ? messageElement.getAttribute('data-attachment-name') : null;

    const forwardAttachmentInfo = document.getElementById('forward-attachment-info');

    if (attachmentName) {
        forwardAttachmentInfo.innerHTML = `
        <div class="alert alert-info p-2">
            سيتم إعادة توجيه المرفق: <strong>${attachmentName}</strong>
        </div>`;
    } else {
        forwardAttachmentInfo.innerHTML = '';
    }

    const forwardModal = new bootstrap.Modal(document.getElementById('forwardMessageModal'));
    forwardModal.show();
}

// إضافة معالج لزر إعادة التوجيه
document.getElementById('forward-message-btn').addEventListener('click', function() {
    const messageId = document.getElementById('forward-message-id').value;
    const recipientId = document.getElementById('forward-recipient').value;
    const content = document.getElementById('forward-content').value;

    if (!recipientId) {
        showAlert('warning', 'يرجى اختيار المستلم');
        return;
    }

    const formData = new FormData();
    formData.append('recipient_id', recipientId);
    if (content) formData.append('content', content);

    // إضافة CSRF token
    formData.append('csrf_token', document.querySelector('input[name="csrf_token"]').value);

    // إرسال طلب إعادة التوجيه
    fetch(`/forward_message/${messageId}`, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('حدث خطأ في الخادم');
        }
        return response.json();
    })
    .then(data => {
        if (data.status === 'success') {
            bootstrap.Modal.getInstance(document.getElementById('forwardMessageModal')).hide();
            updateReceivedMessages();
            showAlert('success', 'تم إعادة توجيه الرسالة بنجاح');
        } else {
            showAlert('warning', data.message || 'حدث خطأ أثناء إعادة توجيه الرسالة');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('danger', 'حدث خطأ أثناء إعادة توجيه الرسالة');
    });
});
</script>

<style>
.alert {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    max-width: 250px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    animation: fadeOut 0.5s ease-in-out 2.5s forwards;
}

/* تنسيق لرسائل خطأ التحقق من الملفات المرفقة */
#selected-file-info {
    margin-top: 8px;
    margin-bottom: 8px;
    font-size: 0.875rem;
    border-radius: 4px;
}

/* تنسيق عرض الملفات المختارة */
.file-item {
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    position: relative;
}

.file-item:hover {
    background-color: #f0f0f0;
}

/* إزالة الحدود الملونة من الملفات المرفقة */
.file-item.file-pdf,
.file-item.file-word,
.file-item.file-excel {
    border-right: none;
}

.file-icon {
    font-size: 1.25rem;
    width: 28px;
    text-align: center;
}

.file-details {
    line-height: 1.2;
    overflow: hidden;
}

.file-name {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#selected-file-info .text-danger,
#file-type-info {
    color: #dc3545 !important;
    font-weight: 500;
    padding: 4px 8px;
    margin-bottom: 4px;
    border-right: 3px solid #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
    border-radius: 2px;
}

#file-type-info {
    display: block;
    width: 100%;
    font-size: 0.875em;
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

.message-item.sending {
    opacity: 0.7;
    background-color: #f8f9fa;
}

.message-status {
    color: #6c757d;
    font-style: italic;
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #dc3545;
}

.invalid-feedback.d-block {
  display: block !important;
  padding: 0.375rem 0.75rem;
  background-color: rgba(220, 53, 69, 0.1);
  border-right: 3px solid #dc3545;
  border-radius: 0.25rem;
  margin-bottom: 0.5rem;
}
</style>

{% endblock %}
