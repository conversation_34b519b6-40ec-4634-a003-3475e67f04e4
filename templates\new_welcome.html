<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>فضاء أساتذة العلوم الفيزيائية</title>
    <!-- إضافة أيقونة التبويب (Favicon) الخضراء -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/green-favicon.ico') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='images/green-favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='images/green-favicon-16x16.png') }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='styles.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='mobile-styles.css') }}" rel="stylesheet">
    <style>
        /* تأثيرات حركية للأزرار */
        .btn-profile, .btn-logout {
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        /* تم إزالة تنسيق الأزرار العلوية من هنا لأنه تم نقله إلى ملف styles.css */
        /* تنسيق الأزرار العلوية يتم التحكم به الآن من خلال ملف styles.css */

        .btn-profile::after, .btn-logout::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%);
            opacity: 0;
            transition: all 0.4s ease;
        }

        .btn-profile:hover::after, .btn-logout:hover::after {
            opacity: 1;
            transform: scale(1.5);
        }

        .btn-logout:hover {
            transform: rotate(360deg);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-profile:hover {
            transform: scale(1.15);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-profile i, .btn-logout i {
            transition: all 0.3s ease;
        }

        .btn-profile:hover i {
            animation: pulse-light 1.5s infinite;
        }

        .btn-logout:hover i {
            animation: bounce 1s infinite;
        }

        /* تأثيرات حركية لزر الرسائل */
        .btn-message {
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .btn-message::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%);
            opacity: 0;
            transition: all 0.4s ease;
        }

        .btn-message:hover::after {
            opacity: 1;
            transform: scale(1.5);
        }

        .btn-message:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-message i {
            transition: all 0.3s ease;
        }

        .btn-message:hover i {
            animation: envelope-shake 1s infinite;
        }

        /* تأثير وميض عند وصول رسالة جديدة */
        @keyframes message-flash {
            0% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.5); }
            50% { box-shadow: 0 0 20px rgba(255, 193, 7, 0.8); }
            100% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.5); }
        }

        .new-message-flash {
            animation: message-flash 0.6s ease-in-out 3;
        }

        @keyframes envelope-shake {
            0%, 100% { transform: rotate(0); }
            25% { transform: rotate(-10deg); }
            75% { transform: rotate(10deg); }
        }

        /* تعريف الإنيميشن */
        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-8px); }
        }

        @keyframes pulse-light {
            0% { opacity: 1; }
            50% { opacity: 0.6; }
            100% { opacity: 1; }
        }

        /* تنسيق القائمة المنسدلة */
        .dropdown-menu {
            margin-top: 10px;
            border-radius: 15px;
            border: none;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            min-width: 260px;
            padding: 0;
            transform: translateY(10px);
            transition: all 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
            opacity: 0;
            visibility: hidden;
            background: #ffffff;
            overflow: hidden;
            pointer-events: none;
        }

        .dropdown-menu.show {
            transform: translateY(0);
            opacity: 1;
            visibility: visible;
            pointer-events: auto;
        }

        .dropdown-item {
            padding: 8px 20px;
            transition: all 0.2s;
            text-align: right;
            font-weight: 500;
            color: #3a3a3a;
            position: relative;
            z-index: 1;
            overflow: hidden;
        }

        .dropdown-item:hover {
            background: linear-gradient(to right, rgba(30, 136, 229, 0.1), transparent);
            color: #1565c0;
            transform: translateX(-5px);
        }

        .dropdown-item i {
            transition: all 0.3s;
            margin-left: 5px;
        }

        .dropdown-item:hover i {
            color: #1565c0;
            transform: scale(1.2);
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding-top: 30px;
            overflow-x: hidden;
        }

        .welcome-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .welcome-header {
            text-align: center;
            margin-bottom: 50px;
            padding: 20px;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .welcome-title {
            font-size: 2.5rem;
            color: #1e88e5;
            font-weight: bold;
            margin-bottom: 15px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        .welcome-subtitle {
            font-size: 1.2rem;
            color: #555;
            margin-bottom: 0;
        }

        .feature-card {
            background-color: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
            text-align: center;
            position: relative;
            overflow: hidden;
            border: none;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 35px rgba(0, 0, 0, 0.2);
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, var(--bs-primary) 0%, var(--bs-info) 100%);
        }

        .feature-icon {
            margin-bottom: 20px;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 2rem;
            position: relative;
            overflow: hidden;
            transition: all 0.5s ease;
            z-index: 1;
        }

        .feature-icon:hover {
            transform: rotate(15deg) scale(1.15);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            cursor: pointer;
        }

        .feature-icon::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(-100%);
            z-index: -1;
            transition: transform 0.4s ease;
        }

        .feature-icon:hover::after {
            transform: translateX(0);
        }

        .feature-icon i {
            transition: all 0.4s ease;
        }

        .feature-icon:hover i {
            transform: scale(1.2);
            animation: pulse-light 1.5s infinite;
        }

        .feature-title {
            font-size: 1.4rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .feature-description {
            color: #666;
            margin-bottom: 20px;
            font-size: 1rem;
            line-height: 1.6;
        }

        .feature-link {
            display: inline-block;
            padding: 10px 25px;
            border-radius: 50px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
            margin-top: 10px;
            color: white;
        }

        .feature-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            color: white;
        }

        .logout-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 1000;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            transform: rotate(360deg);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        /* تم نقل تعديلات الشاشات الصغيرة إلى ملف mobile-styles.css */

        .user-info {
            position: absolute;
            top: 20px;
            left: 20px;
            background-color: white;
            padding: 10px 20px;
            border-radius: 50px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 10px;
            z-index: 1000;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #1565c0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
        }

        .user-name {
            font-weight: 500;
            color: #333;
            margin: 0;
        }

        /* تعديلات معلومات المستخدم للشاشات الصغيرة */
        @media (max-width: 576px) {
            .user-info {
                padding: 5px 10px;
                top: 10px;
                right: 10px;
                position: absolute;
            }

            .user-avatar {
                width: 30px;
                height: 30px;
                font-size: 14px;
            }

            .user-name {
                font-size: 0.8rem;
                max-width: 80px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        /* Animation classes */
        .animate-up {
            animation: fadeInUp 0.5s ease forwards;
        }

        @keyframes fadeInUp {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* إضافات جديدة للترحيب */
        .welcome-badge {
            position: absolute;
            top: -10px;
            right: 40px;
            transform: translateY(0);
            background: linear-gradient(45deg, #1976d2, #64b5f6);
            color: white;
            padding: 10px 25px;
            border-radius: 30px;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(0, 0, 100, 0.2);
            font-size: 1rem;
            z-index: 10;
        }

        .dynamic-icon {
            font-size: 3rem;
            color: #1a237e;
            margin-bottom: 15px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.2);
            }
            100% {
                transform: scale(1);
            }
        }

        .fa-bounce {
            animation: bounce 2s infinite;
        }

        .welcome-actions {
            margin-top: 25px;
        }

        .btn-rounded {
            border-radius: 30px;
            padding: 10px 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-rounded:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        }

        .decoration-circle {
            position: absolute;
            border-radius: 50%;
            opacity: 0.1;
            z-index: 0;
        }

        .circle-1 {
            width: 150px;
            height: 150px;
            background-color: #1976d2;
            top: -40px;
            left: -40px;
        }

        .circle-2 {
            width: 80px;
            height: 80px;
            background-color: #43a047;
            bottom: 20px;
            right: 20px;
        }

        .circle-3 {
            width: 60px;
            height: 60px;
            background-color: #e53935;
            top: 30px;
            right: 35%;
        }

        .welcome-title {
            font-size: 2.8rem;
            color: #1a237e;
            font-weight: bold;
            margin-bottom: 15px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }

        .welcome-subtitle {
            font-size: 1.3rem;
            color: #455a64;
            margin-bottom: 0;
            max-width: 80%;
            margin: 0 auto;
            line-height: 1.7;
            position: relative;
            z-index: 1;
        }

        /* إضافة تنسيقات جديدة للفوتر */
        .footer {
            background-color: rgba(255, 255, 255, 0.8);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }

        .footer p {
            font-size: 0.9rem;
            color: #666;
        }

        /* تنسيقات للإشعارات */
        .message-card {
            position: relative;
            overflow: visible !important;
            cursor: default;
        }

        .notification-badge {
            position: absolute;
            top: -10px;
            right: -10px;
            background-color: #e53935;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
            box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
            z-index: 10;
            animation: pulse-badge 2s infinite;
            opacity: 0;
            transition: opacity 0.3s ease;
            cursor: pointer;
        }

        .notification-badge.show {
            opacity: 1;
        }

        @keyframes pulse-badge {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        .message-popup {
            position: absolute;
            top: 20px;
            right: -290px;
            width: 280px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 25px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            display: none;
            pointer-events: none;
            transition: display 0.3s ease;
        }

        .message-popup.show {
            display: block;
            animation: fadeInNotification 0.5s forwards;
        }

        @keyframes fadeInNotification {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .message-popup-content {
            padding: 15px;
        }

        .message-popup-header {
            display: flex;
            align-items: center;
            color: #e53935;
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .message-popup-body {
            font-size: 0.9rem;
            color: #555;
        }

        /* أنماط الإشعار */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #4CAF50;
            color: white;
            padding: 15px;
            border-radius: 4px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s ease;
            z-index: 1000;
            direction: rtl;
            display: none !important;
        }

        .notification.show {
            opacity: 0 !important;
            transform: translateY(0);
            display: none !important;
        }

        /* تنسيقات أزرار إظهار/إخفاء كلمة المرور */
        .position-relative {
            position: relative;
        }

        /* إضافة padding للمدخلات لتوفير مساحة للأيقونة على اليسار */
        .position-relative input[type="password"],
        .position-relative input[type="text"] {
            padding-left: 35px;
            padding-right: 12px;
            direction: rtl;
        }

        .btn.password-toggle {
            position: absolute;
            left: 10px; /* تم تغيير right إلى left */
            top: 50%;
            transform: translateY(-50%);
            background: transparent;
            border: none;
            color: #6c757d;
            padding: 0;
            font-size: 16px;
            cursor: pointer;
            z-index: 10;
        }

        .btn.password-toggle:hover,
        .btn.password-toggle:focus {
            color: #0d6efd;
            box-shadow: none;
        }

        /* إضافة أسلوب CSS للإشعار في الزاوية العليا */
        .top-corner-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            left: auto;
            z-index: 9999;
            background-color: #4CAF50;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            transform: translateY(-100px);
            opacity: 0;
            transition: all 0.5s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            max-width: 90%;
        }

        .top-corner-notification.show {
            transform: translateY(0);
            opacity: 1;
        }

        .top-corner-notification i {
            margin-left: 10px;
            font-size: 1.2rem;
        }

        /* تحسين شكل الإشعار */
        .notification-badge-pill {
            display: none; /* إخفاء هذا التنسيق لاستخدام التنسيق البسيط */
        }

        @keyframes badge-pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.15); }
            100% { transform: scale(1); }
        }

        /* تأثير وميض قوي عند وصول رسالة جديدة */
        @keyframes badge-flash {
            0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
            100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
        }
    </style>
</head>
<body>
    <!-- User Info -->


    <!-- شريط الأزرار العمودي على اليسار -->
    <div class="vertical-buttons-container">
        <!-- زر الرسائل (الأول في الترتيب العمودي) -->
        <a href="{{ url_for('messages') }}" class="btn btn-warning btn-message" title="الرسائل">
            <i class="fas fa-envelope" style="color: white;"></i>
            <span id="nav-unread-count" class="badge bg-danger" style="position: absolute; top: 0; right: 0; font-size: 0.8rem; border-radius: 50%; width: 18px; height: 18px; display: {{ 'flex' if current_user.get_unread_messages_count() > 0 else 'none' }}; align-items: center; justify-content: center; padding: 0;">
                {{ current_user.get_unread_messages_count() if current_user.get_unread_messages_count() > 0 else '' }}
            </span>
        </a>

        <!-- قائمة الملف الشخصي المنسدلة (الثاني في الترتيب العمودي) -->
        <div class="dropdown">
            <a href="#" class="btn btn-primary btn-profile rounded-circle" id="profileDropdownMobile" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-user"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="profileDropdownMobile">
                <div class="dropdown-header text-center p-3" style="background: linear-gradient(45deg, #1976d2, #64b5f6); color: white;">
                    <div class="dropdown-profile-img mx-auto mb-2" style="width: 70px; height: 70px; border-radius: 50%; overflow: hidden; border: 3px solid white; margin: 0 auto;">
                        {% if current_user.profile_picture %}
                            <img src="{{ current_user.profile_picture }}" alt="{{ current_user.teacher_name }}" class="rounded-circle" style="width: 100%; height: 100%; object-fit: cover;">
                        {% else %}
                            <img src="/static/images/male-profile.png" alt="{{ current_user.teacher_name }}" class="rounded-circle" style="width: 100%; height: 100%; object-fit: cover;">
                        {% endif %}
                    </div>
                    <h6 class="mb-0 fw-bold">{{ current_user.teacher_name }}</h6>
                    <small>{{ current_user.position or 'أستاذ(ة) تعليم متوسط' }}</small>
                </div>
                <div class="dropdown-divider m-0"></div>
                <li><a class="dropdown-item py-2" href="{{ url_for('profile', user_id=current_user.id) }}"><i class="fas fa-user-circle me-2 text-primary"></i> معلومات الأستاذ</a></li>
                <li>
                    <a class="dropdown-item py-2" href="{{ url_for('messages') }}">
                        <i class="fas fa-envelope me-2 text-danger"></i> الرسائل
                        <span id="dropdown-unread-count-mobile" class="badge rounded-pill bg-danger ms-2" style="display: {{ 'inline' if current_user.get_unread_messages_count() > 0 else 'none' }};">
                            {{ current_user.get_unread_messages_count() if current_user.get_unread_messages_count() > 0 else '' }}
                        </span>
                    </a>
                </li>
                <li>
                    <a class="dropdown-item py-2" href="{{ url_for('whatsapp_chat') }}">
                        <i class="fab fa-whatsapp me-2 text-success"></i> الدردشة
                    </a>
                </li>
                <div class="dropdown-divider m-0"></div>
                <li><a class="dropdown-item py-2" href="#" data-bs-toggle="modal" data-bs-target="#changePasswordModal"><i class="fas fa-key me-2 text-warning"></i> تغيير كلمة المرور</a></li>
                <li><a class="dropdown-item py-2" href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt me-2 text-danger"></i> تسجيل الخروج</a></li>
            </ul>
        </div>

        <!-- زر الخروج (الثالث في الترتيب العمودي) -->
        <a href="{{ url_for('logout') }}" class="btn btn-danger btn-logout" title="تسجيل الخروج">
            <i class="fas fa-sign-out-alt"></i>
        </a>
    </div>
    <!-- إضافة عنصر صوت للتنبيه مع مصادر متعددة -->
    <audio id="notification-sound" preload="auto" style="display: none;">
        <source src="{{ url_for('static', filename='sounds/notification.mp3') }}" type="audio/mpeg">
        <source src="https://assets.mixkit.co/active_storage/sfx/2869/2869.wav" type="audio/wav">
    </audio>

    <!-- زر لتفعيل الصوت على الأجهزة المحمولة -->
    <button id="sound-enabler" class="btn btn-sm btn-primary" style="position: fixed; bottom: 20px; right: 20px; z-index: 1000; display: none; border-radius: 50px; padding: 8px 15px;">
        <i class="fas fa-volume-up me-1"></i> تفعيل صوت التنبيهات
    </button>

    <div class="welcome-container">
        <div class="welcome-header animate-up" style="animation-delay: 0.05s; position: relative; background: linear-gradient(120deg, rgba(255, 255, 255, 0.9), rgba(240, 245, 255, 0.9)); padding: 40px; overflow: hidden; margin-top: 20px; margin-bottom: 10px;">
            <div class="dynamic-icon">
                <i class="fas fa-graduation-cap fa-bounce"></i>
            </div>
            <h1 class="welcome-title">مرحباً بك في فضاء أساتذة العلوم الفيزيائية</h1>
            <p class="welcome-subtitle">منصة تعليمية متكاملة تجمع بين إدارة المهام، تنظيم الملفات، والتواصل الفعّال بين الزملاء</p>
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
        </div>

        <style>
            @media (max-width: 768px) {
                .welcome-header {
                    margin-bottom: 5px !important;
                    padding: 30px !important;
                }
                .greeting-bar {
                    margin-top: 0 !important;
                }
            }

            /* زيادة المسافة بين البطاقة الترحيبية والمحتوى عندما يكون الشريط مخفيًا */
            .welcome-header.greeting-hidden {
                margin-bottom: 25px !important;
            }

            @media (max-width: 768px) {
                .welcome-header.greeting-hidden {
                    margin-bottom: 15px !important;
                }
            }
        </style>

        <script>
            // إضافة كلاس للبطاقة الترحيبية عندما يكون الشريط مخفيًا
            document.addEventListener('DOMContentLoaded', function() {
                const greetingBar = document.getElementById('greeting-bar');
                const welcomeHeader = document.querySelector('.welcome-header');

                // التحقق من حالة ظهور الشريط عند تحميل الصفحة
                if (greetingBar && welcomeHeader) {
                    if (window.getComputedStyle(greetingBar).display === 'none') {
                        welcomeHeader.classList.add('greeting-hidden');
                    } else {
                        welcomeHeader.classList.remove('greeting-hidden');
                    }
                }

                // إضافة مستمع للتغييرات في حالة ظهور الشريط (للمدراء فقط)
                const visibilityToggle = document.getElementById('greeting-visibility-toggle');
                if (visibilityToggle) {
                    visibilityToggle.addEventListener('change', function() {
                        if (!this.checked) {
                            welcomeHeader.classList.add('greeting-hidden');
                        } else {
                            welcomeHeader.classList.remove('greeting-hidden');
                        }
                    });
                }
            });
        </script>

        <!-- شريط السلام عليكم -->
        <div class="greeting-bar animate-up" id="greeting-bar" style="animation-delay: 0.1s; background: linear-gradient(to right, #1976d2, #64b5f6); color: white; padding: 4px 15px; border-radius: 6px; margin-top: 0; margin-bottom: {% if current_user.is_admin %}2px{% else %}15px{% endif %}; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); position: relative; overflow: hidden; width: 100%; {% if not greeting_visible %}display: none !important;{% endif %}">
            <div style="display: flex; align-items: center; justify-content: flex-start; position: relative; padding: 0 10px;">
                <!-- أيقونة جديد -->
                <div class="new-badge" style="background-color: #ff5252; color: white; font-size: 0.7rem; font-weight: bold; padding: 2px 6px; border-radius: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.2); display: flex; align-items: center; z-index: 2;">
                    <i class="fas fa-star fa-pulse" style="margin-left: 2px; font-size: 0.6rem;"></i>جديد
                </div>

                <!-- النص المتحرك على يمين الأيقونة -->
                <div class="welcome-text mb-0" style="font-size: 0.95rem; font-weight: 400; color: #1a1a1a; line-height: 1.4; text-align: right; text-shadow: 0 1px 2px rgba(255, 255, 0, 0.3); margin-right: 10px;">
                </div>
            </div>

            <div class="decoration-circle" style="position: absolute; width: 20px; height: 20px; background-color: rgba(255, 255, 255, 0.1); border-radius: 50%; top: -8px; right: 10%; z-index: 0;"></div>
            <div class="decoration-circle" style="position: absolute; width: 15px; height: 15px; background-color: rgba(255, 255, 255, 0.1); border-radius: 50%; bottom: -5px; left: 20%; z-index: 0;"></div>
        </div>

        <style>
            .welcome-text {
                display: flex;
                justify-content: center;
                flex-wrap: wrap;
                gap: 5px;
            }

            .animated-word {
                display: inline-block;
                opacity: 0;
                animation: word-animation 3s ease-in-out infinite;
            }

            @keyframes word-animation {
                0%, 5% {
                    transform: translateY(10px);
                    opacity: 0;
                }
                10%, 80% {
                    transform: translateY(0);
                    opacity: 1;
                }
                90%, 100% {
                    transform: translateY(-5px);
                    opacity: 0;
                }
            }

            /* تأخير الحركة لكل كلمة */
            .animated-word:nth-child(1) { animation-delay: 0.0s; }
            .animated-word:nth-child(2) { animation-delay: 0.2s; }
            .animated-word:nth-child(3) { animation-delay: 0.4s; }
            .animated-word:nth-child(4) { animation-delay: 0.6s; }
            .animated-word:nth-child(5) { animation-delay: 0.8s; }
            .animated-word:nth-child(6) { animation-delay: 1.0s; }
            .animated-word:nth-child(7) { animation-delay: 1.2s; }
            .animated-word:nth-child(8) { animation-delay: 1.4s; }
            .animated-word:nth-child(9) { animation-delay: 1.6s; }
            .animated-word:nth-child(10) { animation-delay: 1.8s; }

            @keyframes pulse {
                0% {
                    opacity: 1;
                }
                50% {
                    opacity: 0.6;
                }
                100% {
                    opacity: 1;
                }
            }

            .fa-pulse {
                animation: pulse 1s infinite;
            }

            /* تعديلات للشاشات الصغيرة */
            @media (max-width: 768px) {
                .new-badge {
                    font-size: 0.65rem;
                    padding: 1px 5px;
                }

                .greeting-bar p {
                    font-size: 0.8rem !important;
                }
            }
        </style>

        <!-- أزرار التحكم بالشريط (تظهر فقط للمدير والمشرفين) -->
        {% if current_user.is_admin %}
        <div class="d-flex justify-content-end mb-1 animate-up greeting-control-buttons" style="animation-delay: 0.15s; margin-top: 1px;">
            <div style="display: flex; align-items: center; gap: 5px;">
                <button id="edit-greeting-btn" class="btn btn-sm btn-light" style="border-radius: 50%; width: 22px; height: 22px; padding: 0; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); transition: all 0.3s ease; display: flex; align-items: center; justify-content: center;" title="تعديل عبارة الترحيب">
                    <i class="fas fa-edit" style="font-size: 0.7rem;"></i>
                </button>

                <div class="form-check form-switch" style="margin: 0; height: 22px; display: flex; align-items: center;">
                    <input class="form-check-input" type="checkbox" id="greeting-visibility-toggle" style="width: 1.8rem; height: 0.9rem; cursor: pointer; margin: 0;" {% if greeting_visible %}checked{% endif %} title="إظهار/إخفاء شريط الترحيب">
                </div>
            </div>
        </div>
        {% endif %}

        <!-- مودال تعديل العبارة (متاح فقط للإدارة) -->
        {% if current_user.is_admin %}
        <div class="modal fade" id="editGreetingModal" tabindex="-1" aria-labelledby="editGreetingModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header" style="background: linear-gradient(to right, #1976d2, #64b5f6); color: white;">
                        <h5 class="modal-title" id="editGreetingModalLabel">تعديل عبارة الترحيب</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="greeting-text" class="form-label">أدخل العبارة الجديدة:</label>
                            <input type="text" class="form-control" id="greeting-text" value="السلام عليكم و رحمة الله و بركاته">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" id="save-greeting-btn">حفظ التغييرات</button>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="row g-4">
            <!-- جدول التوقيت -->
            <div class="col-sm-12 col-md-6 col-lg-4 animate-up" style="animation-delay: 0.1s;">
                <div class="feature-card">
                    <div class="feature-icon" style="background: linear-gradient(45deg, #1976d2, #64b5f6);">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h3 class="feature-title">جدول التوقيت</h3>
                    <p class="feature-description">
                        قم بإدارة جدول حصصك الأسبوعي بسهولة. إضافة وتعديل الحصص، وتتبع الأقسام المسندة إليك.
                    </p>
                    <a href="{{ url_for('dashboard') }}" class="feature-link btn btn-primary">
                        الذهاب للجدول <i class="fas fa-arrow-left me-1"></i>
                    </a>
                </div>
            </div>

            <!-- الملفات التعليمية -->
            <div class="col-sm-12 col-md-6 col-lg-4 animate-up" style="animation-delay: 0.2s;">
                <div class="feature-card">
                    <div class="feature-icon" style="background: linear-gradient(45deg, #F78181, #F5A9A9);">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3 class="feature-title">الملفات التعليمية</h3>
                    <p class="feature-description">
                        رفع وتنظيم الملفات التعليمية ومشاركتها مع زملائك. دعم لمختلف أنواع الملفات بما في ذلك PDF و Word.
                    </p>
                    <a href="{{ url_for('educational_files') }}" class="feature-link btn" style="background-color: #F78181; border-color: #F78181; color: white;">
                        استعراض الملفات <i class="fas fa-arrow-left me-1"></i>
                    </a>
                </div>
            </div>

            <!-- البرنامج التعليمي -->
            <div class="col-sm-12 col-md-6 col-lg-4 animate-up" style="animation-delay: 0.3s;">
                <div class="feature-card">
                    <div class="feature-icon" style="background: linear-gradient(45deg, #fb8c00, #ffb74d);">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="feature-title">تقدم البرنامج</h3>
                    <p class="feature-description">
                        سجل وتتبع تقدمك في البرنامج التعليمي. تدوين وتنظيم الحصص التعليمية وفق التدرج السنوي.
                    </p>
                    <a href="{{ url_for('progress_page') }}" class="feature-link btn" style="background-color: #fb8c00; border-color: #fb8c00;">
                        عرض التقدم <i class="fas fa-arrow-left me-1"></i>
                    </a>
                </div>
            </div>

            <!-- الواتساب -->
            <div class="col-sm-12 col-md-6 col-lg-4 animate-up" style="animation-delay: 0.4s;">
                <div class="feature-card whatsapp-card" style="cursor: default;">
                    <div class="feature-icon" style="background: linear-gradient(45deg, rgb(37, 211, 102), rgb(18, 140, 66));">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <h3 class="feature-title">الدردشة</h3>
                    <p class="feature-description">
                        تواصل مع زملائك بشكل فوري عبر نظام الدردشة المباشرة. إرسال واستقبال الملفات و الرسائل النصية بشكل سريع وسهل.
                    </p>
                    <a href="{{ url_for('whatsapp_chat') }}" class="feature-link btn" style="background-color: rgb(37, 211, 102); border-color: rgb(37, 211, 102); color: white;">
                        فتح الدردشة <i class="fas fa-arrow-left me-1"></i>
                    </a>
                </div>
            </div>

            <!-- انشغالات الأساتذة -->
            <div class="col-sm-12 col-md-6 col-lg-4 animate-up" style="animation-delay: 0.5s;">
                <div class="feature-card">
                    <div class="feature-icon" style="background: linear-gradient(45deg, #00897b, #4db6ac);">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h3 class="feature-title">انشغالات الأساتذة</h3>
                    <p class="feature-description">
                        اطلع على انشغالات الأساتذة وشارك انشغالك. منصة للتواصل وطرح الأفكار والمقترحات لتحسين العملية التعليمية.
                    </p>
                    <a href="{{ url_for('concerns_page') }}" class="feature-link btn" style="background-color: #00897b; border-color: #00897b; color: white;">
                        عرض الانشغالات <i class="fas fa-arrow-left me-1"></i>
                    </a>
                </div>
            </div>

            <!-- الرسائل للأعضاء العاديين -->
            <div class="col-sm-12 col-md-6 col-lg-4 animate-up" style="animation-delay: 0.6s;">
                <div class="feature-card message-card" id="message-card-regular" style="cursor: default;">
                    <div class="notification-badge" id="message-notification-badge-regular">0</div>
                    <div class="feature-icon" style="background: linear-gradient(45deg, #e53935, #ef5350);">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h3 class="feature-title">الرسائل</h3>
                    <p class="feature-description">
                        تواصل مع مفتش المادة عبر نظام الرسائل المدمج. إرسال واستقبال الرسائل والمرفقات بما في ذلك PDF و Word.
                    </p>
                    <a href="{{ url_for('messages') }}" class="feature-link btn btn-danger">
                        صندوق الرسائل <i class="fas fa-arrow-left me-1"></i>
                    </a>
                    <div class="message-popup" id="message-popup-regular">
                        <div class="message-popup-content">
                            <div class="message-popup-header">
                                <i class="fas fa-envelope-open me-2"></i>
                                <span id="popup-message-count-regular">رسالة جديدة</span>
                            </div>
                            <div class="message-popup-body">
                                لديك رسالة جديدة من <span id="sender-name-regular">{{ sender_name }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الملف الشخصي -->
            <div class="col-sm-12 col-md-6 col-lg-4 animate-up" style="animation-delay: 0.7s;">
                <div class="feature-card">
                    <div class="feature-icon" style="background: linear-gradient(45deg, #7b1fa2, #ba68c8);">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3 class="feature-title">الملف الشخصي</h3>
                    <p class="feature-description">
                        إدارة معلوماتك الشخصية والمهنية. تحديث صورتك الشخصية وبياناتك التعريفية.
                    </p>
                    <a href="{{ url_for('profile', user_id=current_user.id) }}" class="feature-link btn" style="background-color: #7b1fa2; border-color: #7b1fa2;">
                        عرض الملف <i class="fas fa-arrow-left me-1"></i>
                    </a>
                </div>
            </div>

            <!-- التوقيت الأسبوعي للأستاذ(ة) - ظاهرة فقط للمدير والمشرفين -->
            {% if current_user.is_admin %}
            <!-- لوحة المشرف -->
            <div class="col-sm-12 col-md-6 col-lg-4 animate-up" style="animation-delay: 0.8s;">
                <div class="feature-card">
                    <div class="feature-icon" style="background: linear-gradient(45deg, #0097a7, #4dd0e1);">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <h3 class="feature-title">لوحة المشرف</h3>
                    <p class="feature-description">
                        إدارة حسابات المستخدمين والإشراف على النظام. متاح فقط للمشرفين ومدير النظام.
                    </p>
                    <a href="{{ url_for('admin_panel') }}" class="feature-link btn" style="background-color: #0097a7; border-color: #0097a7; color: white;">
                        لوحة التحكم <i class="fas fa-arrow-left me-1"></i>
                    </a>
                </div>
            </div>

            <!-- التوقيت الأسبوعي للأستاذ(ة) -->
            <div class="col-sm-12 col-md-6 col-lg-4 animate-up" style="animation-delay: 0.85s;">
                <div class="feature-card">
                    <div class="feature-icon" style="background: linear-gradient(45deg, #e91e63, #f48fb1);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="feature-title">التوقيت الأسبوعي للأستاذ(ة)</h3>
                    <p class="feature-description">
                        عرض جداول التوقيت الأسبوعية لجميع الأساتذة، الأقسام المسندة، وساعات العمل.
                    </p>
                    <a href="{{ url_for('teacher_schedules') }}" class="feature-link btn" style="background-color: #e91e63; border-color: #e91e63; color: white;">
                        استعراض الجداول <i class="fas fa-arrow-left me-1"></i>
                    </a>
                </div>
            </div>

            <!-- مدى التقدم -->
            <div class="col-sm-12 col-md-6 col-lg-4 animate-up" style="animation-delay: 0.9s;">
                <div class="feature-card">
                    <div class="feature-icon" style="background: linear-gradient(45deg, #5e35b1, #9575cd);">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h3 class="feature-title">مدى التقدم</h3>
                    <p class="feature-description">
                        متابعة تقدم كافة الأساتذة في البرنامج التعليمي، ومعرفة مدى تقدمهم أو تأخرهم حسب التدرج السنوي.
                    </p>
                    <a href="{{ url_for('progress_dashboard') }}" class="feature-link btn" style="background-color: #5e35b1; border-color: #5e35b1; color: white;">
                        عرض التقدم <i class="fas fa-arrow-left me-1"></i>
                    </a>
                </div>
            </div>

            <!-- إدارة انشغالات الأساتذة -->
            <div class="col-sm-12 col-md-6 col-lg-4 animate-up" style="animation-delay: 0.95s;">
                <div class="feature-card">
                    <div class="feature-icon" style="background: linear-gradient(45deg, #3949ab, #7986cb);">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <h3 class="feature-title">إدارة انشغالات الأساتذة</h3>
                    <p class="feature-description">
                        مراجعة وإدارة انشغالات الأساتذة. الموافقة على الانشغالات المقدمة أو رفضها مع إضافة تعليقات توضيحية.
                    </p>
                    <a href="{{ url_for('admin_concerns') }}" class="feature-link btn" style="background-color: #3949ab; border-color: #3949ab; color: white;">
                        إدارة الانشغالات <i class="fas fa-arrow-left me-1"></i>
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <footer class="footer mt-5 py-3 text-center">
        <div class="container">
            <p class="mb-0 text-muted" style="white-space: nowrap; font-size: 0.9rem;">
                جميع الحقوق محفوظة &copy; <span id="current-year"></span> - فضاء أساتذة العلوم الفيزيائية
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add animation delays to cards based on their position
            const cards = document.querySelectorAll('.animate-up');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${(index * 0.1) + 0.1}s`;
            });

            // تحديث السنة الحالية تلقائياً
            document.getElementById('current-year').textContent = new Date().getFullYear();

            // تهيئة نظام التنبيهات
            setupNotifications();

            // التحقق من الرسائل الجديدة عند تحميل الصفحة
            checkForNewMessages();

            // التحقق من الرسائل الجديدة كل 10 ثوانٍ
            setInterval(checkForNewMessages, 10000);

            // إضافة مستمعي الأحداث لأزرار إظهار/إخفاء كلمة المرور
            document.querySelectorAll('.password-toggle').forEach(button => {
                button.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-target');
                    const passwordInput = document.getElementById(targetId);
                    const icon = this.querySelector('i');

                    if (passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    } else {
                        passwordInput.type = 'password';
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    }
                });
            });

            // إضافة مستمع لزر حفظ تغييرات كلمة المرور
            const savePasswordBtn = document.getElementById('save-password-btn');
            if (savePasswordBtn) {
                savePasswordBtn.addEventListener('click', function() {
                    const form = document.getElementById('change-password-form');
                    const newPassword = document.getElementById('new_password').value;

                    // إعداد البيانات بصيغة JSON
                    const data = {
                        new_password: newPassword,
                        csrf_token: document.querySelector('input[name="csrf_token"]').value
                    };

                    // إرسال البيانات باستخدام fetch API
                    fetch('{{ url_for("update_password_direct") }}', {
                        method: 'POST',
                        body: JSON.stringify(data),
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('استجابة الخادم:', data);

                        if (data.success === true || data.status === 'success') {
                            // إظهار رسالة نجاح في الزاوية العليا
                            const notification = document.getElementById('success-notification');
                            notification.classList.add('show');

                            // إعادة تعيين حقل كلمة المرور الجديدة
                            document.getElementById('new_password').value = '';

                            // إغلاق المودال
                            const modal = bootstrap.Modal.getInstance(document.getElementById('changePasswordModal'));
                            modal.hide();

                            // إخفاء الإشعار بعد 3 ثوان
                            setTimeout(() => {
                                notification.classList.remove('show');
                            }, 3000);
                        } else {
                            // إظهار رسالة الخطأ
                            alert(data.message || 'حدث خطأ أثناء تغيير كلمة المرور');
                        }
                    })
                    .catch(error => {
                        console.error('خطأ في إرسال البيانات:', error);
                        alert('حدث خطأ أثناء الاتصال بالخادم');
                    });
                });
            }

            // فحص الإشعارات ووصول رسائل جديدة
            function updateNavUnreadCount() {
                fetch('{{ url_for("get_unread_count") }}')
                    .then(response => response.json())
                    .then(data => {
                        const badge = document.getElementById('nav-unread-count');
                        if (!badge) return;

                        // تخزين القيمة القديمة لعدد الرسائل
                        const oldCount = window.lastNavCount || 0;

                        if (data.count > 0) {
                            // تحديث عدد الرسائل
                            badge.textContent = data.count;
                            badge.style.display = 'flex';

                            // التحقق مما إذا كان هناك رسائل جديدة (زيادة في العدد)
                            if (data.count > oldCount) {
                                console.log('تم استلام رسائل جديدة! العدد القديم:', oldCount, 'العدد الجديد:', data.count);

                                // تشغيل الصوت فوراً عند وصول رسالة جديدة
                                playNotificationSound();
                            }
                        } else {
                            badge.style.display = 'none';
                        }

                        // تحديث العداد المخزن
                        window.lastNavCount = data.count;
                    })
                    .catch(error => {
                        console.error('خطأ في التحقق من الرسائل:', error);
                    });
            }

            // متغير للتحقق من تفعيل الصوت
            let isSoundEnabled = false;

            // دالة لتفعيل الصوت على الأجهزة المحمولة
            function enableSound() {
                const audio = document.getElementById('notification-sound');
                if (!audio) return;

                // محاولة تشغيل الصوت بصوت منخفض جدًا لتفعيله
                audio.volume = 0.01;
                audio.play().then(() => {
                    audio.pause();
                    audio.currentTime = 0;
                    audio.volume = 1.0;
                    isSoundEnabled = true;
                    console.log('تم تفعيل الصوت بنجاح');

                    // تخزين حالة تفعيل الصوت
                    localStorage.setItem('soundEnabled', 'true');

                    // إظهار إشعار للمستخدم
                    showSoundEnabledNotification();
                }).catch(error => {
                    console.log('فشل تفعيل الصوت:', error);
                });
            }

            // دالة لإظهار إشعار تفعيل الصوت
            function showSoundEnabledNotification() {
                // إنشاء عنصر الإشعار
                const notification = document.createElement('div');
                notification.className = 'alert alert-success alert-dismissible fade show';
                notification.style.position = 'fixed';
                notification.style.top = '10px';
                notification.style.left = '50%';
                notification.style.transform = 'translateX(-50%)';
                notification.style.zIndex = '9999';
                notification.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
                notification.innerHTML = `
                    تم تفعيل صوت التنبيهات
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;

                // إضافة الإشعار للصفحة
                document.body.appendChild(notification);

                // إزالة الإشعار بعد 3 ثوانٍ
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => notification.remove(), 300);
                }, 3000);
            }

            // دالة منفصلة لتشغيل صوت التنبيه
            function playNotificationSound() {
                const audio = document.getElementById('notification-sound');
                if (!audio) {
                    console.log('عنصر الصوت غير موجود!');
                    return;
                }

                // التحقق من دعم الجهاز للاهتزاز
                if ('vibrate' in navigator) {
                    // اهتزاز الجهاز عند وصول إشعار (للهواتف المحمولة)
                    navigator.vibrate([100, 50, 100]);
                }

                // إيقاف الصوت إذا كان قيد التشغيل
                audio.pause();
                // إعادة تعيين الصوت للبداية
                audio.currentTime = 0;

                // محاولة تشغيل الصوت بشكل متكرر إذا فشل
                const attemptPlay = function(retryCount = 3) {
                    // التحقق من تفعيل الصوت أولاً
                    if (!isSoundEnabled && /Android|iPhone|iPad|iPod|Mobile/i.test(navigator.userAgent)) {
                        console.log('الصوت غير مفعل على الجهاز المحمول');
                        return;
                    }

                    const playPromise = audio.play();

                    if (playPromise !== undefined) {
                        playPromise.then(_ => {
                            console.log('تم تشغيل صوت التنبيه بنجاح');
                        }).catch(error => {
                            console.log('تم منع تشغيل الصوت:', error);
                            if (retryCount > 0) {
                                // محاولة التشغيل مرة أخرى بعد فترة قصيرة
                                setTimeout(() => attemptPlay(retryCount - 1), 300);
                            }
                        });
                    }
                };

                // بدء المحاولة الأولى
                attemptPlay();
            }

            // تحديث العدد كل 5 ثواني
            setInterval(updateNavUnreadCount, 5000);
            updateNavUnreadCount(); // تحديث فوري عند تحميل الصفحة

            // التحقق من الجهاز وتفعيل الصوت
            document.addEventListener('DOMContentLoaded', function() {
                // التحقق مما إذا كان الجهاز محمولاً
                const isMobile = /Android|iPhone|iPad|iPod|Mobile/i.test(navigator.userAgent);

                if (isMobile) {
                    // التحقق من حالة تفعيل الصوت المخزنة
                    const soundEnabled = localStorage.getItem('soundEnabled') === 'true';

                    if (soundEnabled) {
                        // محاولة تفعيل الصوت تلقائيًا
                        setTimeout(enableSound, 1000);
                    } else {
                        // إظهار زر تفعيل الصوت
                        const soundEnabler = document.getElementById('sound-enabler');
                        if (soundEnabler) {
                            soundEnabler.style.display = 'block';

                            // إضافة مستمع حدث للزر
                            soundEnabler.addEventListener('click', function() {
                                enableSound();
                                // إخفاء الزر بعد التفعيل
                                this.style.display = 'none';
                            });
                        }
                    }
                } else {
                    // على أجهزة الكمبيوتر، نفترض أن الصوت مفعل
                    isSoundEnabled = true;
                }

                // تفعيل الصوت عند أي تفاعل مع الصفحة (للهواتف المحمولة)
                if (isMobile) {
                    const activateOnInteraction = function() {
                        if (!isSoundEnabled) {
                            enableSound();
                        }
                        // إزالة المستمعات بعد التفعيل
                        document.removeEventListener('click', activateOnInteraction);
                        document.removeEventListener('touchstart', activateOnInteraction);
                    };

                    // إضافة مستمعات للتفاعل
                    document.addEventListener('click', activateOnInteraction);
                    document.addEventListener('touchstart', activateOnInteraction);
                }
            });

            // التأكد من تحميل صوت التنبيه
            const soundPath = "{{ url_for('static', filename='sounds/notification.mp3') }}";
            const notificationSound = new Audio(soundPath);

            // معالجة خطأ تحميل الصوت
            notificationSound.addEventListener('error', function(e) {
                console.error('خطأ في تحميل ملف الصوت:', e);
            });

            // تحديث عداد الرسائل في القائمة المنسدلة
            function updateUnreadCountInDropdown(count) {
                const dropdownCounter = document.getElementById('dropdown-unread-count-mobile');
                if (dropdownCounter) {
                    dropdownCounter.textContent = count > 0 ? count : '';
                    dropdownCounter.style.display = count > 0 ? 'inline' : 'none';
                }
            }

            // تعديل دالة التحقق من الرسائل لتحديث العداد في القائمة المنسدلة
            function checkForNewMessages() {
                fetch('/check_messages')
                    .then(response => response.json())
                    .then(data => {
                        console.log('بيانات الرسائل:', data);
                        const unreadCount = data.count;
                        const senderName = data.sender || "شخص ما";

                        // تحديث عداد الرسائل في الواجهة
                        const messageCounter = document.getElementById('message-counter');
                        if (messageCounter) {
                            messageCounter.textContent = unreadCount > 0 ? unreadCount : '';
                            messageCounter.style.display = unreadCount > 0 ? 'inline-block' : 'none';
                        }

                        // تحديث عداد الرسائل في القائمة المنسدلة
                        updateUnreadCountInDropdown(unreadCount);

                        // إذا كان هناك رسائل جديدة وكان هناك زيادة في العدد
                        if (unreadCount > lastUnreadCount && unreadCount > 0) {
                            // تشغيل صوت الإشعار
                            try {
                                notificationSound.play().catch(err => {
                                    console.warn('لم يتم تشغيل الصوت:', err);
                                });
                            } catch (e) {
                                console.error('خطأ في تشغيل الصوت:', e);
                            }

                            // عرض إشعار - تم تعطيل هذه الميزة
                            // showNotification(senderName);
                        }

                        // تحديث العداد السابق
                        lastUnreadCount = unreadCount;
                    })
                    .catch(error => {
                        console.error('خطأ في التحقق من الرسائل الجديدة:', error);
                    });
            }

            // وظيفة لعرض الإشعار
            function showNotification(senderName) {
                // تم تعطيل وظيفة عرض الإشعار عند وصول رسائل جديدة
                // const notification = document.getElementById('notification');
                // const notificationText = document.getElementById('notification-text');

                // if (notification && notificationText) {
                //     notificationText.textContent = `لديك رسالة جديدة من ${senderName}`;
                //     notification.classList.add('show');

                //     // إخفاء الإشعار بعد 5 ثوانٍ
                //     setTimeout(() => {
                //         notification.classList.remove('show');
                //     }, 5000);
                // }
            }

            // متغير لتخزين عدد الرسائل غير المقروءة في الفحص السابق
            let lastUnreadCount = 0;
        });

        // نظام التنبيهات للرسائل
        function setupNotifications() {
            console.log('تم استدعاء نظام التنبيهات');

            // إعداد التنبيه الصوتي مع التحقق من وجود الملف
            const notificationSound = new Audio();

            // التحقق من وجود ملف الصوت وإضافة معالجة للأخطاء
            notificationSound.addEventListener('error', function(e) {
                console.error('خطأ في تحميل ملف الصوت:', e);
                // الاستمرار في العمل حتى لو كان هناك خطأ في ملف الصوت
            });

            // محاولة تحميل ملف الصوت
            try {
                // استخدام مسار قياسي متوفر في معظم المتصفحات
                // استخدام مسار متعدد للمحاولة أكثر من مرة
                const audioSources = [
                    // مسار مخصص - يمكن للمستخدم إضافة ملف الصوت هنا لاحقًا
                    "/static/sounds/notification.mp3",
                    // مسار بديل احتياطي
                    "https://assets.mixkit.co/active_storage/sfx/2869/2869.wav"
                ];

                // محاولة تحميل الملف الأول
                notificationSound.src = audioSources[0];

                // إضافة مستمع لحدث الخطأ للمحاولة مع المصدر البديل
                notificationSound.addEventListener('error', function() {
                    console.log('محاولة استخدام المصدر البديل للصوت');
                    notificationSound.src = audioSources[1];
                });
            } catch (e) {
                console.error('خطأ في تعيين مصدر الصوت:', e);
            }

            // إعداد بطاقة الرسائل للأعضاء
            setupMessageCard(
                document.getElementById('message-card-regular'),
                document.getElementById('message-popup-regular'),
                document.getElementById('message-notification-badge-regular'),
                document.getElementById('popup-message-count-regular'),
                document.getElementById('sender-name-regular')
            );
        }

        // دالة لإعداد بطاقة الرسائل
        function setupMessageCard(messageCard, messagePopup, badge, popupMessageCount, senderName) {
            // التحقق من وجود العناصر قبل المتابعة
            if (messageCard && messagePopup && badge && popupMessageCount && senderName) {
                console.log('تم العثور على كافة العناصر بنجاح');

                // متغيرات للتحكم بحالة الرسائل
                let unreadMessages = 0;
                let lastSender = "";

                // دالة لإظهار الإشعار عند وجود رسائل
                function showNotificationBadge() {
                    if (unreadMessages > 0) {
                        badge.textContent = unreadMessages;
                        badge.classList.add('show');
                    } else {
                        badge.classList.remove('show');
                    }
                }

                // عند وضع المؤشر فوق شارة الإشعار (الرقم) فقط
                badge.addEventListener('mouseenter', function() {
                    if (unreadMessages > 0) {
                        // تحديث نص الإشعار حسب عدد الرسائل
                        if (unreadMessages === 1) {
                            popupMessageCount.textContent = "رسالة جديدة";
                            senderName.textContent = lastSender;
                        } else {
                            popupMessageCount.textContent = unreadMessages + " رسائل جديدة";
                            senderName.textContent = "عدة أشخاص";
                        }

                        // إظهار الإشعار المنبثق
                        messagePopup.classList.add('show');
                    }
                });

                // عند إزالة المؤشر من شارة الإشعار
                badge.addEventListener('mouseleave', function() {
                    messagePopup.classList.remove('show');
                });

                // عند النقر على بطاقة الرسائل
                messageCard.addEventListener('click', function() {
                    window.location.href = "{{ url_for('messages') }}";
                });

                // تنفيذ الدالة لإظهار/إخفاء الإشعار في البداية
                showNotificationBadge();

                // دالة للتحقق من وجود رسائل جديدة
                function checkMessages() {
                    // في التطبيق الحقيقي سيتم استبدال هذا بطلب AJAX
                    try {
                        console.log('جاري التحقق من وجود رسائل جديدة...');
                        fetch("{{ url_for('check_messages') }}")
                        .then(response => response.json())
                        .then(data => {
                            console.log('تم استلام بيانات الرسائل:', data);
                            if (data.count > 0) {
                                updateNotification(data.count, data.sender);
                            }
                        })
                        .catch(error => {
                            console.error('خطأ في جلب بيانات الرسائل:', error);
                        });
                    } catch (e) {
                        console.error('خطأ في إرسال طلب التحقق من الرسائل:', e);
                    }
                }

                // دالة لتحديث الإشعار
                function updateNotification(count, sender) {
                    // تحديث المتغيرات
                    unreadMessages = count;
                    lastSender = sender || "أحد المستخدمين";

                    // تحديث الإشعار
                    showNotificationBadge();
                }

                // فحص الرسائل عند تحميل الصفحة مباشرة
                checkMessages();

                // ضبط فحص دوري للرسائل كل 10 ثوانٍ للحصول على التحديثات
                const messageCheckInterval = setInterval(checkMessages, 10000);

                // محاكاة استلام رسالة في حالة وجود رسائل غير مقروءة
                try {
                    const hasUnreadMessages = {{ current_user.get_unread_messages_count() if current_user.is_authenticated else 0 }};
                    console.log('عدد الرسائل غير المقروءة عند التحميل:', hasUnreadMessages);
                    if (hasUnreadMessages > 0) {
                        // جلب آخر رسالة غير مقروءة لعرض اسم المرسل
                        fetch("{{ url_for('check_messages') }}")
                        .then(response => response.json())
                        .then(data => {
                            updateNotification(data.count, data.sender);
                        })
                        .catch(error => {
                            console.error('خطأ في جلب بيانات الرسائل:', error);
                            // إذا فشل الطلب، نستخدم الاسم الافتراضي
                            updateNotification(hasUnreadMessages, "أحد المستخدمين");
                        });
                    }
                } catch (e) {
                    console.error('خطأ في التحقق من الرسائل غير المقروءة:', e);
                }

                // دالة لتحديث حالة الإشعارات عند وصول رسائل جديدة
                function updateNotification(count, sender) {
                    console.log('تحديث الإشعار: العدد=' + count + ', المرسل=' + sender);
                    unreadMessages = count;
                    lastSender = sender || "أحد الزملاء";

                    // تحديث شارة الإشعار
                    showNotificationBadge();

                    // تشغيل التنبيه الصوتي عند وصول رسائل جديدة
                    if (count > 0) {
                        try {
                            // التحقق أولاً إذا كان الصوت جاهزاً للتشغيل
                            if (notificationSound.readyState >= 2) {
                                notificationSound.play()
                                .catch(e => {
                                    console.error('خطأ في تشغيل الصوت:', e);
                                    // الاستمرار في العمل حتى لو فشل تشغيل الصوت
                                });
                            }
                        } catch (e) {
                            console.error('خطأ عام في تشغيل الصوت:', e);
                        }
                    }
                }

                // دالة للتحقق من وجود رسائل جديدة
                function checkMessages() {
                    // في التطبيق الحقيقي سيتم استبدال هذا بطلب AJAX
                    try {
                        console.log('جاري التحقق من وجود رسائل جديدة...');
                        fetch("{{ url_for('check_messages') }}")
                        .then(response => response.json())
                        .then(data => {
                            console.log('تم استلام بيانات الرسائل:', data);
                            if (data.count > 0) {
                                updateNotification(data.count, data.sender);
                            }
                        })
                        .catch(error => {
                            console.error('خطأ في جلب بيانات الرسائل:', error);
                        });
                    } catch (e) {
                        console.error('خطأ في إرسال طلب التحقق من الرسائل:', e);
                    }
                }

                // فحص الرسائل عند تحميل الصفحة مباشرة
                checkMessages();

                // ضبط فحص دوري للرسائل كل 10 ثوانٍ للحصول على التحديثات
                const messageCheckInterval = setInterval(checkMessages, 10000);

                // محاكاة استلام رسالة في حالة وجود رسائل غير مقروءة
                try {
                    const hasUnreadMessages = {{ current_user.get_unread_messages_count() if current_user.is_authenticated else 0 }};
                    console.log('عدد الرسائل غير المقروءة عند التحميل:', hasUnreadMessages);
                    if (hasUnreadMessages > 0) {
                        // جلب آخر رسالة غير مقروءة لعرض اسم المرسل
                        fetch("{{ url_for('check_messages') }}")
                        .then(response => response.json())
                        .then(data => {
                            updateNotification(data.count, data.sender);
                        })
                        .catch(error => {
                            console.error('خطأ في جلب بيانات الرسائل:', error);
                            // إذا فشل الطلب، نستخدم الاسم الافتراضي
                            updateNotification(hasUnreadMessages, "أحد المستخدمين");
                        });
                    }
                } catch (e) {
                    console.error('خطأ في التحقق من الرسائل غير المقروءة:', e);
                }
            } else {
                console.error('لم يتم العثور على أحد العناصر المطلوبة');
            }
        }

        // تم نقل كود التحكم بشريط السلام عليكم إلى ملف منفصل (greeting-bar.js)
    </script>

    <!-- إضافة عنصر الإشعار إذا لم يكن موجوداً -->
    <div id="notification" class="notification" style="display: none !important;">
        <div id="notification-text">لديك رسالة جديدة</div>
    </div>

    <!-- مودال تغيير كلمة المرور -->
    <div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="changePasswordModalLabel">تغيير كلمة المرور</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="change-password-form">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                        <div class="mb-3">
                            <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                            <div class="position-relative">
                                <input type="password" class="form-control" id="current_password" name="current_password" value="••••••••" readonly="">
                                <button type="button" class="btn password-toggle" data-target="current_password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                            <div class="position-relative">
                                <input type="password" class="form-control" id="new_password" name="new_password" placeholder="اترك هذا الحقل فارغاً إذا كنت لا ترغب في تغيير كلمة المرور">
                                <button type="button" class="btn password-toggle" data-target="new_password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="save-password-btn">حفظ التغييرات</button>
                </div>
            </div>
        </div>
    </div>

    <!-- إشعار النجاح في الزاوية العليا -->
    <div id="success-notification" class="top-corner-notification">
        <i class="fas fa-check-circle"></i>
        <span>تم حفظ التغييرات بنجاح</span>
    </div>

    <!-- إضافة ملف JavaScript الخاص بشريط السلام عليكم -->
    <script src="{{ url_for('static', filename='js/greeting-bar.js') }}"></script>
</body>
</html>