{% extends "base.html" %}
{% block content %}
<div class="container mt-4">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" id="csrf-token">

    <div class="card mb-4 border-0 shadow-sm bg-gradient">
        <div class="card-header bg-primary text-white py-3 d-flex align-items-center justify-content-between">
            <div class="d-flex align-items-center">
                <i class="fas fa-calendar-alt me-2 fa-lg"></i>
                <h5 class="mb-0">التوقيت الأسبوعي للأستاذ(ة)</h5>
            </div>
            <span class="badge bg-light text-primary">{{ teachers|length }} أساتذة</span>
        </div>

        <div class="card-body p-4">
            <!-- قسم البحث -->
            <div class="row mb-4">
                <div class="col-md-6 mb-3 mb-md-0">
                    <div class="input-group">
                        <span class="input-group-text bg-light border-0">
                            <i class="fas fa-search text-primary"></i>
                        </span>
                        <input type="text" id="searchTeacherName" class="form-control border-0 shadow-sm" placeholder="بحث حسب اسم الأستاذ...">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text bg-light border-0">
                            <i class="fas fa-map-marker-alt text-primary"></i>
                        </span>
                        <input type="text" id="searchWorkplace" class="form-control border-0 shadow-sm" placeholder="بحث حسب مكان العمل...">
                    </div>
                </div>
            </div>

            <!-- جدول الأساتذة -->
            <div class="table-responsive">
                <table class="table table-hover custom-table" id="teachers-table">
                    <thead class="table-light">
                        <tr class="text-center">
                            <th scope="col" style="width: 60px;">#</th>
                            <th scope="col">اسم الأستاذ(ة)</th>
                            <th scope="col">مكان العمل</th>
                            <th scope="col" style="width: 120px;">معاينة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for teacher in teachers %}
                        <tr>
                            <td class="text-center">
                                <span class="row-number{% if teacher.is_admin %} is-admin{% endif %}">
                                    {{ loop.index }}
                                    {% if teacher.is_admin %}
                                    <span class="admin-badge" title="مشرف"><i class="fas fa-star"></i></span>
                                    {% endif %}
                                </span>
                            </td>
                            <td>
                                {{ teacher.teacher_name }}
                            </td>
                            <td class="workplace-cell">{{ teacher.workplace }}</td>
                            <td class="text-center">
                                <button type="button" class="btn btn-sm btn-primary view-schedule-btn rounded-circle"
                                        data-user-id="{{ teacher.id }}"
                                        title="معاينة التوقيت الأسبوعي">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>

                <!-- رسالة عندما لا توجد نتائج للبحث -->
                <div id="noResultsMessage" class="text-center py-4 d-none">
                    <i class="fas fa-search fa-2x text-muted mb-3"></i>
                    <p class="text-muted">لم يتم العثور على نتائج مطابقة لبحثك</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال عرض جدول التوقيت -->
<div class="modal fade" id="scheduleModal" tabindex="-1" aria-labelledby="scheduleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content border-0 shadow">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="scheduleModalLabel" style="font-size: 0.9rem;">جدول التوقيت الأسبوعي للأستاذ(ة): <span id="teacher-name-display"></span></h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3" id="schedule-loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل جدول التوقيت...</p>
                </div>

                <div class="table-responsive" id="schedule-container" style="display: none;">
                    <table class="table table-bordered text-center compact-schedule" id="schedule-table-modal">
                        <thead class="table-light">
                            <tr>
                                <th>التوقيت</th>
                                <th>الأحد</th>
                                <th>الإثنين</th>
                                <th>الثلاثاء</th>
                                <th>الأربعاء</th>
                                <th>الخميس</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- الفترة الصباحية -->
                            <tr>
                                <td class="table-light time-cell" data-original-time="08:00">08:00 - 09:00</td>
                                <td data-day="0" data-hour="8" class="schedule-cell"></td>
                                <td data-day="1" data-hour="8" class="schedule-cell"></td>
                                <td data-day="2" data-hour="8" class="schedule-cell"></td>
                                <td data-day="3" data-hour="8" class="schedule-cell"></td>
                                <td data-day="4" data-hour="8" class="schedule-cell"></td>
                            </tr>
                            <tr>
                                <td class="table-light time-cell" data-original-time="09:00">09:00 - 10:00</td>
                                <td data-day="0" data-hour="9" class="schedule-cell"></td>
                                <td data-day="1" data-hour="9" class="schedule-cell"></td>
                                <td data-day="2" data-hour="9" class="schedule-cell"></td>
                                <td data-day="3" data-hour="9" class="schedule-cell"></td>
                                <td data-day="4" data-hour="9" class="schedule-cell"></td>
                            </tr>
                            <tr>
                                <td class="table-light time-cell" data-original-time="10:00">10:00 - 11:00</td>
                                <td data-day="0" data-hour="10" class="schedule-cell"></td>
                                <td data-day="1" data-hour="10" class="schedule-cell"></td>
                                <td data-day="2" data-hour="10" class="schedule-cell"></td>
                                <td data-day="3" data-hour="10" class="schedule-cell"></td>
                                <td data-day="4" data-hour="10" class="schedule-cell"></td>
                            </tr>
                            <tr>
                                <td class="table-light time-cell" data-original-time="11:00">11:00 - 12:00</td>
                                <td data-day="0" data-hour="11" class="schedule-cell"></td>
                                <td data-day="1" data-hour="11" class="schedule-cell"></td>
                                <td data-day="2" data-hour="11" class="schedule-cell"></td>
                                <td data-day="3" data-hour="11" class="schedule-cell"></td>
                                <td data-day="4" data-hour="11" class="schedule-cell"></td>
                            </tr>

                            <!-- فاصل بين الفترتين -->
                            <tr class="bg-white">
                                <td colspan="6" class="text-center">
                                    <strong>فترة الراحة</strong>
                                </td>
                            </tr>

                            <!-- الفترة المسائية -->
                            <tr>
                                <td class="table-light time-cell" data-original-time="13:00">13:00 - 14:00</td>
                                <td data-day="0" data-hour="13" class="schedule-cell"></td>
                                <td data-day="1" data-hour="13" class="schedule-cell"></td>
                                <td data-day="2" data-hour="13" class="schedule-cell"></td>
                                <td data-day="3" data-hour="13" class="schedule-cell"></td>
                                <td data-day="4" data-hour="13" class="schedule-cell"></td>
                            </tr>
                            <tr>
                                <td class="table-light time-cell" data-original-time="14:00">14:00 - 15:00</td>
                                <td data-day="0" data-hour="14" class="schedule-cell"></td>
                                <td data-day="1" data-hour="14" class="schedule-cell"></td>
                                <td data-day="2" data-hour="14" class="schedule-cell"></td>
                                <td data-day="3" data-hour="14" class="schedule-cell"></td>
                                <td data-day="4" data-hour="14" class="schedule-cell"></td>
                            </tr>
                            <tr>
                                <td class="table-light time-cell" data-original-time="15:00">15:00 - 16:00</td>
                                <td data-day="0" data-hour="15" class="schedule-cell"></td>
                                <td data-day="1" data-hour="15" class="schedule-cell"></td>
                                <td data-day="2" data-hour="15" class="schedule-cell"></td>
                                <td data-day="3" data-hour="15" class="schedule-cell"></td>
                                <td data-day="4" data-hour="15" class="schedule-cell"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="alert alert-warning" id="no-schedule-message" style="display: none;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    لم يتم العثور على جدول توقيت لهذا الأستاذ.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تعريف الحدث على أزرار معاينة الجدول
    document.querySelectorAll('.view-schedule-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const userId = this.getAttribute('data-user-id');
            const teacherName = this.parentElement.previousElementSibling.previousElementSibling.textContent.trim();

            // عرض اسم الأستاذ في عنوان المودال
            document.getElementById('teacher-name-display').textContent = teacherName;

            // إظهار رسالة التحميل وإخفاء الجدول
            document.getElementById('schedule-loading').style.display = 'block';
            document.getElementById('schedule-container').style.display = 'none';
            document.getElementById('no-schedule-message').style.display = 'none';

            // فتح المودال
            const scheduleModal = new bootstrap.Modal(document.getElementById('scheduleModal'));
            scheduleModal.show();

            // جلب بيانات جدول التوقيت
            fetchTeacherSchedule(userId);
        });
    });

    // دالة لجلب بيانات جدول توقيت الأستاذ
    function fetchTeacherSchedule(userId) {
        fetch(`/get_teacher_schedule/${userId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            // إخفاء رسالة التحميل
            document.getElementById('schedule-loading').style.display = 'none';

            if (data.status === 'success') {
                // التحقق من وجود بيانات في الجدول
                if (data.data.schedule && data.data.schedule.length > 0) {
                    // تفريغ الجدول أولاً
                    clearScheduleTable();

                    // ملء الجدول بالبيانات
                    fillScheduleTable(data.data.schedule, data.data.times);

                    // إظهار الجدول
                    document.getElementById('schedule-container').style.display = 'block';
                } else {
                    // إظهار رسالة عدم وجود جدول
                    document.getElementById('no-schedule-message').style.display = 'block';
                }
            } else {
                // إظهار رسالة خطأ
                document.getElementById('no-schedule-message').textContent = data.message || 'حدث خطأ أثناء جلب البيانات';
                document.getElementById('no-schedule-message').style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error fetching schedule:', error);
            document.getElementById('schedule-loading').style.display = 'none';
            document.getElementById('no-schedule-message').textContent = 'حدث خطأ أثناء جلب البيانات';
            document.getElementById('no-schedule-message').style.display = 'block';
        });
    }

    // دالة لتفريغ جدول التوقيت
    function clearScheduleTable() {
        const cells = document.querySelectorAll('#schedule-table-modal .schedule-cell');
        cells.forEach(cell => {
            cell.textContent = '';
            cell.className = 'schedule-cell';
        });

        // إعادة توقيتات الخلايا إلى قيمها الافتراضية
        const timeCells = document.querySelectorAll('#schedule-table-modal .time-cell');
        timeCells.forEach(cell => {
            const originalTime = cell.getAttribute('data-original-time');
            let timeRange = '';

            switch(originalTime) {
                case '08:00': timeRange = '08:00 - 09:00'; break;
                case '09:00': timeRange = '09:00 - 10:00'; break;
                case '10:00': timeRange = '10:00 - 11:00'; break;
                case '11:00': timeRange = '11:00 - 12:00'; break;
                case '13:00': timeRange = '13:00 - 14:00'; break;
                case '14:00': timeRange = '14:00 - 15:00'; break;
                case '15:00': timeRange = '15:00 - 16:00'; break;
            }

            cell.textContent = timeRange;
        });
    }

    // دالة لملء جدول التوقيت بالبيانات
    function fillScheduleTable(scheduleData, timesData) {
        // أولاً، تحديث خلايا التوقيت إذا كانت متوفرة
        if (timesData && timesData.length > 0) {
            timesData.forEach(timeItem => {
                const timeCell = document.querySelector(`#schedule-table-modal .time-cell[data-original-time="${timeItem.original_time}"]`);
                if (timeCell) {
                    timeCell.textContent = timeItem.time_range;
                }
            });
        }

        // ثم ملء خلايا الجدول بالبيانات
        scheduleData.forEach(item => {
            const day = item.day;
            const hour = item.hour;
            const className = item.class_name;

            const cell = document.querySelector(`#schedule-table-modal td[data-day="${day}"][data-hour="${hour}"]`);
            if (cell) {
                cell.textContent = className;
                cell.classList.add('filled-cell');
            }
        });
    }

    // وظائف البحث
    const searchTeacherName = document.getElementById('searchTeacherName');
    const searchWorkplace = document.getElementById('searchWorkplace');
    const tableRows = document.querySelectorAll('#teachers-table tbody tr');
    const noResultsMessage = document.getElementById('noResultsMessage');

    // دالة البحث الرئيسية
    function filterTable() {
        const teacherNameFilter = searchTeacherName.value.toLowerCase();
        const workplaceFilter = searchWorkplace.value.toLowerCase();

        let visibleCount = 0;

        // إعادة تعيين فئات المجموعات
        tableRows.forEach(row => {
            row.classList.remove('new-workplace-group');
        });

        // تطبيق معايير البحث
        tableRows.forEach(row => {
            const teacherName = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
            const workplace = row.querySelector('td:nth-child(3)').textContent.toLowerCase();

            // عرض الصف إذا كان يتطابق مع كلا المرشحين
            const matchesTeacherName = teacherName.includes(teacherNameFilter);
            const matchesWorkplace = workplace.includes(workplaceFilter);

            if (matchesTeacherName && matchesWorkplace) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        // إظهار رسالة عدم وجود نتائج إذا لم تكن هناك صفوف مرئية
        if (visibleCount === 0) {
            noResultsMessage.classList.remove('d-none');
        } else {
            noResultsMessage.classList.add('d-none');

            // إعادة تطبيق مجموعات مكان العمل للصفوف المرئية فقط
            let currentWorkplace = '';
            tableRows.forEach(row => {
                if (row.style.display !== 'none') {
                    const workplace = row.querySelector('td:nth-child(3)').textContent.trim();
                    if (workplace !== currentWorkplace) {
                        row.classList.add('new-workplace-group');
                        currentWorkplace = workplace;
                    }
                }
            });
        }
    }

    // تسجيل أحداث البحث
    searchTeacherName.addEventListener('input', filterTable);
    searchWorkplace.addEventListener('input', filterTable);

    // إضافة فئة 'supervisor' إلى صفوف المشرفين للمتصفحات القديمة التي لا تدعم خاصية :has()
    document.querySelectorAll('#teachers-table tbody tr').forEach(row => {
        if (row.querySelector('.is-admin')) {
            row.classList.add('supervisor');
        }
    });

    // تهيئة الجدول
    filterTable();
});
</script>

<style>
.filled-cell {
    background-color: rgba(13, 110, 253, 0.1);
    font-weight: 500;
    border: 1px solid #c6d3e6;
}

#teachers-table tbody tr {
    transition: all 0.2s ease;
    height: 40px;
}

#teachers-table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

.view-schedule-btn {
    transition: all 0.3s ease;
    width: 28px;
    height: 28px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.view-schedule-btn:hover {
    transform: scale(1.15);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* تنسيقات خاصة بالمودال */
#teacher-name-display {
    font-weight: bold;
    color: #fff;
}

#schedule-table-modal {
    font-size: 0.8rem;
    border-collapse: collapse;
    border: 1px solid #dee2e6;
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.03);
}

#schedule-table-modal th,
#schedule-table-modal td {
    padding: 0.25rem 0.15rem;
    border: 1px solid #c6d3e6;
    height: 28px;
}

#schedule-table-modal .time-cell {
    font-size: 0.75rem;
    width: 85px;
}

#schedule-table-modal th {
    background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    padding: 8px 5px;
    font-size: 0.85rem;
}

/* تنسيق الجدول الرئيسي */
.custom-table {
    border-radius: 8px;
    overflow: hidden;
    border-collapse: collapse;
    border: 1px solid #c6d3e6;
    width: 100%;
}

.custom-table thead th {
    background-color: #f0f4f8;
    border: 1px solid #c6d3e6;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
}

.custom-table tbody tr {
    border-bottom: 1px solid #c6d3e6;
}

.custom-table tbody td {
    padding: 0.4rem 0.75rem;
    vertical-align: middle;
    border: 1px solid #c6d3e6;
    max-width: 200px;
}

/* تنسيق عمود الرقم التسلسلي والمعاينة */
.custom-table th:first-child,
.custom-table td:first-child {
    width: 40px;
    text-align: center;
}

.custom-table th:last-child,
.custom-table td:last-child {
    width: 60px;
    text-align: center;
}

/* تنسيق حقول البحث */
.input-group {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.input-group-text {
    border-radius: 8px 0 0 8px;
    padding: 0.5rem 1rem;
}

.input-group .form-control {
    height: 40px;
    transition: all 0.3s;
    border-radius: 0 8px 8px 0;
}

.input-group .form-control:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
    border-color: #86b7fe;
}

/* تنسيق البطاقة الرئيسية */
.card {
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s;
}

.card-header {
    border-radius: 10px 10px 0 0;
}

.bg-gradient {
    background: linear-gradient(to bottom, #ffffff, #f8f9fa);
}

/* تنسيق صف المشرف */
.custom-table tbody tr.supervisor {
    border-left: 3px solid #0dcaf0;
}

/* تنسيق مجموعات مكان العمل */
.new-workplace-group {
    border-top: 2px solid #e9ecef;
}

.new-workplace-group td {
    padding-top: 1.25rem;
}

.new-workplace-group td.workplace-cell {
    font-weight: 600;
    color: #0d6efd;
}

/* خلية مكان العمل */
#teachers-table td.workplace-cell {
    font-weight: 500;
    color: #495057;
}

/* تغميق خلفية الصفوف عند التحويم */
.new-workplace-group:hover {
    background-color: rgba(13, 110, 253, 0.03);
}

@media (max-width: 767.98px) {
    #schedule-table-modal {
        font-size: 0.6rem;
    }

    #schedule-table-modal th,
    #schedule-table-modal td {
        padding: 0.12rem 0.08rem;
        height: 22px;
    }

    #schedule-table-modal .time-cell {
        width: 65px;
        font-size: 0.6rem;
    }

    #schedule-table-modal th {
        font-size: 0.7rem;
        padding: 6px 3px;
    }

    .input-group .form-control {
        height: 38px;
    }

    .custom-table tbody td {
        padding: 0.3rem 0.4rem;
        font-size: 0.85rem;
    }

    .view-schedule-btn {
        width: 24px;
        height: 24px;
    }

    /* تصغير حجم المودال على الهاتف */
    #scheduleModal .modal-dialog {
        max-width: 95%;
        margin: 0.5rem auto;
    }

    #scheduleModal .modal-content {
        border-radius: 8px;
    }

    #scheduleModal .modal-header {
        padding: 8px 12px;
    }

    #scheduleModal .modal-body {
        padding: 10px;
    }

    #scheduleModal .modal-footer {
        padding: 8px 12px;
    }

    #scheduleModalLabel {
        font-size: 0.85rem !important;
    }

    #schedule-loading p {
        font-size: 0.85rem;
    }

    #no-schedule-message {
        font-size: 0.85rem;
    }

    #scheduleModal .btn {
        font-size: 0.85rem;
        padding: 0.25rem 0.5rem;
    }
}

/* تنسيقات إضافية للهواتف الصغيرة جداً */
@media (max-width: 576px) {
    #schedule-table-modal {
        font-size: 0.55rem;
    }

    #schedule-table-modal th,
    #schedule-table-modal td {
        padding: 0.1rem 0.05rem;
        height: 20px;
    }

    #schedule-table-modal .time-cell {
        width: 60px;
        font-size: 0.55rem;
    }

    #schedule-table-modal th {
        font-size: 0.65rem;
        padding: 5px 2px;
    }

    /* تصغير حجم المودال أكثر على الهواتف الصغيرة */
    #scheduleModal .modal-dialog {
        max-width: 98%;
        margin: 0.3rem auto;
    }

    #scheduleModal .modal-header {
        padding: 6px 10px;
    }

    #scheduleModal .modal-body {
        padding: 8px;
    }

    #scheduleModal .modal-footer {
        padding: 6px 10px;
    }

    #scheduleModalLabel {
        font-size: 0.8rem !important;
    }

    #schedule-loading p {
        font-size: 0.8rem;
    }

    #no-schedule-message {
        font-size: 0.8rem;
    }

    #scheduleModal .btn {
        font-size: 0.8rem;
        padding: 0.2rem 0.4rem;
    }

    /* تصغير حجم خط جدول الأساتذة */
    #teachers-table tbody td {
        font-size: 0.8rem;
        padding: 0.25rem 0.3rem;
    }

    #teachers-table thead th {
        font-size: 0.8rem;
    }
}

/* تنسيقات جدول الأساتذة المحدثة لتتوافق مع progress-dashboard */
/* الأولوية العالية ستضمن أن هذه التنسيقات لن يتم تجاوزها */
#teachers-table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
}

#teachers-table thead th {
    font-weight: 600;
    text-align: center;
    background: linear-gradient(to bottom, #f8f9fa, #e9ecef) !important;
    border-bottom: 2px solid #dee2e6 !important;
    border: 1px solid #dee2e6;
    padding: 8px 5px;
    font-size: 0.9rem;
}

#teachers-table tbody td {
    padding: 6px 5px;
    vertical-align: middle;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* تصميم رقم السطر */
#teachers-table .row-number {
    display: inline-block;
    width: 22px;
    height: 22px;
    line-height: 22px;
    text-align: center;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #495057;
    font-weight: bold;
    font-size: 0.7rem;
    position: relative;
}

/* تمييز المشرفين */
.is-admin {
    position: relative;
}

.admin-badge {
    position: absolute;
    top: -3px;
    right: -3px;
    width: 14px;
    height: 14px;
    background-color: #ffc107;
    border-radius: 50%;
    border: 1px solid #fff;
    font-size: 7px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
}
</style>
{% endblock %}