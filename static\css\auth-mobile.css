/**
 * تنسيقات CSS لصفحتي الدخول والتسجيل في وضع الهاتف
 */

/* الحفاظ على أبعاد الإطار وتكبير خط الحقول في وضع الهاتف فقط */
@media (max-width: 768px) {
    /* الحفاظ على أبعاد الإطار */
    .login-card {
        width: 100%;
        max-width: 400px !important; /* الحفاظ على العرض الأقصى */
        margin: 0 auto;
    }

    /* تكبير خط الحقول مع تقليل الارتفاع */
    .form-control {
        font-size: 16px !important; /* تكبير حجم الخط */
        padding: 8px 12px !important; /* تقليل التباعد الداخلي لتقليل الارتفاع */
        height: auto !important; /* السماح بتحديد الارتفاع تلقائيًا */
        line-height: 1.2 !important; /* تقليل المسافة بين السطور */
    }

    /* تكبير خط النص الإرشادي */
    .form-control::placeholder {
        font-size: 14px !important;
    }

    /* تكبير خط أزرار الدخول والتسجيل مع تقليل الارتفاع */
    .btn-login {
        font-size: 16px !important;
        padding: 8px 25px !important; /* تقليل التباعد العمودي */
        line-height: 1.2 !important;
    }

    /* تكبير خط روابط التسجيل وتسجيل الدخول */
    .register-link, .login-here, .register-here {
        font-size: 15px !important;
    }

    /* تكبير خط رابط نسيت كلمة المرور */
    .forgot-password {
        font-size: 15px !important;
    }

    /* تكبير خط حقل رمز التحقق مع تقليل الارتفاع */
    .captcha-input {
        font-size: 22px !important; /* تكبير حجم الخط */
        padding: 6px 10px !important; /* تقليل التباعد الداخلي */
        height: auto !important;
    }

    /* تكبير خط النص الإرشادي في حقل رمز التحقق */
    .captcha-input::placeholder {
        font-size: 14px !important;
    }

    /* تكبير خط رمز التحقق */
    .captcha-code {
        font-size: 26px !important;
    }

    /* تكبير خط حقل رقم الهاتف مع تقليل الارتفاع */
    .phone-number, .phone-prefix {
        font-size: 16px !important;
        padding: 8px 12px !important; /* تقليل التباعد الداخلي */
        height: auto !important;
    }

    /* تكبير خط زر إظهار/إخفاء كلمة المرور */
    .toggle-password {
        font-size: 18px !important;
    }

    /* تقليل المسافات بين الحقول */
    .mb-3 {
        margin-bottom: 0.75rem !important;
    }

    /* تقليل ارتفاع حاوية كلمة المرور */
    .password-field-container {
        height: auto !important;
    }

    /* تقليل ارتفاع حاوية رقم الهاتف */
    .phone-input-container {
        height: auto !important;
    }
}

/* تنسيقات إضافية للهواتف الصغيرة */
@media (max-width: 576px) {
    /* الحفاظ على أبعاد الإطار */
    .login-card {
        max-width: 100% !important;
    }

    /* تقليل ارتفاع الحقول أكثر */
    .form-control {
        padding: 6px 10px !important;
    }

    /* تقليل المسافات بين الحقول أكثر */
    .mb-3 {
        margin-bottom: 0.5rem !important;
    }
}
