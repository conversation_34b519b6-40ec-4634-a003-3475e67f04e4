<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة تحت الصيانة</title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --text-color: #333;
            --light-color: #ecf0f1;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--text-color);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            overflow-x: hidden;
        }

        .maintenance-container {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 40px;
            max-width: 800px;
            width: 100%;
            text-align: center;
            position: relative;
            overflow: hidden;
            animation: fadeIn 1s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .maintenance-icon {
            font-size: 60px;
            color: var(--secondary-color);
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }

        h1 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 2.5rem;
            font-weight: 700;
            position: relative;
            display: inline-block;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: var(--accent-color);
            border-radius: 2px;
        }

        .message {
            font-size: 1.2rem;
            line-height: 1.8;
            margin-bottom: 30px;
            color: var(--text-color);
            animation: slideIn 1s ease-in-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .decoration {
            position: absolute;
            width: 200px;
            height: 200px;
            background-color: rgba(52, 152, 219, 0.1);
            border-radius: 50%;
            z-index: -1;
        }

        .decoration-1 {
            top: -100px;
            right: -100px;
            animation: float 8s ease-in-out infinite;
        }

        .decoration-2 {
            bottom: -100px;
            left: -100px;
            animation: float 10s ease-in-out infinite reverse;
        }

        @keyframes float {
            0% {
                transform: translate(0, 0) rotate(0deg);
            }
            50% {
                transform: translate(20px, 20px) rotate(10deg);
            }
            100% {
                transform: translate(0, 0) rotate(0deg);
            }
        }

        .footer {
            margin-top: 30px;
            font-size: 0.9rem;
            color: var(--primary-color);
            opacity: 0.8;
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .maintenance-container {
                padding: 30px 20px;
            }

            h1 {
                font-size: 2rem;
            }

            .message {
                font-size: 1.1rem;
            }

            .maintenance-icon {
                font-size: 50px;
            }
        }

        @media (max-width: 480px) {
            .maintenance-container {
                padding: 25px 15px;
            }

            h1 {
                font-size: 1.8rem;
            }

            .message {
                font-size: 1rem;
                line-height: 1.6;
            }

            .maintenance-icon {
                font-size: 40px;
            }
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <div class="decoration decoration-1"></div>
        <div class="decoration decoration-2"></div>

        <div class="maintenance-icon">
            <i class="fas fa-tools"></i>
        </div>

        <h1>الصفحة تحت الصيانة</h1>

        <div class="message">
            <p>الزملاء الأعزاء،</p>
            <p>نود إعلامكم بأن صفحة الواتساب الخاصة بنا متوقفة مؤقتًا بسبب أعمال صيانة ضرورية تهدف لتحسين الأداء وضمان أفضل تجربة استخدام.</p>
            <p>نعتذر عن هذا التوقف الخارج عن إرادتنا، ونعمل جاهدين لإعادة الخدمة بأسرع وقت ممكن.</p>
            <p>شكرًا لتفهمكم وتعاونكم الدائم.</p>
        </div>

        <div class="footer">
            <p>© <span id="current-year"></span> جميع الحقوق محفوظة</p>
        </div>
    </div>

    <script>
        // تحديث السنة الحالية
        document.getElementById('current-year').textContent = new Date().getFullYear();

        // إضافة تأثيرات حركية إضافية
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.maintenance-container');

            // إضافة تأثير عند تحريك الماوس
            document.addEventListener('mousemove', function(e) {
                const x = e.clientX / window.innerWidth;
                const y = e.clientY / window.innerHeight;

                container.style.transform = `perspective(1000px) rotateY(${x * 5 - 2.5}deg) rotateX(${y * -5 + 2.5}deg)`;
            });

            // إعادة الوضع الطبيعي عند مغادرة الماوس
            document.addEventListener('mouseleave', function() {
                container.style.transform = 'perspective(1000px) rotateY(0deg) rotateX(0deg)';
            });
        });
    </script>
</body>
</html>
