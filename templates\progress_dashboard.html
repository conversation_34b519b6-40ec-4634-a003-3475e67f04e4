{% extends "base.html" %}
{% block title %}مدى التقدم{% endblock %}
{% block head %}
<script src="{{ url_for('static', filename='js/mobile-progress-dashboard.js') }}"></script>
{% endblock %}
{% block content %}
<div class="container-fluid py-4">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" id="csrf-token">

    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center py-3">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-chart-bar me-2"></i>
                        لوحة مدى التقدم
                    </h5>
                </div>
                <div class="card-body p-3">
                    <div class="alert alert-info d-flex align-items-center py-2">
                        <i class="fas fa-calendar-alt me-2 fs-5"></i>
                        <div>
                            <strong>الأسبوع الحالي حسب التدرج السنوي:</strong>
                            <span id="current-week-display" class="badge bg-primary ms-2 px-3 py-2">{{ current_week }}</span>
                        </div>
                    </div>

                    <!-- أدوات البحث والفلترة -->
                    <div class="row g-2 mb-3">
                        <div class="col-md-3">
                            <div class="input-group input-group-sm">
                                <span class="input-group-text bg-light"><i class="fas fa-search"></i></span>
                                <input type="text" id="search-teacher" class="form-control" placeholder="بحث باسم الأستاذ(ة)...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="input-group input-group-sm">
                                <span class="input-group-text bg-light"><i class="fas fa-map-marker-alt"></i></span>
                                <input type="text" id="search-workplace" class="form-control" placeholder="بحث حسب مكان العمل...">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="input-group input-group-sm">
                                <span class="input-group-text bg-light"><i class="fas fa-filter"></i></span>
                                <select id="filter-weeks" class="form-select">
                                    <option value="all">جميع المستويات</option>
                                    <option value="ahead">المتقدمين</option>
                                    <option value="on-track">المسايرين</option>
                                    <option value="behind-1">المتأخرين بأسبوع</option>
                                    <option value="behind-2">المتأخرين بأسبوعين</option>
                                    <option value="behind-more">المتأخرين بأكثر</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <button id="refresh-data" class="btn btn-sm btn-outline-primary w-100">
                                <i class="fas fa-sync-alt"></i> تحديث
                            </button>
                        </div>
                    </div>

                    <!-- جدول مدى التقدم -->
                    <div class="table-responsive mt-2">
                        <table id="progress-dashboard-table" class="table table-bordered table-hover align-middle table-sm">
                            <thead class="table-light">
                                <tr>
                                    <th class="text-center" width="5%">#</th>
                                    <th class="text-center">اسم الأستاذ(ة)</th>
                                    <th class="text-center">مكان العمل</th>
                                    <th class="text-center">المستوى</th>
                                    <th class="text-center">عنوان آخر حصة تعليمية</th>
                                    <th class="text-center" width="8%">رقم أسبوع</th>
                                    <th class="text-center" width="15%">الحالة</th>
                                </tr>
                            </thead>
                            <tbody id="progress-dashboard-body">
                                <tr>
                                    <td colspan="7" class="text-center py-3">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                        <p class="mt-2 text-muted">جاري تحميل بيانات التقدم...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* تصميم الجدول */
    #progress-dashboard-table {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
    }

    #progress-dashboard-table thead th {
        font-weight: 600;
        text-align: center;
        background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
        border-bottom: 2px solid #dee2e6;
        padding: 8px 5px;
        font-size: 0.9rem;
    }

    #progress-dashboard-table tbody td {
        padding: 6px 5px;
        vertical-align: middle;
        font-size: 0.9rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* في وضع الهاتف المحمول، سيتم تجاوز هذه الخصائص */
    @media (max-width: 576px) {
        #progress-dashboard-table tbody td {
            white-space: nowrap;
            overflow: visible;
            text-overflow: clip;
        }
    }

    /* تعديل حقل عنوان الحصة */
    #progress-dashboard-table td:nth-child(5) {
        max-width: 200px;
        /* سيتم تجاوز هذه الخصائص في وضع الهاتف المحمول */
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* تنسيق عمود مكان العمل */
    #progress-dashboard-table td:nth-child(3) {
        font-weight: 500;
        color: #495057;
        max-width: 150px;
        /* سيتم تجاوز هذه الخصائص في وضع الهاتف المحمول */
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* تمييز مكان العمل الجديد (لتقسيم المجموعات بصرياً) */
    .new-workplace {
        border-top: 2px solid #e9ecef;
    }

    .new-workplace td {
        background-color: rgba(13, 110, 253, 0.02);
    }

    /* تصميم بطاقات الحالة */
    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 5px;
        padding: 4px 8px;
        border-radius: 50px;
        font-size: 0.8rem;
        font-weight: 500;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        white-space: nowrap;
    }

    .status-ahead {
        background-color: #e8f5e9;
        color: #2e7d32;
        border: 1px solid #c8e6c9;
    }

    .status-behind {
        background-color: #ffebee;
        color: #c62828;
        border: 1px solid #ffcdd2;
    }

    .status-on-track {
        background-color: #e3f2fd;
        color: #1565c0;
        border: 1px solid #bbdefb;
    }

    /* تأثير الصفوف عند المرور */
    #progress-dashboard-table tbody tr {
        transition: all 0.2s ease;
    }

    #progress-dashboard-table tbody tr:hover {
        background-color: rgba(13, 110, 253, 0.05);
        transform: translateY(-1px);
        box-shadow: 0 3px 5px rgba(0, 0, 0, 0.05);
    }

    /* تصميم رقم الأسبوع */
    .week-number {
        display: inline-block;
        width: 28px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        border-radius: 50%;
        background-color: #f0f0f0;
        font-weight: bold;
        font-size: 0.8rem;
    }

    /* تصميم رقم السطر */
    .row-number {
        display: inline-block;
        width: 22px;
        height: 22px;
        line-height: 22px;
        text-align: center;
        border-radius: 50%;
        background-color: #e9ecef;
        color: #495057;
        font-weight: bold;
        font-size: 0.7rem;
    }

    /* تمييز المشرفين */
    .is-admin {
        position: relative;
    }

    .admin-badge {
        position: absolute;
        top: -3px;
        right: -3px;
        width: 14px;
        height: 14px;
        background-color: #ffc107;
        border-radius: 50%;
        border: 1px solid #fff;
        font-size: 7px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
    }

    /* تنسيق حقول البحث */
    .input-group-text {
        border-right: 0;
    }

    #search-teacher, #filter-weeks {
        border-left: 0;
    }

    /* صف بدون بيانات */
    .no-results-row {
        background-color: #f8f9fa;
        font-style: italic;
        color: #6c757d;
    }

    /* تنسيق الخلايا المدمجة */
    .rowspan-cell-top {
        border-bottom: 0 !important;
    }

    .rowspan-cell-middle {
        border-top: 0 !important;
        border-bottom: 0 !important;
    }

    .rowspan-cell-bottom {
        border-top: 0 !important;
    }

    /* إخفاء محتوى الخلايا المكررة */
    .hidden-content {
        display: none;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const csrfToken = document.getElementById('csrf-token').value;
        const progressTable = document.getElementById('progress-dashboard-body');
        const currentWeek = parseInt(document.getElementById('current-week-display').textContent, 10);
        const searchTeacher = document.getElementById('search-teacher');
        const searchWorkplace = document.getElementById('search-workplace');
        const filterWeeks = document.getElementById('filter-weeks');
        const refreshBtn = document.getElementById('refresh-data');

        // متغير لتخزين البيانات الأصلية
        let originalData = [];

        // جلب بيانات تقدم جميع الأساتذة
        function fetchProgressData() {
            progressTable.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-2 text-muted">جاري تحميل بيانات التقدم...</p>
                    </td>
                </tr>
            `;

            fetch('/get-all-teachers-progress', {
                method: 'GET',
                headers: {
                    'X-CSRF-Token': csrfToken,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // تصفية المديرين من البيانات (نعرض فقط الأساتذة العاديين والمشرفين)
                let filteredData = data.filter(item => {
                    // استبعاد المدير الرئيسي (admin_type = 'مدير')
                    return item.admin_type !== 'مدير';
                });

                originalData = filteredData;
                applyFilters(); // استخدام الفلترة مباشرة
            })
            .catch(error => {
                console.error('خطأ في جلب بيانات التقدم:', error);
                progressTable.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-danger py-3">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            حدث خطأ أثناء تحميل البيانات. يرجى المحاولة مرة أخرى.
                        </td>
                    </tr>
                `;
            });
        }

        // تطبيق الفلترة على البيانات
        function applyFilters() {
            const searchTerm = searchTeacher.value.trim().toLowerCase();
            const searchWorkplaceTerm = searchWorkplace.value.trim().toLowerCase();
            const filterValue = filterWeeks.value;

            let filteredData = originalData.filter(item => {
                // فلترة حسب اسم الأستاذ
                const nameMatch = item.teacher_name.toLowerCase().includes(searchTerm);

                // حساب الفرق بين الأسابيع
                const weekDifference = item.last_lesson_week - currentWeek;

                // فلترة حسب حالة التقدم
                let statusMatch = true;
                if (filterValue === 'ahead') {
                    statusMatch = weekDifference > 0;
                } else if (filterValue === 'on-track') {
                    statusMatch = weekDifference === 0;
                } else if (filterValue === 'behind-1') {
                    statusMatch = weekDifference === -1;
                } else if (filterValue === 'behind-2') {
                    statusMatch = weekDifference === -2;
                } else if (filterValue === 'behind-more') {
                    statusMatch = weekDifference < -2;
                }

                // فلترة حسب مكان العمل
                const workplaceMatch = item.workplace.toLowerCase().includes(searchWorkplaceTerm);

                return nameMatch && statusMatch && workplaceMatch;
            });

            // تنظيم البيانات حسب مكان العمل أولاً ثم حسب اسم الأستاذ
            filteredData.sort((a, b) => {
                // المقارنة الأولى حسب مكان العمل
                const workplaceCompare = a.workplace.localeCompare(b.workplace);

                // إذا كان مكان العمل متساوي، فالترتيب يكون حسب اسم الأستاذ
                if (workplaceCompare === 0) {
                    return a.teacher_name.localeCompare(b.teacher_name);
                }

                return workplaceCompare;
            });

            renderProgressTable(filteredData);
        }

        // عرض بيانات الجدول
        function renderProgressTable(data) {
            if (!data || data.length === 0) {
                progressTable.innerHTML = `
                    <tr class="no-results-row">
                        <td colspan="7" class="text-center py-3">
                            <i class="fas fa-info-circle me-2"></i>
                            لا توجد بيانات تقدم متاحة حالياً.
                        </td>
                    </tr>
                `;
                return;
            }

            // تنظيم البيانات بحيث يتم جمع المستويات لنفس الأستاذ
            const teacherLevels = {};

            data.forEach(item => {
                if (!teacherLevels[item.teacher_name]) {
                    teacherLevels[item.teacher_name] = [];
                }
                teacherLevels[item.teacher_name].push(item);
            });

            let tableHtml = '';
            let rowCounter = 1;
            let currentWorkplace = '';

            // إنشاء صفوف الجدول مع دمج خلايا الاسم والرقم
            for (const [teacherName, levels] of Object.entries(teacherLevels)) {
                const rowCount = levels.length;
                const teacherWorkplace = levels[0].workplace;

                // تحديد ما إذا كان هذا مكان عمل جديد
                const isNewWorkplace = teacherWorkplace !== currentWorkplace;
                if (isNewWorkplace) {
                    currentWorkplace = teacherWorkplace;
                }

                levels.forEach((item, index) => {
                    // حساب حالة التقدم
                    const weekDifference = item.last_lesson_week - currentWeek;
                    let statusClass, statusText, statusIcon;

                    // تنسيق النص حسب حالة التقدم
                    if (weekDifference > 0) {
                        statusClass = 'status-ahead';
                        statusText = `متقدم بـ ${weekDifference} أسبوع`;
                        statusIcon = 'fa-arrow-circle-up';
                    } else if (weekDifference < 0) {
                        statusClass = 'status-behind';
                        statusText = `متأخر بـ ${Math.abs(weekDifference)} أسبوع`;
                        statusIcon = 'fa-arrow-circle-down';
                    } else {
                        statusClass = 'status-on-track';
                        statusText = 'مسايِر للتدرج';
                        statusIcon = 'fa-check-circle';
                    }

                    // إضافة علامة للمشرفين
                    const isAdmin = item.is_admin && item.admin_type !== 'مدير';
                    const adminBadge = isAdmin
                        ? `<span class="admin-badge" title="مشرف"><i class="fas fa-star"></i></span>`
                        : '';

                    // تحديد نوع الخلية المدمجة
                    let cellClass = '';
                    if (rowCount > 1) {
                        if (index === 0) {
                            cellClass = 'rowspan-cell-top';
                        } else if (index === rowCount - 1) {
                            cellClass = 'rowspan-cell-bottom';
                        } else {
                            cellClass = 'rowspan-cell-middle';
                        }
                    }

                    // إضافة فئة مكان العمل الجديد للصف الأول من كل مجموعة
                    const rowClass = index === 0 && isNewWorkplace ? 'new-workplace' : '';

                    tableHtml += `<tr class="${rowClass}">`;

                    // عمود الرقم
                    if (index === 0) {
                        tableHtml += `
                            <td class="text-center ${cellClass}" ${rowCount > 1 ? 'rowspan="' + rowCount + '"' : ''}>
                                <span class="row-number${isAdmin ? ' is-admin' : ''}">${rowCounter}${adminBadge}</span>
                            </td>
                        `;
                    }

                    // عمود اسم الأستاذ
                    if (index === 0) {
                        tableHtml += `
                            <td class="${cellClass}" ${rowCount > 1 ? 'rowspan="' + rowCount + '"' : ''}>
                                ${item.teacher_name}
                            </td>
                        `;
                    }

                    // عمود مكان العمل
                    if (index === 0) {
                        tableHtml += `
                            <td class="${cellClass}" ${rowCount > 1 ? 'rowspan="' + rowCount + '"' : ''}>
                                ${item.workplace}
                            </td>
                        `;
                    }

                    // باقي الأعمدة
                    tableHtml += `
                        <td class="text-center">${item.level}</td>
                        <td class="lesson-title-cell" ${window.innerWidth > 576 ? `title="${item.last_lesson_title}"` : ''}>${item.last_lesson_title}</td>
                        <td class="text-center">
                            <span class="week-number">${item.last_lesson_week}</span>
                        </td>
                        <td class="text-center">
                            <span class="status-badge ${statusClass}">
                                <i class="fas ${statusIcon} me-1"></i>
                                ${statusText}
                            </span>
                        </td>
                    `;

                    tableHtml += `</tr>`;
                });

                rowCounter++;
            }

            progressTable.innerHTML = tableHtml;
        }

        // إضافة مستمعي الأحداث لحقول البحث والفلترة
        searchTeacher.addEventListener('input', applyFilters);
        searchWorkplace.addEventListener('input', applyFilters);
        filterWeeks.addEventListener('change', applyFilters);
        refreshBtn.addEventListener('click', fetchProgressData);

        // جلب البيانات عند تحميل الصفحة
        fetchProgressData();
    });
</script>
{% endblock %}