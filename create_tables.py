from app import app, db, SiteSettings

# إنشاء سياق التطبيق
with app.app_context():
    # إنشاء الجداول
    db.create_all()

    # إضافة عبارة الترحيب الافتراضية إذا لم تكن موجودة
    greeting = SiteSettings.query.filter_by(key='greeting_text').first()
    if not greeting:
        greeting = SiteSettings(key='greeting_text', value='السلام عليكم و رحمة الله و بركاته')
        db.session.add(greeting)
        db.session.commit()
        print("تم إضافة عبارة الترحيب الافتراضية بنجاح")
    else:
        print("عبارة الترحيب موجودة بالفعل")

    # إضافة إعداد ظهور الشريط إذا لم يكن موجودًا
    greeting_visible = SiteSettings.query.filter_by(key='greeting_visible').first()
    if not greeting_visible:
        greeting_visible = SiteSettings(key='greeting_visible', value='true')
        db.session.add(greeting_visible)
        db.session.commit()
        print("تم إضافة إعداد ظهور الشريط الافتراضي بنجاح")
    else:
        print("إعداد ظهور الشريط موجود بالفعل")

    print("تم إنشاء الجداول بنجاح")