GOOGLE_DRIVE_FOLDER_ID = '1lmruqG-ZEEzBNez0P-fu_ojVc-ttayV7'
# يمكنك الحصول على هذه المعلومات من لوحة تحكم Google Cloud
GOOGLE_CLIENT_ID = '*************-avi93hrqb0r3f1q41l3c4o380qdsun8a.apps.googleusercontent.com'
GOOGLE_CLIENT_SECRET = '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'
GOOGLE_REDIRECT_URI = 'http://localhost:5000/oauth2callback'

import os

# إعدادات Cloudinary
CLOUDINARY_CLOUD_NAME = os.environ.get('CLOUDINARY_CLOUD_NAME', 'dho0fkwf4')
CLOUDINARY_API_KEY = os.environ.get('CLOUDINARY_API_KEY', '726728831258746')
CLOUDINARY_API_SECRET = os.environ.get('CLOUDINARY_API_SECRET', 'bKLcmXjcyRDO_Uw9tfzKeeffJc4')

# إعدادات قاعدة البيانات
basedir = os.path.abspath(os.path.dirname(__file__))
instance_path = os.path.join(basedir, 'instance')
if not os.path.exists(instance_path):
    os.makedirs(instance_path)

# استخدام قاعدة البيانات الخارجية الموجودة
# يمكن تمرير DATABASE_URL كمتغير بيئة أو استخدام القاعدة الحالية
DATABASE_URL = os.environ.get('DATABASE_URL')
if DATABASE_URL:
    # إذا تم تمرير DATABASE_URL من متغيرات البيئة
    SQLALCHEMY_DATABASE_URI = DATABASE_URL
else:
    # استخدام قاعدة البيانات الخارجية الحالية (Supabase)
    import urllib.parse
    password = urllib.parse.quote_plus('Sgorge**2008')
    SQLALCHEMY_DATABASE_URI = f'postgresql://postgres.psoorxvmtjpvmyxjcyea:{password}@aws-0-eu-central-1.pooler.supabase.com:6543/postgres'

SQLALCHEMY_TRACK_MODIFICATIONS = False

# مفتاح سري للجلسة
SECRET_KEY = os.environ.get('SECRET_KEY', 'your-secret-key-123')

# إعدادات OAuth2 للبريد الإلكتروني
GOOGLE_CLIENT_ID = os.environ.get('GOOGLE_CLIENT_ID', '*************-cg6kttp07a7vfgm7vjdtmpbvch3tng94.apps.googleusercontent.com')
GOOGLE_CLIENT_SECRET = os.environ.get('GOOGLE_CLIENT_SECRET', '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')
GOOGLE_REDIRECT_URI = os.environ.get('GOOGLE_REDIRECT_URI', 'http://localhost:5000/oauth2callback')
GOOGLE_AUTH_SCOPES = ['https://www.googleapis.com/auth/gmail.send']

# إعدادات Twilio
TWILIO_ACCOUNT_SID = os.environ.get('TWILIO_ACCOUNT_SID', 'YOUR_ACCOUNT_SID')
TWILIO_AUTH_TOKEN = os.environ.get('TWILIO_AUTH_TOKEN', 'YOUR_AUTH_TOKEN')
TWILIO_PHONE_NUMBER = os.environ.get('TWILIO_PHONE_NUMBER', 'YOUR_TWILIO_PHONE_NUMBER')

# إعدادات Web Push
VAPID_PUBLIC_KEY = os.environ.get('VAPID_PUBLIC_KEY', "BHuFRZ0YE9pQxjVQBfp80fT1wkG4bsjyUUBvQ-9_v2lPm0Jf_WMJcqfv_UEitq6SCOLstxL5q-Dz9bXcwlFdRG8")
VAPID_PRIVATE_KEY = os.environ.get('VAPID_PRIVATE_KEY', "DKkJuZ1vLU3eHaX6dDX4TsZBwZF09Wl-_uJvBTWaOIw")
VAPID_CLAIM_EMAIL = os.environ.get('VAPID_CLAIM_EMAIL', "<EMAIL>")

# إعدادات البريد الإلكتروني
MAIL_SERVER = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')
MAIL_PORT = int(os.environ.get('MAIL_PORT', 465))
MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'False').lower() == 'true'
MAIL_USE_SSL = os.environ.get('MAIL_USE_SSL', 'True').lower() == 'true'
MAIL_USERNAME = os.environ.get('MAIL_USERNAME', '<EMAIL>')
MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD', 'rkyieuepofpiuvcj')
MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER', '<EMAIL>')
MAIL_MAX_EMAILS = None
MAIL_ASCII_ATTACHMENTS = False
MAIL_DEBUG = os.environ.get('MAIL_DEBUG', 'True').lower() == 'true'
