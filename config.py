GOOGLE_DRIVE_FOLDER_ID = '1lmruqG-ZEEzBNez0P-fu_ojVc-ttayV7'
# يمكنك الحصول على هذه المعلومات من لوحة تحكم Google Cloud
GOOGLE_CLIENT_ID = '*************-avi93hrqb0r3f1q41l3c4o380qdsun8a.apps.googleusercontent.com'
GOOGLE_CLIENT_SECRET = '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'
GOOGLE_REDIRECT_URI = 'http://localhost:5000/oauth2callback'

import os

# إعدادات Cloudinary
CLOUDINARY_CLOUD_NAME = 'dho0fkwf4'
CLOUDINARY_API_KEY = '726728831258746'
CLOUDINARY_API_SECRET = 'bKLcmXjcyRDO_Uw9tfzKeeffJc4'

# إعدادات قاعدة البيانات
basedir = os.path.abspath(os.path.dirname(__file__))
instance_path = os.path.join(basedir, 'instance')
if not os.path.exists(instance_path):
    os.makedirs(instance_path)
# تم تغيير قاعدة البيانات من Neon إلى Supabase
import urllib.parse
password = urllib.parse.quote_plus('Sgorge**2008')
SQLALCHEMY_DATABASE_URI = f'postgresql://postgres.psoorxvmtjpvmyxjcyea:{password}@aws-0-eu-central-1.pooler.supabase.com:6543/postgres'
SQLALCHEMY_TRACK_MODIFICATIONS = False

# مفتاح سري للجلسة
SECRET_KEY = 'your-secret-key-123'  # يجب تغييره في الإنتاج

# إعدادات OAuth2 للبريد الإلكتروني
GOOGLE_CLIENT_ID = '*************-cg6kttp07a7vfgm7vjdtmpbvch3tng94.apps.googleusercontent.com'
GOOGLE_CLIENT_SECRET = '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'
GOOGLE_REDIRECT_URI = 'http://localhost:5000/oauth2callback'
GOOGLE_AUTH_SCOPES = ['https://www.googleapis.com/auth/gmail.send']

# إعدادات Google Drive (لا تستخدم)
# GOOGLE_CLIENT_ID_DRIVE = '*************-avi93hrqb0r3f1q41l3c4o380qdsun8a.apps.googleusercontent.com'
# GOOGLE_CLIENT_SECRET_DRIVE = '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'

# إعدادات Twilio
TWILIO_ACCOUNT_SID = 'YOUR_ACCOUNT_SID'
TWILIO_AUTH_TOKEN = 'YOUR_AUTH_TOKEN'
TWILIO_PHONE_NUMBER = 'YOUR_TWILIO_PHONE_NUMBER'

# إعدادات Web Push
VAPID_PUBLIC_KEY = "BHuFRZ0YE9pQxjVQBfp80fT1wkG4bsjyUUBvQ-9_v2lPm0Jf_WMJcqfv_UEitq6SCOLstxL5q-Dz9bXcwlFdRG8"
VAPID_PRIVATE_KEY = "DKkJuZ1vLU3eHaX6dDX4TsZBwZF09Wl-_uJvBTWaOIw"
VAPID_CLAIM_EMAIL = "<EMAIL>"

# إعدادات البريد الإلكتروني
MAIL_SERVER = 'smtp.gmail.com'
MAIL_PORT = 465
MAIL_USE_TLS = False
MAIL_USE_SSL = True
MAIL_USERNAME = '<EMAIL>'
MAIL_PASSWORD = 'rkyieuepofpiuvcj'
MAIL_DEFAULT_SENDER = '<EMAIL>'
MAIL_MAX_EMAILS = None
MAIL_ASCII_ATTACHMENTS = False
MAIL_DEBUG = True
