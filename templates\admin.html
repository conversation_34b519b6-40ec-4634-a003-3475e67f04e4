{% extends "base.html" %}
{% block content %}
<!-- إضافة ملف CSS لإصلاح مشكلة ظهور نص مكان العمل مع حقل التحرير -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/edit-fix.css') }}">
<style>
    .nav-button {
        background-color: white !important;
        color: #0d6efd !important;
        border: 1px solid #0d6efd !important;
    }

    .nav-button.active-section {
        background-color: #0d6efd !important;
        color: white !important;
    }

    .nav-button:hover:not(.active-section) {
        background-color: #e9ecef !important;
    }

    /* تحسين مظهر حقول البحث */
    .input-group {
        box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
        border-radius: 0.25rem;
        overflow: hidden;
    }

    .input-group .input-group-text {
        border: none;
        padding: 0.5rem 1rem;
    }

    .input-group .form-control {
        border: none;
        padding-left: 0.75rem;
        transition: all 0.3s ease;
    }

    .input-group .form-control:focus {
        box-shadow: none;
        background-color: #f8f9fa;
    }

    /* تأثير الحقل عند التركيز */
    .input-group:focus-within {
        box-shadow: 0 2px 10px rgba(13, 110, 253, 0.2);
    }

    /* تنسيق المرشح للصفوف */
    #teachers-section tbody tr {
        transition: all 0.3s ease;
    }

    /* تعديل هيكل الجدول والخلايا */
    .yearly-progress-table {
        width: 100%;
        table-layout: fixed;
        border-collapse: collapse;
    }

    /* تحديد عرض الأعمدة */
    .yearly-progress-table th:nth-child(1),
    .yearly-progress-table td:first-child:not(.month-cell) {
        width: 60px;
    }

    .yearly-progress-table th:nth-child(2),
    .yearly-progress-table td:nth-child(2) {
        width: 80px;
    }

    .yearly-progress-table th:nth-child(5),
    .yearly-progress-table td:nth-child(5) {
        width: 120px;
    }

    /* تنسيق الخلايا القابلة للتحرير */
    .yearly-progress-table td[contenteditable="true"] {
        min-height: 38px;
        height: auto;
        white-space: normal;
        word-break: break-word;
        overflow-wrap: break-word;
        text-align: center;
        direction: rtl;
        max-height: 150px;
        overflow-y: auto;
    }

    .yearly-progress-table td[contenteditable="true"]:focus {
        outline: 2px solid #0d6efd;
        outline-offset: -2px;
    }
</style>
<div class="container mt-4">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" id="csrf-token">

    <!-- قائمة التنقل بين الأقسام -->
    <div class="card mb-4 border-0 shadow-sm">
        <div class="card-header bg-success text-white py-3">
            <div class="d-flex justify-content-center align-items-center flex-wrap">
                <button id="admin-info-btn" class="btn nav-button mx-2 mb-1 mb-md-0 fw-bold active-section">
                    <i class="fas fa-user-shield me-2"></i>بيانات المدير
                </button>
                <button id="supervisors-btn" class="btn nav-button mx-2 mb-1 mb-md-0 fw-bold">
                    <i class="fas fa-user-shield me-2"></i>إدارة المشرفين
                </button>
                <button id="teachers-btn" class="btn nav-button mx-2 mb-1 mb-md-0 fw-bold">
                    <i class="fas fa-users me-2"></i>إدارة الأساتذة
                </button>
                <button id="yearly-progress-btn" class="btn nav-button mx-2 mb-1 mb-md-0 fw-bold">
                    <i class="fas fa-calendar-alt me-2"></i>التدرج السنوي
                </button>
            </div>
        </div>
    </div>

    <!-- بطاقة معلومات المدير -->
    <div id="admin-info-section" class="card mb-4 border-0 shadow-sm section-content">
        <div class="card-body p-4">
            <div class="row">
                <div class="col-md-3 text-center mb-3 mb-md-0">
                    <!-- صورة لمدير -->
                    <div class="admin-image-container mb-2">
                        {% if current_user.profile_picture %}
                            {% if current_user.profile_picture and ('male-profile.png' in current_user.profile_picture or 'female-profile.png' in current_user.profile_picture) %}
                            <img src="{{ current_user.profile_picture }}"
                                 class="rounded-circle admin-img"
                                 alt="{{ current_user.teacher_name }}">
                            {% else %}
                            <img src="{{ url_for('static', filename='images/male-profile.png') }}"
                                 class="rounded-circle admin-img"
                                 alt="{{ current_user.teacher_name }}">
                            {% endif %}
                        {% else %}
                        <div class="rounded-circle mx-auto d-flex align-items-center justify-content-center bg-primary bg-opacity-50 text-white admin-img">
                            {{ current_user.teacher_name[0] }}
                        </div>
                        {% endif %}
                    </div>
                    <h5 class="fw-bold text-primary mb-1">{{ current_user.teacher_name }}</h5>
                    <p class="text-muted small">{{ current_user.position or 'مدير النظام' }}</p>
                    <!-- زر للذهاب إلى صفحة المدير -->
                    <a href="{{ url_for('profile', user_id=current_user.id) }}" class="btn btn-sm btn-outline-primary mt-2">
                        <i class="fas fa-user me-1"></i> عرض الملف الشخصي
                    </a>
                </div>

                <div class="col-md-9">
                    <div class="row admin-info-container">
                        <!-- المعلومات الشخصية -->
                        <div class="col-md-6">
                            <div class="admin-info-section mb-3">
                                <h6 class="section-title text-primary mb-3">
                                    <i class="fas fa-user-circle me-2"></i>المعلومات الشخصية
                                </h6>

                                <div class="admin-info-item">
                                    <span class="info-label text-muted">البريد الإلكتروني:</span>
                                    <span class="info-value">{{ current_user.email }}</span>
                                </div>

                                <div class="admin-info-item">
                                    <span class="info-label text-muted">رقم الهاتف:</span>
                                    <span class="info-value">{{ current_user.phone_number }}</span>
                                </div>

                                <div class="admin-info-item">
                                    <span class="info-label text-muted">مكان العمل:</span>
                                    <span class="info-value">{{ current_user.workplace }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- المعلومات الإدارية -->
                        <div class="col-md-6">
                            <div class="admin-info-section mb-3">
                                <h6 class="section-title text-primary mb-3">
                                    <i class="fas fa-briefcase me-2"></i>النظام والإحصائيات
                                </h6>

                                <div class="admin-info-item">
                                    <span class="info-label text-muted">المدير:</span>
                                    <span class="info-value stats-badge bg-primary">1</span>
                                </div>

                                {% set supervisor_count = namespace(total=0) %}
                                {% for user in users %}
                                    {% if user.is_admin and user.id != current_user.id %}
                                        {% set supervisor_count.total = supervisor_count.total + 1 %}
                                    {% endif %}
                                {% endfor %}

                                <div class="admin-info-item">
                                    <span class="info-label text-muted">عدد المشرفين:</span>
                                    <span class="info-value stats-badge bg-success" id="supervisor-count" data-statistic="supervisor-count">{{ supervisor_count.total }}</span>
                                </div>

                                {% set teachers_count = namespace(total=0) %}
                                {% for user in users %}
                                    {% if not user.is_admin %}
                                        {% set teachers_count.total = teachers_count.total + 1 %}
                                    {% endif %}
                                {% endfor %}

                                <div class="admin-info-item">
                                    <span class="info-label text-muted">عدد الأساتذة:</span>
                                    <span class="info-value stats-badge bg-info" id="teacher-count" data-statistic="teacher-count">{{ teachers_count.total }}</span>
                                </div>

                                <div class="admin-info-item">
                                    <span class="info-label text-muted">صلاحيات:</span>
                                    <span class="info-value badge bg-success">مدير النظام</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- بطاقة إدارة المشرفين -->
    <div id="supervisors-section" class="card mb-4 border-0 shadow-sm section-content d-none">
        <div class="card-body p-4">
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="text-primary mb-0">
                            <i class="fas fa-users-cog me-2"></i>المشرفون الحاليون
                        </h6>
                        {% if is_main_admin %}
                        <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#addSupervisorModal">
                            <i class="fas fa-plus me-1"></i> إضافة مشرف جديد
                        </button>
                        {% endif %}
                    </div>
                </div>

                <!-- قائمة المشرفين -->
                <div class="col-md-12">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">اسم المشرف</th>
                                    <th scope="col">البريد الإلكتروني</th>
                                    <th scope="col">نوع الإشراف</th>
                                    <th scope="col">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                {% if user.is_admin and user.id != current_user.id %} <!-- لا تظهر المدير الحالي -->
                                <tr id="admin-row-{{ user.id }}">
                                    <td><span class="row-number">{{ loop.index }}</span></td>
                                    <td>
                                        <a href="{{ url_for('profile', user_id=user.id) }}" class="teacher-name-link">
                                            {{ user.teacher_name }}
                                        </a>
                                    </td>
                                    <td>{{ user.email }}</td>
                                    <td>
                                        <span class="badge rounded-pill {% if user.admin_type == 'مشرف أول' %}bg-success{% elif user.admin_type == 'مشرف ثاني' %}bg-info{% else %}bg-primary{% endif %}">
                                            {{ user.admin_type or 'مدير' }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if is_main_admin %}
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="changeSupervisorType('{{ user.id }}')" title="تغيير نوع الإشراف">
                                                <i class="fas fa-exchange-alt me-1"></i><span class="btn-text">تغيير</span> <span class="btn-text-small">النوع</span>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="removeSupervisor('{{ user.id }}')" title="إزالة صلاحيات الإشراف">
                                                <i class="fas fa-user-minus me-1"></i><span class="btn-text">إزالة</span> <span class="btn-text-small">الإشراف</span>
                                            </button>
                                        </div>
                                        {% else %}
                                        <div class="text-muted small">
                                            <i class="fas fa-lock me-1"></i> الصلاحية للمدير فقط
                                        </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endif %}
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- محتوى إدارة المستخدمين -->
    <div id="teachers-section" class="card border-0 shadow-sm section-content d-none">
        <div class="card-body p-4">
            <!-- إضافة حقول البحث -->
            <div class="row mb-4">
                <div class="col-md-6 mb-3 mb-md-0">
                    <div class="input-group">
                        <span class="input-group-text bg-light border-0">
                            <i class="fas fa-search text-primary"></i>
                        </span>
                        <input type="text" id="search-teacher" class="form-control border-0 shadow-sm" placeholder="بحث حسب اسم الأستاذ(ة)...">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text bg-light border-0">
                            <i class="fas fa-map-marker-alt text-primary"></i>
                        </span>
                        <input type="text" id="search-workplace" class="form-control border-0 shadow-sm" placeholder="بحث حسب مكان العمل...">
                    </div>
                </div>
            </div>

            <div class="admin-table table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">اسم الأستاذ(ة)</th>
                            <th scope="col">مكان العمل</th>
                            <th scope="col">البريد الإلكتروني</th>
                            <th scope="col">رقم الهاتف</th>
                            <th scope="col">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users|sort(attribute='workplace') %}
                        {% if not user.is_admin %}
                        <tr id="row-{{ user.id }}" data-teacher-name="{{ user.teacher_name }}" data-workplace="{{ user.workplace }}">
                            <td><span class="row-number">{{ loop.index }}</span></td>
                            <td>
                                <span class="view-mode" id="name-{{ user.id }}">
                                    <a href="{{ url_for('profile', user_id=user.id) }}" class="teacher-name-link">
                                        {{ user.teacher_name }}
                                    </a>
                                </span>
                                <input type="text" class="form-control edit-mode d-none" value="{{ user.teacher_name }}" id="edit-name-{{ user.id }}">
                            </td>
                            <td>
                                <span class="view-mode" id="workplace-{{ user.id }}">{{ user.workplace }}</span>
                                <input type="text" class="form-control edit-mode d-none" value="{{ user.workplace }}" id="edit-workplace-{{ user.id }}">
                            </td>
                            <td>
                                <span class="view-mode" id="email-{{ user.id }}">{{ user.email }}</span>
                                <input type="email" class="form-control edit-mode d-none" value="{{ user.email }}" id="edit-email-{{ user.id }}">
                            </td>
                            <td>
                                <span class="view-mode" id="phone-{{ user.id }}">{{ user.phone_number }}</span>
                                <div class="edit-mode d-none">
                                    <input type="text" class="form-control" value="{{ user.phone_number }}"
                                           pattern="[0-9]{10}" maxlength="10" placeholder="أدخل 10 أرقام" id="edit-phone-number-{{ user.id }}">
                                </div>
                            </td>
                            <td>
                                <div class="btn-group view-mode">
                                    <button class="btn btn-sm btn-primary" onclick="toggleEdit('{{ user.id }}')">تعديل</button>
                                    <a href="{{ url_for('profile', user_id=user.id) }}" class="btn btn-sm btn-success">معاينة</a>
                                    <button class="btn btn-sm btn-danger" onclick="deleteUser('{{ user.id }}')">حذف</button>
                                </div>
                                <div class="btn-group edit-mode d-none">
                                    <button class="btn btn-sm btn-success" onclick="saveChanges('{{ user.id }}')">حفظ</button>
                                    <button class="btn btn-sm btn-secondary" onclick="cancelEdit('{{ user.id }}')">إلغاء</button>
                                </div>
                            </td>
                        </tr>
                        {% endif %}
                        {% endfor %}

                        <tr id="no-results-row" class="d-none">
                            <td colspan="6" class="text-center py-4 text-muted">
                                <i class="fas fa-search me-1"></i> لا توجد نتائج للبحث
                            </td>
                        </tr>

                        {% set normal_users_count = namespace(total=0) %}
                        {% for user in users %}
                            {% if not user.is_admin %}
                                {% set normal_users_count.total = normal_users_count.total + 1 %}
                            {% endif %}
                        {% endfor %}

                        {% if normal_users_count.total == 0 %}
                        <tr>
                            <td colspan="6" class="text-center py-4 text-muted">
                                <i class="fas fa-info-circle me-1"></i> لا يوجد مستخدمين عاديين في النظام
                            </td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- بطاقة التدرج السنوي -->
    <div id="yearly-progress-section" class="card border-0 shadow-sm section-content d-none">
        <div class="card-body p-4">
            <!-- عنوان القسم والإعدادات للشاشات المتوسطة والكبيرة -->
            <div class="d-flex justify-content-between align-items-center mb-3 d-none d-md-flex">
                <div class="d-flex align-items-center gap-3">
                    <h6 class="text-primary mb-0">
                        <i class="fas fa-list-alt me-2"></i>التدرج السنوي
                    </h6>

                    <!-- قائمة منسدلة لاختيار السنة الدراسية -->
                    <select id="year-selector" class="form-select" style="width: auto;">
                        <option value="1">السنة الأولى متوسط</option>
                        <option value="2">السنة الثانية متوسط</option>
                        <option value="3">السنة الثالثة متوسط</option>
                        <option value="4">السنة الرابعة متوسط</option>
                    </select>

                    <!-- حقل تحديد عدد الأسابيع المعتمدة في شهر سبتمبر -->
                    <div class="d-flex align-items-center">
                        <span class="me-2"><i class="fas fa-calendar-week me-1"></i> عدد الأسابيع لشهر سبتمبر:</span>
                        <div class="d-inline-flex align-items-stretch" style="height: 31px;">
                            <input type="number" class="form-control form-control-sm me-2 p-1" id="september-weeks" min="0" max="4" value="2" style="width: 60px; height: 100%; border-radius: 0.2rem;">
                            <button class="btn btn-sm btn-outline-primary p-1" id="save-september-weeks" style="height: 100%; display: inline-flex; align-items: center;">
                                <i class="fas fa-save me-1"></i> حفظ
                            </button>
                        </div>
                    </div>
                </div>

                <div>
                    <button type="button" id="clear-lessons-btn" class="btn btn-warning me-2">
                        <i class="fas fa-eraser me-1"></i> تفريغ الحقول
                    </button>
                    <button type="button" id="save-progress-btn" class="btn btn-success">
                        <i class="fas fa-save me-1"></i> حفظ التدرجات
                    </button>
                </div>
            </div>

            <!-- عنوان القسم والإعدادات للهواتف المحمولة -->
            <div class="d-md-none mb-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="text-primary mb-0">
                        <i class="fas fa-list-alt me-2"></i>التدرج السنوي
                    </h6>

                    <!-- قائمة منسدلة لاختيار السنة الدراسية -->
                    <select id="year-selector-mobile" class="form-select" style="width: auto;">
                        <option value="1">السنة الأولى متوسط</option>
                        <option value="2">السنة الثانية متوسط</option>
                        <option value="3">السنة الثالثة متوسط</option>
                        <option value="4">السنة الرابعة متوسط</option>
                    </select>
                </div>

                <div class="d-flex justify-content-between align-items-center mb-2">
                    <!-- حقل تحديد عدد الأسابيع المعتمدة في شهر سبتمبر -->
                    <div class="d-flex align-items-center">
                        <span class="me-1"><i class="fas fa-calendar-week me-1"></i> أسابيع سبتمبر:</span>
                        <div class="d-inline-flex align-items-stretch" style="height: 31px;">
                            <input type="number" class="form-control form-control-sm me-1 p-1" id="september-weeks-mobile" min="0" max="4" value="2" style="width: 40px; height: 100%; border-radius: 0.2rem;">
                            <button class="btn btn-sm btn-outline-primary p-1" id="save-september-weeks-mobile" style="height: 100%; display: inline-flex; align-items: center;">
                                <i class="fas fa-save"></i>
                            </button>
                        </div>
                    </div>

                    <div>
                        <button type="button" id="clear-lessons-btn-mobile" class="btn btn-sm btn-warning me-1">
                            <i class="fas fa-eraser"></i>
                        </button>
                        <button type="button" id="save-progress-btn-mobile" class="btn btn-sm btn-success">
                            <i class="fas fa-save"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-bordered text-center yearly-progress-table">
                    <colgroup>
                        <col style="width: 60px;">
                        <col style="width: 80px;">
                        <col>
                        <col>
                        <col style="width: 120px;">
                    </colgroup>
                    <thead>
                        <tr class="bg-primary text-white">
                            <th scope="col">الشهر</th>
                            <th scope="col">الأسبوع</th>
                            <th scope="col">الحصـــة الأولـــــى</th>
                            <th scope="col">الحصـــة الثانيــــة</th>
                            <th scope="col">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="yearly-progress-table">
                        <!-- شهر سبتمبر -->
                        <tr style="background-color: rgba(255, 193, 7, 0.1);" data-week="1" data-month="سبتمبر">
                            <td rowspan="4" class="align-middle fw-bold month-cell" style="writing-mode: vertical-lr; transform: rotate(180deg); background-color: rgba(255, 193, 7, 0.3); width: 60px;">سبتمبر</td>
                            <td class="week-number">1</td>
                            <td contenteditable="true"></td>
                            <td contenteditable="true"></td>
                            <td>
                                <button type="button" class="btn btn-sm btn-outline-primary merge-btn" onclick="mergeCells(this)">
                                    <i class="fas fa-link"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger delete-row-btn" onclick="deleteRow(this)">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success add-row-btn" onclick="addRow(this)" data-month="سبتمبر" data-color="rgba(255, 193, 7, 0.1)" data-bg-color="rgba(255, 193, 7, 0.3)">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
function toggleEdit(userId) {
    const row = document.getElementById(`row-${userId}`);
    row.querySelectorAll('.view-mode').forEach(function(el) {
        el.classList.add('d-none');
    });
    row.querySelectorAll('.edit-mode').forEach(function(el) {
        el.classList.remove('d-none');
    });
}

function cancelEdit(userId) {
    const row = document.getElementById(`row-${userId}`);
    row.querySelectorAll('.view-mode').forEach(function(el) {
        el.classList.remove('d-none');
    });
    row.querySelectorAll('.edit-mode').forEach(function(el) {
        el.classList.add('d-none');
    });
}

function showMessage(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.style.position = 'fixed';
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '1050';
    alertDiv.style.padding = '8px 16px';
    alertDiv.style.borderRadius = '4px';
    alertDiv.style.fontSize = '14px';
    alertDiv.style.maxWidth = '250px';
    alertDiv.style.textAlign = 'center';
    alertDiv.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
    alertDiv.innerHTML = message;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        alertDiv.style.animation = 'fadeOut 0.5s ease-in-out forwards';
        setTimeout(() => alertDiv.remove(), 500);
    }, 2500);
}

function saveChanges(userId) {
    const formData = new FormData();
    const fields = {
        'teacher_name': document.getElementById(`edit-name-${userId}`).value.trim(),
        'workplace': document.getElementById(`edit-workplace-${userId}`).value.trim(),
        'email': document.getElementById(`edit-email-${userId}`).value.trim(),
        'phone_number': document.getElementById(`edit-phone-number-${userId}`).value.trim()
    };

    // التحقق من البيانات
    if (!fields.teacher_name || !fields.workplace || !fields.email || !fields.phone_number) {
        showMessage('جميع الحقول مطلوبة', 'danger');
        return;
    }

    // التحقق من تنسيق رقم الهاتف
    if (!/^\d{10}$/.test(fields.phone_number)) {
        showMessage('رقم الهاتف يجب أن يتكون من 10 أرقام', 'danger');
        return;
    }

    // التحقق من تنسيق البريد الإلكتروني
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(fields.email)) {
        showMessage('يرجى إدخال بريد إلكتروني صحيح', 'danger');
        return;
    }

    // الحصول على زر الحفظ وحفظ النص الأصلي
    const saveButton = document.querySelector(`button[onclick="saveChanges('${userId}')"]`);
    const originalButtonText = saveButton.innerHTML;

    // تعطيل الزر فقط دون تغيير نصه
    saveButton.disabled = true;

    // إضافة رمز CSRF إلى البيانات
    const csrfToken = document.getElementById('csrf-token').value;
    formData.append('csrf_token', csrfToken);

    Object.keys(fields).forEach(function(key) {
        formData.append(key, fields[key]);
    });

    fetch(`/edit_user/${userId}`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': csrfToken
        },
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.message || 'حدث خطأ أثناء حفظ التغييرات');
            });
        }
        return response.json();
    })
    .then(data => {
        if (data.status === 'success') {
            // تحديث البيانات في الصفحة
            const nameElement = document.getElementById(`name-${userId}`);
            nameElement.innerHTML = `<a href="/profile/${userId}" class="teacher-name-link">${fields.teacher_name}</a>`;
            document.getElementById(`workplace-${userId}`).textContent = fields.workplace;
            document.getElementById(`email-${userId}`).textContent = fields.email;
            document.getElementById(`phone-${userId}`).textContent = fields.phone_number;

            // إخفاء نمط التعديل
            cancelEdit(userId);

            // تغيير نص الزر إلى "تم الحفظ" مباشرة
            saveButton.innerHTML = '<i class="fas fa-check"></i> تم الحفظ';

            // إعادة الزر إلى حالته الأصلية بعد ثانيتين
            setTimeout(() => {
                saveButton.innerHTML = originalButtonText;
                saveButton.disabled = false;
            }, 2000);

            // التأكد من البقاء في قسم إدارة الأساتذة
            window.showSection('teachers-section');
        } else {
            throw new Error(data.message);
        }
    })
    .catch(error => {
        // إظهار رسالة الخطأ
        showMessage(error.message, 'danger');

        // إعادة الزر إلى حالته الأصلية
        saveButton.innerHTML = originalButtonText;
        saveButton.disabled = false;
    });
}

function deleteUser(userId) {
    const csrfToken = document.getElementById('csrf-token').value;

    // الحصول على زر الحذف وتغيير حالته مباشرة
    const deleteButton = document.querySelector(`#row-${userId} .btn-danger`);
    const originalText = deleteButton.innerHTML;
    deleteButton.innerHTML = '<i class="fas fa-check"></i> تم الحذف';
    deleteButton.disabled = true;

    fetch(`/delete_user/${userId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': csrfToken,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ csrf_token: csrfToken }),
        credentials: 'same-origin'
    })
    .then(response => {
        // التحقق من نوع المحتوى قبل محاولة تحليل الرد كـ JSON
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.message || 'حدث خطأ أثناء حذف المستخدم');
                });
            }
            return response.json();
        } else {
            // في حالة كان الرد ليس JSON (قد يكون HTML أو نص)
            throw new Error('استجابة الخادم غير صالحة. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
        }
    })
    .then(data => {
        if (data.status === 'success') {
            // حذف الصف من جدول الأساتذة بعد مهلة قصيرة
            setTimeout(() => {
                document.getElementById(`row-${userId}`).remove();

                // تحديث ترقيم الأساتذة المتبقين
                updateTeacherNumbers();

                // تحقق ما إذا كان المستخدم موجودًا في قائمة المشرفين أيضًا
                const adminRow = document.getElementById(`admin-row-${userId}`);
                if (adminRow) {
                    // إذا كان المستخدم موجودًا في قائمة المشرفين، قم بإزالته
                    adminRow.remove();

                    // إعادة ترتيب أرقام المشرفين المتبقين
                    const adminRows = document.querySelectorAll('#supervisors-section tbody tr[id^="admin-row-"]');
                    adminRows.forEach((row, index) => {
                        row.querySelector('td:first-child').textContent = index + 1;
                    });
                }

                // حذف المستخدم من قائمة اختيار المستخدمين في نافذة إضافة مشرف جديد
                const userSelect = document.getElementById('user-select');
                if (userSelect) {
                    const optionToRemove = Array.from(userSelect.options).find(option => option.value === userId);
                    if (optionToRemove) {
                        userSelect.removeChild(optionToRemove);

                        // إذا لم يعد هناك مستخدمين عاديين، أظهر رسالة
                        if (userSelect.options.length <= 1) {
                            userSelect.innerHTML = '<option value="" selected disabled>-- لا يوجد مستخدمين متاحين --</option>';
                        }
                    }
                }

                // تحديث الإحصائيات في لوحة البيانات
                updateStatistics();
            }, 1000); // مهلة قصيرة قبل حذف الصف للسماح برؤية "تم الحذف"
        } else {
            throw new Error(data.message);
        }
    })
    .catch(error => {
        showMessage('حدث خطأ أثناء حذف المستخدم', 'danger');
        console.error('Error:', error);

        // إعادة الزر إلى حالته الأصلية
        deleteButton.innerHTML = originalText;
        deleteButton.disabled = false;
    });
}

// إضافة تعريف الرسوم المتحركة
const style = document.createElement('style');
style.textContent = `
@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}`;
document.head.appendChild(style);

// دوال إدارة المشرفين
function changeSupervisorType(userId) {
    // عرض مربع حوار لتغيير نوع المشرف
    Swal.fire({
        title: 'تغيير نوع الإشراف',
        html: `
            <div class="mb-2">
                <select class="form-select" id="supervisor-type" style="direction: rtl; text-align: right;">
                    <option value="مشرف أول">مشرف أول</option>
                    <option value="مشرف ثاني">مشرف ثاني</option>
                </select>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'تأكيد',
        cancelButtonText: 'إلغاء',
        reverseButtons: true,
        focusConfirm: false,
        customClass: {
            popup: 'supervisor-type-modal',
            title: 'supervisor-type-title',
            confirmButton: 'btn btn-primary',
            cancelButton: 'btn btn-secondary'
        },
        buttonsStyling: false,
        preConfirm: () => {
            return document.getElementById('supervisor-type').value;
        }
    }).then((result) => {
        if (result.isConfirmed) {
            updateSupervisorType(userId, result.value);
        }
    });
}

function updateSupervisorType(userId, type) {
    const csrfToken = document.getElementById('csrf-token').value;

    fetch(`/change_supervisor_type/${userId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            csrf_token: csrfToken,
            admin_type: type
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showMessage('تم تغيير نوع الإشراف بنجاح', 'success');
            // تحديث البيانات المعروضة مباشرة
            const badge = document.querySelector(`#admin-row-${userId} .badge`);
            badge.textContent = type;

            // تحديث لون الشارة
            badge.className = 'badge rounded-pill';
            if (type === 'مشرف أول') {
                badge.classList.add('bg-success');
            } else if (type === 'مشرف ثاني') {
                badge.classList.add('bg-info');
            } else {
                badge.classList.add('bg-primary');
            }

            // تحديث أرقام المشرفين للتأكد من التسلسل الصحيح
            updateSupervisorNumbers();

            // التأكد من البقاء في قسم إدارة المشرفين
            window.showSection('supervisors-section');
        } else {
            showMessage(data.message || 'حدث خطأ أثناء تغيير نوع الإشراف', 'danger');
        }
    })
    .catch(error => {
        showMessage('حدث خطأ أثناء تغيير نوع الإشراف', 'danger');
        console.error('Error:', error);
    });
}

function removeSupervisor(userId) {
    const csrfToken = document.getElementById('csrf-token').value;

    // الحصول على معلومات المشرف قبل إزالته
    const adminRow = document.getElementById(`admin-row-${userId}`);
    let teacherName = '';
    let email = '';
    let workplace = '';
    let phoneNumber = '';

    if (adminRow) {
        // استخراج اسم المشرف والبريد الإلكتروني
        teacherName = adminRow.querySelector('td:nth-child(2) a').textContent.trim();
        email = adminRow.querySelector('td:nth-child(3)').textContent.trim();
    }

    // إرسال طلب مع تعليمة لاسترجاع بيانات المستخدم الكاملة
    fetch(`/remove_supervisor/${userId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            csrf_token: csrfToken,
            get_full_data: true // طلب البيانات الكاملة
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // إزالة الصف من جدول المشرفين
            if (adminRow) {
                adminRow.remove();
            }

            // التحقق مما إذا كان هناك صفوف متبقية في جدول المشرفين
            const supervisorRows = document.querySelectorAll('#supervisors-section tbody tr[id^="admin-row-"]');

            // تحديث أرقام المشرفين لضمان التسلسل الصحيح
            updateSupervisorNumbers();

            // إضافة المستخدم مرة أخرى إلى قائمة الأساتذة العاديين
            if (data.user_data) {
                const userData = data.user_data;

                // إنشاء صف جديد في جدول الأساتذة
                const teachersTableBody = document.querySelector('#teachers-section tbody');

                // إنشاء صف جديد للأستاذ
                const newRow = document.createElement('tr');
                newRow.id = `row-${userData.id}`;

                newRow.innerHTML = `
                    <td><span class="row-number">0</span></td>
                    <td>
                        <span class="view-mode" id="name-${userData.id}">
                            <a href="/profile/${userData.id}" class="teacher-name-link">
                                ${userData.teacher_name}
                            </a>
                        </span>
                        <input type="text" class="form-control edit-mode d-none" value="${userData.teacher_name}" id="edit-name-${userData.id}">
                    </td>
                    <td>
                        <span class="view-mode" id="workplace-${userData.id}">${userData.workplace || '-'}</span>
                        <input type="text" class="form-control edit-mode d-none" value="${userData.workplace || ''}" id="edit-workplace-${userData.id}">
                    </td>
                    <td>
                        <span class="view-mode" id="email-${userData.id}">${userData.email}</span>
                        <input type="email" class="form-control edit-mode d-none" value="${userData.email}" id="edit-email-${userData.id}">
                    </td>
                    <td>
                        <span class="view-mode" id="phone-${userData.id}">${userData.phone_number || '-'}</span>
                        <div class="edit-mode d-none">
                            <input type="text" class="form-control" value="${userData.phone_number || ''}" pattern="[0-9]{10}" maxlength="10" placeholder="أدخل 10 أرقام" id="edit-phone-number-${userData.id}">
                        </div>
                    </td>
                    <td>
                        <div class="btn-group view-mode">
                            <button class="btn btn-sm btn-primary" onclick="toggleEdit('${userData.id}')">تعديل</button>
                            <a href="/profile/${userData.id}" class="btn btn-sm btn-success">معاينة</a>
                            <button class="btn btn-sm btn-danger" onclick="deleteUser('${userData.id}')">حذف</button>
                        </div>
                        <div class="btn-group edit-mode d-none">
                            <button class="btn btn-sm btn-success" onclick="saveChanges('${userData.id}')">حفظ</button>
                            <button class="btn btn-sm btn-secondary" onclick="cancelEdit('${userData.id}')">إلغاء</button>
                        </div>
                    </td>
                `;

                teachersTableBody.appendChild(newRow);

                // تحديث أرقام الأساتذة بعد الإضافة
                updateTeacherNumbers();

                // إضافة المستخدم إلى قائمة اختيار المستخدمين في نافذة إضافة مشرف جديد
                const userSelect = document.getElementById('user-select');
                if (userSelect) {
                    // إذا كانت الرسالة "لا يوجد مستخدمين متاحين" موجودة، قم بإزالتها
                    if (userSelect.options.length === 1 && userSelect.options[0].disabled && userSelect.options[0].value === '') {
                        userSelect.innerHTML = '';
                        const defaultOption = document.createElement('option');
                        defaultOption.value = '';
                        defaultOption.disabled = true;
                        defaultOption.selected = true;
                        defaultOption.textContent = '-- اختر مستخدم --';
                        userSelect.appendChild(defaultOption);
                    }

                    // إضافة المستخدم إلى القائمة
                    const newOption = document.createElement('option');
                    newOption.value = userData.id;
                    newOption.textContent = `${userData.teacher_name} (${userData.email})`;
                    userSelect.appendChild(newOption);
                }
            }

            // تحديث أرقام الأساتذة بعد الإضافة
            updateTeacherNumbers();

            // تحديث الإحصائيات في لوحة البيانات
            updateStatistics();

            // التأكد من البقاء في قسم إدارة المشرفين
            window.showSection('supervisors-section');
        } else {
            console.error('Error:', data.message || 'حدث خطأ أثناء إزالة الإشراف');
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// Add the new updateStatistics function
function updateStatistics() {
    console.log("بدء تنفيذ دالة تحديث الإحصائيات");

    // Count supervisors (admins that are not the current user)
    const supervisorRows = document.querySelectorAll('#supervisors-section tbody tr[id^="admin-row-"]');
    const supervisorCount = supervisorRows.length;

    // Count teachers (non-admin users)
    const teacherRows = document.querySelectorAll('#teachers-section tbody tr[id^="row-"]');
    const teacherCount = teacherRows.length;

    console.log("عدد المشرفين:", supervisorCount);
    console.log("عدد الأساتذة:", teacherCount);

    // استخدام المعرفات المباشرة للوصول للعناصر
    const supervisorCountElement = document.getElementById('supervisor-count');
    const teacherCountElement = document.getElementById('teacher-count');

    // تحديث الإحصائيات إذا تم العثور على العناصر
    if (supervisorCountElement) {
        supervisorCountElement.textContent = supervisorCount;
        console.log("تم تحديث عدد المشرفين إلى:", supervisorCount);
    } else {
        console.error("لم يتم العثور على عنصر إحصائيات المشرفين");
        // محاولة العثور على العنصر بطريقة بديلة
        const allInfoLabels = document.querySelectorAll('.info-label');
        let found = false;
        allInfoLabels.forEach(label => {
            if (label.textContent.includes('عدد المشرفين')) {
                const valueElement = label.nextElementSibling;
                if (valueElement) {
                    valueElement.textContent = supervisorCount;
                    console.log("تم العثور وتحديث عدد المشرفين بالطريقة البديلة");
                    found = true;
                }
            }
        });
        if (!found) {
            console.error("فشلت جميع محاولات تحديث عدد المشرفين");
        }
    }

    if (teacherCountElement) {
        teacherCountElement.textContent = teacherCount;
        console.log("تم تحديث عدد الأساتذة إلى:", teacherCount);
    } else {
        console.error("لم يتم العثور على عنصر إحصائيات الأساتذة");
        // محاولة العثور على العنصر بطريقة بديلة
        const allInfoLabels = document.querySelectorAll('.info-label');
        let found = false;
        allInfoLabels.forEach(label => {
            if (label.textContent.includes('عدد الأساتذة')) {
                const valueElement = label.nextElementSibling;
                if (valueElement) {
                    valueElement.textContent = teacherCount;
                    console.log("تم العثور وتحديث عدد الأساتذة بالطريقة البديلة");
                    found = true;
                }
            }
        });
        if (!found) {
            console.error("فشلت جميع محاولات تحديث عدد الأساتذة");
        }
    }

    // تحديث أرقام الصفوف
    updateTeacherNumbers();
    updateSupervisorNumbers();

    console.log("انتهاء تنفيذ دالة تحديث الإحصائيات");
}

// دالة لتحديث أرقام الأساتذة بشكل متسلسل
function updateTeacherNumbers() {
    console.log("تحديث أرقام الأساتذة");
    const teacherRows = document.querySelectorAll('#teachers-section tbody tr[id^="row-"]');
    teacherRows.forEach((row, index) => {
        const numberCell = row.querySelector('td:first-child');
        if (numberCell) {
            numberCell.innerHTML = `<span class="row-number">${index + 1}</span>`;
        }
    });
}

// دالة لتحديث أرقام المشرفين بشكل متسلسل
function updateSupervisorNumbers() {
    console.log("تحديث أرقام المشرفين");
    const supervisorRows = document.querySelectorAll('#supervisors-section tbody tr[id^="admin-row-"]');
    supervisorRows.forEach((row, index) => {
        const numberCell = row.querySelector('td:first-child');
        if (numberCell) {
            numberCell.innerHTML = `<span class="row-number">${index + 1}</span>`;
        }
    });
}

function makeAdmin(userId) {
    // عرض مربع حوار لاختيار نوع المشرف
    Swal.fire({
        title: 'إضافة مشرف جديد',
        html: `
            <div class="mb-2">
                <select class="form-select" id="supervisor-type" style="direction: rtl; text-align: right;">
                    <option value="مشرف أول">مشرف أول</option>
                    <option value="مشرف ثاني">مشرف ثاني</option>
                </select>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'تأكيد',
        cancelButtonText: 'إلغاء',
        reverseButtons: true,
        focusConfirm: false,
        customClass: {
            popup: 'supervisor-type-modal',
            title: 'supervisor-type-title',
            confirmButton: 'btn btn-primary',
            cancelButton: 'btn btn-secondary'
        },
        buttonsStyling: false,
        preConfirm: () => {
            return document.getElementById('supervisor-type').value;
        }
    }).then((result) => {
        if (result.isConfirmed) {
            promoteTeSupervisor(userId, result.value);
        }
    });
}

function promoteTeSupervisor(userId, adminType) {
    const csrfToken = document.getElementById('csrf-token').value;

    fetch(`/add_supervisor/${userId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            csrf_token: csrfToken,
            admin_type: adminType
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showMessage('تم إضافة المشرف بنجاح', 'success');

            // الحصول على بيانات الأستاذ
            const teacherRow = document.getElementById(`row-${userId}`);
            if (teacherRow) {
                const teacherName = teacherRow.querySelector('td:nth-child(2) a').textContent.trim();
                const email = teacherRow.querySelector('td:nth-child(4)').textContent.trim();

                // إزالة المستخدم من قائمة الأساتذة
                teacherRow.remove();

                // التحقق من وجود أساتذة آخرين، وإضافة صف "لا يوجد مستخدمين عاديين" إذا لزم الأمر
                const teacherRows = document.querySelectorAll('#teachers-section tbody tr[id^="row-"]');
                if (teacherRows.length === 0) {
                    const teachersTableBody = document.querySelector('#teachers-section tbody');
                    const noTeachersRow = document.createElement('tr');
                    noTeachersRow.innerHTML = `
                        <td colspan="6" class="text-center py-4 text-muted">
                            <i class="fas fa-info-circle me-1"></i> لا يوجد مستخدمين عاديين في النظام
                        </td>
                    `;
                    teachersTableBody.appendChild(noTeachersRow);
                } else {
                    // تحديث أرقام الأساتذة المتبقين
                    updateTeacherNumbers();
                }
            }

            // إزالة صف "لا يوجد مشرفون" إذا كان موجودًا
            const noSupervisorsRow = document.querySelector('#supervisors-section tbody .no-supervisors');
            if (noSupervisorsRow) {
                noSupervisorsRow.remove();
            }

            // إنشاء صف جديد للمشرف
            const supervisorsTableBody = document.querySelector('#supervisors-section tbody');
            const newRow = document.createElement('tr');
            newRow.id = `admin-row-${userId}`;

            // تحديد لون الشارة بناءً على نوع المشرف
            let badgeClass = 'bg-primary';
            if (adminType === 'مشرف أول') {
                badgeClass = 'bg-success';
            } else if (adminType === 'مشرف ثاني') {
                badgeClass = 'bg-info';
            }

            newRow.innerHTML = `
                <td><span class="row-number">0</span></td>
                <td>
                    <a href="/profile/${userId}" class="teacher-name-link">
                        ${teacherName}
                    </a>
                </td>
                <td>${email}</td>
                <td>
                    <span class="badge rounded-pill ${badgeClass}">
                        ${adminType}
                    </span>
                </td>
                <td>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-outline-primary" onclick="changeSupervisorType('${userId}')">
                            <i class="fas fa-exchange-alt me-1"></i> تغيير النوع
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeSupervisor('${userId}')">
                            <i class="fas fa-user-minus me-1"></i> إزالة الإشراف
                        </button>
                    </div>
                </td>
            `;

            supervisorsTableBody.appendChild(newRow);

            // تحديث أرقام المشرفين بعد الإضافة
            updateSupervisorNumbers();

            // إزالة المستخدم من قائمة الاختيار
            const userSelect = document.getElementById('user-select');
            const selectedOption = userSelect.options[userSelect.selectedIndex];

            // إذا لم يعد هناك مستخدمين عاديين، أظهر رسالة
            if (userSelect.options.length <= 1) {
                userSelect.innerHTML = '<option value="" selected disabled>-- لا يوجد مستخدمين متاحين --</option>';
            }

            // تحديث الإحصائيات في لوحة البيانات
            updateStatistics();

            // التأكد من البقاء في قسم إدارة المشرفين
            window.showSection('supervisors-section');
        } else {
            showMessage(data.message || 'حدث خطأ أثناء إضافة المشرف', 'danger');
        }
    })
    .catch(error => {
        showMessage('حدث خطأ أثناء إضافة المشرف', 'danger');
        console.error('Error:', error);
    });
}

// دالة لدمج الخلايا
function mergeCells(button) {
    const row = button.closest('tr');

    // البحث عن خلايا الحصص حسب ترتيبها في الصف
    let lessonCells = Array.from(row.cells).filter(cell => cell.hasAttribute('contenteditable'));

    // إذا وجدنا خليتي الحصص
    if (lessonCells.length >= 2) {
        const firstCell = lessonCells[0];  // الحصة الأولى
        const secondCell = lessonCells[1]; // الحصة الثانية

        // دمج محتوى الخليتين
        const mergedContent = firstCell.textContent + ' ' + secondCell.textContent;
        firstCell.setAttribute('colspan', '2');
        firstCell.textContent = mergedContent;
        secondCell.style.display = 'none';

        // تغيير زر الدمج إلى زر للفصل
        button.innerHTML = '<i class="fas fa-unlink"></i>';
        button.onclick = function() { unMergeCells(this); };
        button.title = 'فصل الخلايا';
    }
}

// دالة لفصل الخلايا
function unMergeCells(button) {
    const row = button.closest('tr');

    // البحث عن خلايا الحصص حسب ترتيبها في الصف
    let lessonCells = Array.from(row.cells).filter(cell =>
        cell.hasAttribute('contenteditable') ||
        (cell.style.display === 'none' && cell !== row.cells[0])
    );

    // نحتاج إلى الخلية المرئية (الحصة الأولى) والخلية المخفية (الحصة الثانية)
    const visibleCells = lessonCells.filter(cell => cell.style.display !== 'none');
    const hiddenCells = lessonCells.filter(cell => cell.style.display === 'none');

    if (visibleCells.length > 0 && hiddenCells.length > 0) {
        const firstCell = visibleCells[0];  // الحصة الأولى (مرئية)
        const secondCell = hiddenCells[0];  // الحصة الثانية (مخفية)

        // إزالة خاصية colspan وإظهار الخلية الثانية
        firstCell.removeAttribute('colspan');
        secondCell.style.display = '';

        // تقسيم المحتوى (بطريقة بسيطة)
        const content = firstCell.textContent;
        const middle = Math.floor(content.length / 2);
        firstCell.textContent = content.substring(0, middle);
        secondCell.textContent = content.substring(middle);

        // إعادة زر الدمج
        button.innerHTML = '<i class="fas fa-link"></i>';
        button.onclick = function() { mergeCells(this); };
        button.title = 'دمج الخلايا';
    }
}

// دالة لحذف صف
function deleteRow(button) {
    const row = button.closest('tr');
    const table = document.getElementById('yearly-progress-table');
    const month = row.getAttribute('data-month');

    // الحصول على جميع الصفوف التي تنتمي لنفس الشهر
    const monthRows = table.querySelectorAll(`tr[data-month="${month}"]:not(.add-row-container)`);

    // إذا كان هذا هو الصف الوحيد المتبقي للشهر، لا نفعل شيئًا ولا نظهر أي رسالة
    if (monthRows.length <= 1) {
        return;
    }

    // حذف الصف مباشرة دون رسالة تأكيد
    row.remove();

    // تحديث rowspan لخلية الشهر
    updateMonthRowspans();

    // إعادة ترقيم الأسابيع بشكل متسلسل
    renumberWeeks();

    // حفظ التغييرات بعد الحذف
    saveAfterDelete();
}

// دالة لإعادة ترقيم الأسابيع بشكل متسلسل حسب الشهور
function renumberWeeks() {
    const table = document.getElementById('yearly-progress-table');
    const allRows = table.querySelectorAll('tr:not(.add-row-container)');

    // ترتيب الأشهر
    const monthOrder = [
        'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر',
        'جانفي', 'فيفري', 'مارس', 'أفريل', 'ماي'
    ];

    // ترتيب الصفوف حسب الشهر
    const sortedRows = Array.from(allRows).sort((a, b) => {
        const monthA = a.getAttribute('data-month');
        const monthB = b.getAttribute('data-month');

        const monthIndexA = monthOrder.indexOf(monthA);
        const monthIndexB = monthOrder.indexOf(monthB);

        if (monthIndexA !== monthIndexB) {
            return monthIndexA - monthIndexB;
        }

        // إذا كان نفس الشهر، نرتب حسب موقع الصف في DOM
        return Array.from(table.rows).indexOf(a) - Array.from(table.rows).indexOf(b);
    });

    // إعادة ترقيم الأسابيع
    let currentWeek = 1;
    let lastMonth = "";

    sortedRows.forEach(row => {
        const month = row.getAttribute('data-month');
        const weekCell = row.querySelector('.week-number');

        if (weekCell) {
            weekCell.textContent = currentWeek.toString();
            row.setAttribute('data-week', currentWeek.toString());
            currentWeek++;
        }
    });
}

// دالة لإضافة صف جديد
function addRow(button) {
    const table = document.getElementById('yearly-progress-table');
    const allRows = table.querySelectorAll('tr:not(.add-row-container)');
    const row = button.closest('tr');
    const month = button.getAttribute('data-month');
    const color = button.getAttribute('data-color');
    const bgColor = button.getAttribute('data-bg-color');

    // ترتيب الأشهر
    const monthOrder = [
        'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر',
        'جانفي', 'فيفري', 'مارس', 'أفريل', 'ماي'
    ];

    let weekNumber;

    // الحصول على جميع صفوف هذا الشهر
    const monthRows = Array.from(allRows).filter(r => r.getAttribute('data-month') === month);

    if (monthRows.length > 0) {
        // إذا كان هناك صفوف في هذا الشهر، نضيف بعد آخر رقم أسبوع في الشهر
        let maxMonthWeek = 0;
        monthRows.forEach(r => {
            const weekNum = parseInt(r.getAttribute('data-week'));
            if (!isNaN(weekNum) && weekNum > maxMonthWeek) {
                maxMonthWeek = weekNum;
            }
        });
        weekNumber = maxMonthWeek + 1;
    } else {
        // إذا كان أول أسبوع في الشهر
        const currentMonthIndex = monthOrder.indexOf(month);

        if (month === 'سبتمبر' && currentMonthIndex === 0) {
            // لشهر سبتمبر (أول شهر في السنة الدراسية)، نبدأ الترقيم من 1

            // نتحقق إذا كان هناك بالفعل رقم 1 في الجدول (في حالة تم حذف بعض الأسابيع)
            const week1Exists = Array.from(allRows).some(r => parseInt(r.getAttribute('data-week')) === 1);

            if (week1Exists) {
                // إذا كان الرقم 1 موجود بالفعل، نبحث عن أقصى رقم أسبوع في الجدول ونضيف 1
                let maxWeekNumber = 0;
                allRows.forEach(r => {
                    const weekNum = parseInt(r.getAttribute('data-week'));
                    if (!isNaN(weekNum) && weekNum > maxWeekNumber) {
                        maxWeekNumber = weekNum;
                    }
                });
                weekNumber = maxWeekNumber + 1;
            } else {
                // بدء الترقيم من 1 لشهر سبتمبر
                weekNumber = 1;
            }
        } else if (currentMonthIndex > 0) {
            // للأشهر الأخرى، نأخذ آخر رقم من الشهر السابق ونضيف 1
            const previousMonth = monthOrder[currentMonthIndex - 1];
            const previousMonthRows = Array.from(allRows).filter(r => r.getAttribute('data-month') === previousMonth);

            if (previousMonthRows.length > 0) {
                // البحث عن أعلى رقم أسبوع في الشهر السابق
                let maxPreviousMonthWeek = 0;
                previousMonthRows.forEach(r => {
                    const weekNum = parseInt(r.getAttribute('data-week'));
                    if (!isNaN(weekNum) && weekNum > maxPreviousMonthWeek) {
                        maxPreviousMonthWeek = weekNum;
                    }
                });
                weekNumber = maxPreviousMonthWeek + 1;
            } else {
                // إذا لم يكن هناك شهر سابق، نبحث عن آخر رقم موجود في جميع الأشهر السابقة
                let maxWeekForPreviousMonths = 0;

                // فحص جميع الأشهر السابقة
                for (let i = 0; i < currentMonthIndex; i++) {
                    const prevMonth = monthOrder[i];
                    const prevMonthRows = Array.from(allRows).filter(r => r.getAttribute('data-month') === prevMonth);

                    prevMonthRows.forEach(r => {
                        const weekNum = parseInt(r.getAttribute('data-week'));
                        if (!isNaN(weekNum) && weekNum > maxWeekForPreviousMonths) {
                            maxWeekForPreviousMonths = weekNum;
                        }
                    });
                }

                weekNumber = maxWeekForPreviousMonths > 0 ? maxWeekForPreviousMonths + 1 : 1;
            }
        } else {
            // في حالة غير متوقعة، ابحث عن أقصى رقم موجود في الجدول
            let maxWeekNumber = 0;
            allRows.forEach(r => {
                const weekNum = parseInt(r.getAttribute('data-week'));
                if (!isNaN(weekNum) && weekNum > maxWeekNumber) {
                    maxWeekNumber = weekNum;
                }
            });
            weekNumber = maxWeekNumber > 0 ? maxWeekNumber + 1 : 1;
        }
    }

    // إنشاء صف جديد
    const newRow = document.createElement('tr');
    newRow.setAttribute('data-month', month);
    newRow.setAttribute('data-week', weekNumber);
    newRow.style.backgroundColor = color;

    // إضافة محتوى الصف
    newRow.innerHTML = `
        <td class="week-number">${weekNumber}</td>
        <td contenteditable="true"></td>
        <td contenteditable="true"></td>
        <td>
            <button type="button" class="btn btn-sm btn-outline-primary merge-btn" onclick="mergeCells(this)">
                <i class="fas fa-link"></i>
            </button>
            <button type="button" class="btn btn-sm btn-outline-danger delete-row-btn" onclick="deleteRow(this)">
                <i class="fas fa-trash-alt"></i>
            </button>
            <button type="button" class="btn btn-sm btn-outline-success add-row-btn" onclick="addRow(this)" data-month="${month}" data-color="${color}" data-bg-color="${bgColor}">
                <i class="fas fa-plus"></i>
            </button>
        </td>
    `;

    // إدراج الصف الجديد بعد الصف الحالي
    row.parentNode.insertBefore(newRow, row.nextSibling);

    // تحديث rowspan لخلايا الشهر
    updateMonthRowspans();
}

// دالة لتحديث قيم rowspan لخلايا الشهور
function updateMonthRowspans() {
    const table = document.getElementById('yearly-progress-table');
    const allRows = table.querySelectorAll('tr:not(.add-row-container)');

    // تحصيل قائمة بجميع الشهور
    const months = new Set();
    allRows.forEach(row => {
        const month = row.getAttribute('data-month');
        if (month) {
            months.add(month);
        }
    });

    // لكل شهر، حساب عدد الصفوف وتحديث rowspan
    months.forEach(month => {
        const monthRows = table.querySelectorAll(`tr[data-month="${month}"]:not(.add-row-container)`);
        if (monthRows.length > 0) {
            // حدد الصف الأول من الشهر
            const firstMonthRow = monthRows[0];

            // البحث عن خلية الشهر التي تحتاج للتحديث
            const monthCells = table.querySelectorAll('.month-cell');
            let monthCell = null;

            // البحث عن خلية الشهر المناسبة
            for (let i = 0; i < monthCells.length; i++) {
                if (monthCells[i].textContent === month) {
                    monthCell = monthCells[i];
                    break;
                }
            }

            // إذا لم يتم العثور على خلية الشهر، قم بإنشاء واحدة جديدة
            if (!monthCell) {
                // تحديد الألوان حسب الشهر
                let bgColor = "";
                switch(month) {
                    case "سبتمبر": bgColor = "rgba(255, 193, 7, 0.3)"; break;
                    case "أكتوبر": bgColor = "rgba(40, 167, 69, 0.3)"; break;
                    case "نوفمبر": bgColor = "rgba(23, 162, 184, 0.3)"; break;
                    case "ديسمبر": bgColor = "rgba(220, 53, 69, 0.3)"; break;
                    // يمكن إضافة المزيد من الشهور هنا
                    default: bgColor = "rgba(108, 117, 125, 0.3)";
                }

                // إنشاء خلية شهر جديدة
                const newMonthCell = document.createElement('td');
                newMonthCell.className = "align-middle fw-bold month-cell";
                newMonthCell.style.writingMode = "vertical-lr";
                newMonthCell.style.transform = "rotate(180deg)";
                newMonthCell.style.backgroundColor = bgColor;
                newMonthCell.style.width = "60px";
                newMonthCell.textContent = month;

                // إضافة الخلية الجديدة إلى الصف الأول
                firstMonthRow.insertBefore(newMonthCell, firstMonthRow.firstChild);

                // استخدام الخلية الجديدة للخطوة التالية
                monthCell = newMonthCell;
            }

            // تحديث rowspan
            if (monthCell) {
                monthCell.setAttribute('rowspan', monthRows.length);
            }
        }
    });
}

// دالة تحديث أرقام الأسابيع
function updateWeekNumbers() {
    const table = document.getElementById('yearly-progress-table');
    const allRows = table.querySelectorAll('tr:not(.add-row-container)');

    allRows.forEach((row, index) => {
        // تحديث رقم الأسبوع
        const weekCell = row.querySelector('.week-number');
        if (weekCell) {
            const weekNumber = index + 1;
            weekCell.textContent = weekNumber.toString();
            row.setAttribute('data-week', weekNumber.toString());
        }
    });
}

// عند تحميل المستند، نضيف مستمعي الأحداث
document.addEventListener('DOMContentLoaded', function() {
    // تأكد من تهيئة الجدول
    if (document.getElementById('yearly-progress-table')) {
        // تحديث rowspan للشهور
        updateMonthRowspans();

        // حذف جميع صفوف أزرار الإضافة (add-row-container)
        const addRowContainers = document.querySelectorAll('.add-row-container');
        addRowContainers.forEach(function(container) {
            container.remove();
        });

        // إعادة ترقيم الأسابيع
        renumberWeeks();
    }
});

// تحديث دالة loadYearlyProgressData لتتوافق مع التغييرات في الواجهة
function loadYearlyProgressData(year) {
    // إذا لم يتم تمرير السنة، نستخدم السنة المحددة حالياً
    if (!year) {
        year = parseInt(document.getElementById('year-selector').value);
    }

    // تغيير حالة واجهة المستخدم لإظهار أنه يتم تحميل البيانات
    const table = document.getElementById('yearly-progress-table');
    if (!table) return;

    // إضافة مؤشر التحميل
    table.innerHTML = '<tr><td colspan="4" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>جاري تحميل البيانات...</td></tr>';

    // استخدام API للحصول على بيانات محدثة من الخادم
    fetch(`/get-yearly-progress/${year}`)
        .then(response => response.json())
        .then(response => {
            if (response.status === 'success') {
                const progressItems = response.data;

                // حذف جميع الصفوف الحالية
                table.innerHTML = '';

                // ترتيب الأشهر
                const monthOrder = [
                    'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر',
                    'جانفي', 'فيفري', 'مارس', 'أفريل', 'ماي'
                ];

                // جمع البيانات حسب الشهر
                const monthsData = {};
                monthOrder.forEach(month => {
                    monthsData[month] = progressItems.filter(item => item.month === month);
                });

                // عرض بيانات كل شهر
                monthOrder.forEach((month, index) => {
                    const monthItems = monthsData[month];
                    if (monthItems && monthItems.length > 0) {
                        addMonthToTable(table, month, index, monthItems);
                    } else {
                        // إذا لم تكن هناك بيانات للشهر، نضيف صفوفًا افتراضية
                        const colors = getMonthColors(month);
                        addDefaultMonth(table, month, colors.background, colors.border, (index * 4) + 1);
                    }
                });

                // تحديث rowspan للشهور
                updateMonthRowspans();

                // تهيئة خصائص التفاعل في الجدول
                setupTableInteractivity();
            } else {
                // إذا كان هناك خطأ، نظهر رسالة الخطأ
                table.innerHTML = `<tr><td colspan="4" class="text-center text-danger"><i class="fas fa-exclamation-triangle me-2"></i>${response.message || 'حدث خطأ أثناء تحميل البيانات'}</td></tr>`;
            }
        })
        .catch(error => {
            console.error('Error loading yearly progress:', error);
            table.innerHTML = '<tr><td colspan="4" class="text-center text-danger"><i class="fas fa-exclamation-triangle me-2"></i>حدث خطأ أثناء الاتصال بالخادم</td></tr>';
        });
}

// وظيفة للحصول على ألوان الشهر
function getMonthColors(month) {
    const colorMap = {
        'سبتمبر': { background: 'rgba(255, 193, 7, 0.1)', border: 'rgba(255, 193, 7, 0.3)' },
        'أكتوبر': { background: 'rgba(40, 167, 69, 0.1)', border: 'rgba(40, 167, 69, 0.3)' },
        'نوفمبر': { background: 'rgba(23, 162, 184, 0.1)', border: 'rgba(23, 162, 184, 0.3)' },
        'ديسمبر': { background: 'rgba(220, 53, 69, 0.1)', border: 'rgba(220, 53, 69, 0.3)' },
        'جانفي': { background: 'rgba(111, 66, 193, 0.1)', border: 'rgba(111, 66, 193, 0.3)' },
        'فيفري': { background: 'rgba(32, 201, 151, 0.1)', border: 'rgba(32, 201, 151, 0.3)' },
        'مارس': { background: 'rgba(253, 126, 20, 0.1)', border: 'rgba(253, 126, 20, 0.3)' },
        'أفريل': { background: 'rgba(13, 202, 240, 0.1)', border: 'rgba(13, 202, 240, 0.3)' },
        'ماي': { background: 'rgba(102, 16, 242, 0.1)', border: 'rgba(102, 16, 242, 0.3)' }
    };
    return colorMap[month] || { background: 'rgba(200, 200, 200, 0.1)', border: 'rgba(200, 200, 200, 0.3)' };
}

// وظيفة لإضافة شهر إلى الجدول بناءً على البيانات المتوفرة
function addMonthToTable(table, month, monthIndex, monthItems) {
    const colors = getMonthColors(month);

    let isFirstRow = true;
    let weekCount = 0;

    // إضافة صفوف لكل أسبوع في الشهر
    monthItems.forEach((item, index) => {
        const row = document.createElement('tr');
        row.setAttribute('data-month', month);
        row.setAttribute('data-week', item.week);
        row.style.backgroundColor = colors.background;
        row.style.borderBottom = `1px solid ${colors.border}`;

        // إضافة خلية الشهر فقط للصف الأول
        if (isFirstRow) {
            const monthCell = document.createElement('td');
            monthCell.className = 'month-cell';
            monthCell.rowSpan = monthItems.length;
            monthCell.textContent = month;
            monthCell.style.backgroundColor = colors.border;
            row.appendChild(monthCell);
            isFirstRow = false;
        }

        // إضافة خلية رقم الأسبوع
        const weekCell = document.createElement('td');
        weekCell.className = 'week-number';
        weekCell.textContent = item.week;
        row.appendChild(weekCell);

        // إضافة خلايا الحصص
        if (item.isMerged) {
            const lessonCell = document.createElement('td');
            lessonCell.setAttribute('contenteditable', 'true');
            lessonCell.setAttribute('colspan', '2');
            lessonCell.className = 'editable-cell merged-cell';
            lessonCell.textContent = item.firstLesson;
            row.appendChild(lessonCell);
        } else {
            const firstLessonCell = document.createElement('td');
            firstLessonCell.setAttribute('contenteditable', 'true');
            firstLessonCell.className = 'editable-cell';
            firstLessonCell.textContent = item.firstLesson;
            row.appendChild(firstLessonCell);

            const secondLessonCell = document.createElement('td');
            secondLessonCell.setAttribute('contenteditable', 'true');
            secondLessonCell.className = 'editable-cell';
            secondLessonCell.textContent = item.secondLesson;
            row.appendChild(secondLessonCell);
        }

        // إضافة الصف إلى الجدول
        table.appendChild(row);
        weekCount++;
    });

    // إضافة صف لزر إضافة أسبوع جديد
    const addRow = document.createElement('tr');
    addRow.className = 'add-row-container';
    addRow.setAttribute('data-month', month);

    const addButtonCell = document.createElement('td');
    addButtonCell.setAttribute('colspan', '4');
    addButtonCell.style.backgroundColor = colors.background;

    const addButton = document.createElement('button');
    addButton.className = 'btn btn-sm btn-outline-secondary add-week-btn w-100';
    addButton.innerHTML = '<i class="fas fa-plus me-1"></i> إضافة أسبوع';
    addButton.onclick = function() {
        addNewWeek(month, monthIndex * 4 + weekCount + 1);
    };

    addButtonCell.appendChild(addButton);
    addRow.appendChild(addButtonCell);
    table.appendChild(addRow);
}

// وظيفة لتهيئة خصائص التفاعل في الجدول
function setupTableInteractivity() {
    // إضافة مستمعي الأحداث للأزرار والخلايا القابلة للتحرير
    const editableCells = document.querySelectorAll('.editable-cell');
    editableCells.forEach(cell => {
        // منع انتقال السطر عند الضغط على Enter
        cell.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.blur();
            }
        });
    });
}

// وظيفة لإعادة تعيين الجدول إلى الوضع الافتراضي
function resetTableToDefault() {
    const table = document.getElementById('yearly-progress-table');
    if (table) {
        // حذف جميع الصفوف الحالية
        const rows = table.querySelectorAll('tr');
        rows.forEach(row => {
            row.remove();
        });

        // إضافة شهر سبتمبر الافتراضي
        addDefaultMonth(table, 'سبتمبر', 'rgba(255, 193, 7, 0.1)', 'rgba(255, 193, 7, 0.3)', 1);

        // إضافة شهر أكتوبر الافتراضي
        addDefaultMonth(table, 'أكتوبر', 'rgba(40, 167, 69, 0.1)', 'rgba(40, 167, 69, 0.3)', 5);

        // إضافة باقي الأشهر بنفس الطريقة
        addDefaultMonth(table, 'نوفمبر', 'rgba(23, 162, 184, 0.1)', 'rgba(23, 162, 184, 0.3)', 9);
        addDefaultMonth(table, 'ديسمبر', 'rgba(220, 53, 69, 0.1)', 'rgba(220, 53, 69, 0.3)', 13);
        addDefaultMonth(table, 'جانفي', 'rgba(111, 66, 193, 0.1)', 'rgba(111, 66, 193, 0.3)', 17);
        addDefaultMonth(table, 'فيفري', 'rgba(32, 201, 151, 0.1)', 'rgba(32, 201, 151, 0.3)', 21);
        addDefaultMonth(table, 'مارس', 'rgba(253, 126, 20, 0.1)', 'rgba(253, 126, 20, 0.3)', 25);
        addDefaultMonth(table, 'أفريل', 'rgba(13, 202, 240, 0.1)', 'rgba(13, 202, 240, 0.3)', 29);
        addDefaultMonth(table, 'ماي', 'rgba(102, 16, 242, 0.1)', 'rgba(102, 16, 242, 0.3)', 33);

        // تحديث هيكل الجدول
        updateMonthRowspans();
    }
}

// وظيفة لإضافة شهر افتراضي إلى الجدول
function addDefaultMonth(table, monthName, bgColor, headerBgColor, startWeek) {
    for (let i = 0; i < 4; i++) {
        const row = document.createElement('tr');
        row.setAttribute('data-month', monthName);
        row.setAttribute('data-week', (startWeek + i).toString());
        row.style.backgroundColor = bgColor;

        // إنشاء خلية الشهر فقط للصف الأول من كل شهر
        let monthCell = '';
        if (i === 0) {
            monthCell = `<td rowspan="4" class="align-middle fw-bold month-cell" style="writing-mode: vertical-lr; transform: rotate(180deg); background-color: ${headerBgColor}; width: 60px;">${monthName}</td>`;
        }

        // إضافة محتوى الصف
        row.innerHTML = `
            ${monthCell}
            <td class="week-number">${startWeek + i}</td>
            <td contenteditable="true"></td>
            <td contenteditable="true"></td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-primary merge-btn" onclick="mergeCells(this)">
                    <i class="fas fa-link"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-danger delete-row-btn" onclick="deleteRow(this)">
                    <i class="fas fa-trash-alt"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-success add-row-btn" onclick="addRow(this)" data-month="${monthName}" data-color="${bgColor}" data-bg-color="${headerBgColor}">
                    <i class="fas fa-plus"></i>
                </button>
            </td>
        `;

        // إضافة الصف إلى الجدول
        table.appendChild(row);
    }

    // إضافة صف إضافة أسبوع جديد
    const addRowContainer = document.createElement('tr');
    addRowContainer.className = 'add-row-container';
    addRowContainer.setAttribute('data-month', monthName);
    addRowContainer.style.backgroundColor = bgColor.replace('0.1', '0.2');
    addRowContainer.innerHTML = `
        <td colspan="5" class="text-center py-1">
            <button type="button" class="btn btn-sm btn-success add-row-btn" onclick="addRow(this)" data-month="${monthName}" data-color="${bgColor}" data-bg-color="${headerBgColor}">
                <i class="fas fa-plus-circle"></i> إضافة أسبوع
            </button>
        </td>
    `;
    table.appendChild(addRowContainer);
}

document.addEventListener('DOMContentLoaded', function() {
    // تحميل البيانات للسنة الأولى افتراضياً
    loadYearlyProgressData(1);

    // إضافة مستمع حدث لتغيير السنة الدراسية
    const yearSelector = document.getElementById('year-selector');
    if (yearSelector) {
        yearSelector.addEventListener('change', function() {
            const selectedYear = parseInt(this.value);
            loadYearlyProgressData(selectedYear);
        });
    }

    // البحث عن زر الحفظ وإضافة معالج الحدث
    const saveButton = document.getElementById('save-progress-btn');
    if (saveButton) {
        saveButton.addEventListener('click', saveYearlyProgress);
    }

    // إضافة معالج الحدث لزر تفريغ الحقول
    const clearButton = document.getElementById('clear-lessons-btn');
    if (clearButton) {
        clearButton.addEventListener('click', clearLessonFields);
    }
});

// إضافة كود البحث للأساتذة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة حقول البحث إذا كانت موجودة
    const searchTeacher = document.getElementById('search-teacher');
    const searchWorkplace = document.getElementById('search-workplace');

    if (searchTeacher && searchWorkplace) {
        // إضافة مستمعي الأحداث للبحث الفوري
        searchTeacher.addEventListener('input', filterTeachers);
        searchWorkplace.addEventListener('input', filterTeachers);
    }

    // وظيفة تصفية قائمة الأساتذة
    function filterTeachers() {
        const teacherValue = searchTeacher.value.trim().toLowerCase();
        const workplaceValue = searchWorkplace.value.trim().toLowerCase();

        // الحصول على جميع صفوف الأساتذة
        const rows = document.querySelectorAll('#teachers-section tbody tr[id^="row-"]');
        let visibleCount = 0;

        rows.forEach(row => {
            const teacherName = row.getAttribute('data-teacher-name').toLowerCase();
            const workplace = row.getAttribute('data-workplace').toLowerCase();

            // التحقق من تطابق كلا الشرطين
            const matchesTeacher = teacherName.includes(teacherValue);
            const matchesWorkplace = workplace.includes(workplaceValue);

            // إظهار/إخفاء الصف بناءً على نتائج البحث
            if (matchesTeacher && matchesWorkplace) {
                row.classList.remove('d-none');
                visibleCount++;
            } else {
                row.classList.add('d-none');
            }
        });

        // إظهار رسالة "لا توجد نتائج" إذا لم يتم العثور على نتائج
        const noResultsRow = document.getElementById('no-results-row');
        if (noResultsRow) {
            if (visibleCount === 0 && (teacherValue.length > 0 || workplaceValue.length > 0)) {
                noResultsRow.classList.remove('d-none');
            } else {
                noResultsRow.classList.add('d-none');
            }
        }

        // تحديث أرقام الصفوف المرئية
        updateVisibleRowNumbers();
    }

    // وظيفة تحديث أرقام الصفوف المرئية
    function updateVisibleRowNumbers() {
        const visibleRows = document.querySelectorAll('#teachers-section tbody tr[id^="row-"]:not(.d-none)');
        visibleRows.forEach((row, index) => {
            const rowNumber = row.querySelector('.row-number');
            if (rowNumber) {
                rowNumber.textContent = index + 1;
            }
        });
    }
});

// وظيفة لحفظ التغييرات بعد حذف الأسبوع
function saveAfterDelete() {
    // الحصول على السنة المحددة
    const selectedYear = parseInt(document.getElementById('year-selector').value);

    // إعادة ترقيم الأسابيع للتأكد من أنها متسلسلة قبل الحفظ
    renumberWeeks();

    // الحصول على بيانات الجدول
    const table = document.getElementById('yearly-progress-table');
    const rows = table.querySelectorAll('tr:not(.add-row-container)');

    // التحقق من وجود صفوف في الجدول
    if (rows.length === 0) {
        return;
    }

    const progressData = [];

    // حفظ معلومات هيكل الجدول
    const tableStructure = {
        deletedWeeks: [],
        maxWeek: 0
    };

    // تحديد أقصى رقم أسبوع
    rows.forEach(row => {
        const weekNumber = parseInt(row.getAttribute('data-week'));
        if (weekNumber > tableStructure.maxWeek) {
            tableStructure.maxWeek = weekNumber;
        }
    });

    // تحديد الأسابيع المحذوفة (الأرقام المفقودة)
    for (let i = 1; i <= tableStructure.maxWeek; i++) {
        const weekExists = Array.from(rows).some(row => parseInt(row.getAttribute('data-week')) === i);
        if (!weekExists) {
            tableStructure.deletedWeeks.push(i);
        }
    }

    // معالجة كل صف في الجدول
    rows.forEach(row => {
        const weekNumber = parseInt(row.getAttribute('data-week'));
        const month = row.getAttribute('data-month');

        // الحصول على خلايا المحتوى (الحصص)
        const contentCells = row.querySelectorAll('[contenteditable="true"]');

        // التحقق مما إذا كانت الخلايا مدمجة
        const isMerged = contentCells[0].hasAttribute('colspan');

        let firstLesson = contentCells[0].textContent.trim();
        let secondLesson = isMerged ? "" : contentCells[1].textContent.trim();

        // إضافة البيانات إلى المصفوفة
        progressData.push({
            week: weekNumber,
            month: month,
            firstLesson: firstLesson,
            secondLesson: secondLesson,
            isMerged: isMerged
        });
    });

    // تأكد من ترتيب البيانات حسب رقم الأسبوع
    progressData.sort((a, b) => a.week - b.week);

    // التحقق من وجود رمز CSRF في الصفحة
    const csrfToken = document.querySelector('input[name="csrf_token"]').value;

    // إرسال البيانات إلى الخادم باستخدام طلب Fetch API
    fetch('/save-yearly-progress', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            year: selectedYear,
            data: progressData,
            tableStructure: tableStructure
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // تخزين السنة الحالية لإعادة تحميلها بعد الحفظ
            const currentYear = selectedYear;

            // حفظ مؤقت للبيانات المحلية - سيتم استبدالها عند إعادة تحميل البيانات
            window.latestYearlyProgressData = {
                year: currentYear,
                data: progressData
            };
        }
    })
    .catch(error => {
        console.error('Error saving changes after delete:', error);
    });
}
</script>

<!-- Modal إضافة مشرف جديد -->
<div class="modal fade" id="addSupervisorModal" tabindex="-1" aria-labelledby="addSupervisorModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white py-2">
        <h5 class="modal-title" id="addSupervisorModalLabel">إضافة مشرف جديد</h5>
        <button type="button" class="btn-close text-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body py-3">
        <form id="add-supervisor-form">
          <div class="mb-2">
            <select class="form-select" id="user-select" required>
              <option value="" selected disabled>-- اختر مستخدم --</option>
              {% for user in users %}
              {% if not user.is_admin %}
              <option value="{{ user.id }}">{{ user.teacher_name }} ({{ user.email }})</option>
              {% endif %}
              {% endfor %}
            </select>
          </div>
          <div class="mb-2">
            <select class="form-select" id="supervisor-type-select" required>
              <option value="مشرف أول">مشرف أول</option>
              <option value="مشرف ثاني">مشرف ثاني</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer py-2">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
        <button type="button" class="btn btn-primary" onclick="addSupervisor()">إضافة</button>
      </div>
    </div>
  </div>
</div>

<script>
function addSupervisor() {
    const userId = document.getElementById('user-select').value;
    const adminType = document.getElementById('supervisor-type-select').value;

    if (!userId) {
        showMessage('يرجى اختيار مستخدم', 'danger');
        return;
    }

    const csrfToken = document.getElementById('csrf-token').value;

    fetch(`/add_supervisor/${userId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            csrf_token: csrfToken,
            admin_type: adminType
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('addSupervisorModal'));
            modal.hide();

            // إضافة المشرف الجديد إلى الجدول
            const userSelect = document.getElementById('user-select');
            const selectedOption = userSelect.options[userSelect.selectedIndex];
            const teacherName = selectedOption.textContent.split('(')[0].trim();
            const email = selectedOption.textContent.match(/\((.*?)\)/)[1];

            // إزالة رسالة "لا يوجد مشرفون" إذا كانت موجودة
            const noSupervisorsRow = document.querySelector('#supervisors-section tbody .no-supervisors');
            if (noSupervisorsRow) {
                noSupervisorsRow.remove();
            }

            // عد عدد الصفوف الحالية
            const existingRows = document.querySelectorAll('#supervisors-section tbody tr');
            const rowCount = existingRows.length;

            // إنشاء صف جديد للمشرف
            const supervisorsTableBody = document.querySelector('#supervisors-section tbody');
            const newRow = document.createElement('tr');
            newRow.id = `admin-row-${userId}`;

            // تحديد لون الشارة بناءً على نوع المشرف
            let badgeClass = 'bg-primary';
            if (adminType === 'مشرف أول') {
                badgeClass = 'bg-success';
            } else if (adminType === 'مشرف ثاني') {
                badgeClass = 'bg-info';
            }

            newRow.innerHTML = `
                <td><span class="row-number">${rowCount + 1}</span></td>
                <td>
                    <a href="/profile/${userId}" class="teacher-name-link">
                        ${teacherName}
                    </a>
                </td>
                <td>${email}</td>
                <td>
                    <span class="badge rounded-pill ${badgeClass}">
                        ${adminType}
                    </span>
                </td>
                <td>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-outline-primary" onclick="changeSupervisorType('${userId}')">
                            <i class="fas fa-exchange-alt me-1"></i> تغيير النوع
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeSupervisor('${userId}')">
                            <i class="fas fa-user-minus me-1"></i> إزالة الإشراف
                        </button>
                    </div>
                </td>
            `;

            supervisorsTableBody.appendChild(newRow);

            // تحديث أرقام المشرفين
            updateSupervisorNumbers();

            // إزالة المستخدم من قائمة الاختيار
            userSelect.removeChild(selectedOption);

            // حذف الصف من قائمة الأساتذة لأنه أصبح مشرف
            const teacherRow = document.getElementById(`row-${userId}`);
            if (teacherRow) {
                teacherRow.remove();

                // التحقق من وجود أساتذة آخرين، وإضافة صف "لا يوجد مستخدمين عاديين" إذا لزم الأمر
                const teacherRows = document.querySelectorAll('#teachers-section tbody tr[id^="row-"]');
                if (teacherRows.length === 0) {
                    const teachersTableBody = document.querySelector('#teachers-section tbody');
                    const noTeachersRow = document.createElement('tr');
                    noTeachersRow.innerHTML = `
                        <td colspan="6" class="text-center py-4 text-muted">
                            <i class="fas fa-info-circle me-1"></i> لا يوجد مستخدمين عاديين في النظام
                        </td>
                    `;
                    teachersTableBody.appendChild(noTeachersRow);
                } else {
                    // تحديث أرقام الأساتذة المتبقين
                    updateTeacherNumbers();
                }
            }

            // إذا لم يعد هناك مستخدمين عاديين، أظهر رسالة
            if (userSelect.options.length <= 1) {
                userSelect.innerHTML = '<option value="" selected disabled>-- لا يوجد مستخدمين متاحين --</option>';
            }

            // تحديث الإحصائيات في لوحة البيانات
            updateStatistics();

            // التأكد من البقاء في قسم إدارة المشرفين
            window.showSection('supervisors-section');
        } else {
            showMessage(data.message || 'حدث خطأ أثناء إضافة المشرف', 'danger');
        }
    })
    .catch(error => {
        showMessage('حدث خطأ أثناء إضافة المشرف', 'danger');
        console.error('Error:', error);
    });
}
</script>

<style>
.teacher-name-link {
    color: #1e88e5;
    text-decoration: none;
    transition: all 0.3s ease;
}

.teacher-name-link:hover {
    color: #1565c0;
    text-decoration: none;
    transform: translateX(-3px);
}

/* تنسيقات بطاقة بيانات المدير */
.admin-img {
    width: 120px;
    height: 120px;
    font-size: 45px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.08);
    border: 3px solid white;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.admin-info-section {
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 8px;
    height: 100%;
}

.section-title {
    font-size: 16px;
    color: #0d6efd;
    border-bottom: 1px solid #ddd;
    padding-bottom: 8px;
    margin-bottom: 10px;
}

.admin-info-item {
    margin-bottom: 8px;
    padding: 8px;
    background-color: #fff;
    border-radius: 4px;
    transition: all 0.3s;
}

.admin-info-item:hover {
    background-color: #e9f0ff;
}

.info-label {
    display: inline-block;
    width: 120px;
    font-size: 14px;
    color: #6c757d;
}

.info-value {
    font-weight: 600;
    color: #212529;
}

.admin-info-container {
    height: 100%;
}

/* تنسيق أزرار التنقل */
.active-section {
    background-color: #0d6efd !important;
    color: white !important;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* تنسيق أرقام الإحصائيات */
.stats-badge {
    display: inline-block;
    min-width: 30px;
    padding: 4px 8px;
    font-size: 14px;
    font-weight: bold;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    border-radius: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* تنسيق أرقام الصفوف في الجداول */
.row-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background-color: #0d6efd;
    color: white;
    font-weight: bold;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 0 auto;
}

/* تخصيص حجم مربع حوار SweetAlert2 */
.swal2-popup {
    width: 25em !important;
    padding: 1.5em !important;
    min-height: auto !important;
}

.swal2-title {
    font-size: 1.2em !important;
    margin-bottom: 0.5em !important;
}

.swal2-html-container {
    font-size: 0.9em !important;
    margin: 0.5em 0 !important;
}

.swal2-actions {
    margin-top: 0.5em !important;
    display: flex !important;
    justify-content: center !important;
}

.swal2-actions button {
    padding: 0.4em 1em !important;
    font-size: 0.9em !important;
}

.swal2-actions button.swal2-confirm,
.swal2-actions button.swal2-cancel {
    margin: 0 40px !important;
}

/* تخصيص حجم النموذج داخل مربع الحوار */
.swal2-popup .form-select {
    font-size: 0.9em !important;
    padding: 0.4em !important;
}

.swal2-popup .form-label {
    font-size: 0.9em !important;
    margin-bottom: 0.3em !important;
}

/* تخصيص حجم أيقونة التحذير */
.swal2-icon {
    width: 3em !important;
    height: 3em !important;
    margin: 0.5em auto 0.5em !important;
}

.swal2-icon .swal2-icon-content {
    font-size: 1.5em !important;
}

.swal2-icon.swal2-warning {
    border-color: #ffc107 !important;
    color: #ffc107 !important;
}

.swal2-icon.swal2-warning .swal2-icon-content {
    font-size: 1.5em !important;
}

/* تقليل المسافات الداخلية */
.swal2-popup .swal2-icon {
    margin: 0.5em auto 0.5em !important;
}

.swal2-popup .swal2-title {
    margin-bottom: 0.3em !important;
}

.swal2-popup .swal2-html-container {
    margin: 0.3em 0 !important;
}

.swal2-popup .swal2-actions {
    margin-top: 0.3em !important;
}

/* تنسيق جدول التدرج السنوي */
.yearly-progress-table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
}

.yearly-progress-table td {
    padding: 5px 8px;
    vertical-align: middle;
}

.yearly-progress-table td[contenteditable="true"] {
    min-height: 38px;
    height: auto;
    white-space: normal;
    word-break: break-word;
    overflow-wrap: break-word;
    text-align: center;
    direction: rtl;
    max-height: 150px;
    overflow-y: auto;
}

.yearly-progress-table td[contenteditable="true"]:focus {
    outline: 2px solid #0d6efd;
    outline-offset: -2px;
}

.yearly-progress-table .week-number {
    width: 36px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    font-weight: bold;
    color: #495057;
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: 50%;
}

.yearly-progress-table .month-cell {
    padding: 10px 2px;
    font-size: 16px;
    letter-spacing: 1px;
}

.add-row-container {
    height: 40px;
}

.add-row-container .btn {
    padding: 2px 10px;
}

.yearly-progress-table .btn-sm {
    padding: 0.2rem 0.4rem;
    font-size: 0.75rem;
}
</style>

<!-- إضافة مكتبة SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- سكريبت التنقل بين الأقسام -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // إضافة معالجات الأحداث لأزرار التحكم في القسم
    document.getElementById('admin-info-btn').addEventListener('click', function() {
        window.showSection('admin-info-section');
    });

    document.getElementById('supervisors-btn').addEventListener('click', function() {
        window.showSection('supervisors-section');
        // تحديث أرقام المشرفين عند عرض قسم المشرفين
        updateSupervisorNumbers();
    });

    document.getElementById('teachers-btn').addEventListener('click', function() {
        window.showSection('teachers-section');
        // تحديث أرقام الأساتذة عند عرض قسم الأساتذة
        updateTeacherNumbers();
    });

    document.getElementById('yearly-progress-btn').addEventListener('click', function() {
        window.showSection('yearly-progress-section');
    });

    // تحديث أرقام المشرفين والأساتذة عند تحميل الصفحة
    updateSupervisorNumbers();
    updateTeacherNumbers();

    // تحميل بيانات التدرجات السنوية المحفوظة
    loadYearlyProgressData();
});

// وظيفة لإظهار/إخفاء الأقسام
window.showSection = function(sectionId) {
    // إخفاء جميع الأقسام أولاً
    document.querySelectorAll('.section-content').forEach(function(section) {
            section.classList.add('d-none');
        });

        // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.nav-button').forEach(function(btn) {
            btn.classList.remove('active-section');
        });

    // إظهار القسم المطلوب
    document.getElementById(sectionId).classList.remove('d-none');

    // الحصول على معرف الزر المناسب
    let btnId = 'admin-info-btn'; // افتراضي

    if (sectionId === 'supervisors-section') {
        btnId = 'supervisors-btn';
    } else if (sectionId === 'teachers-section') {
        btnId = 'teachers-btn';
    } else if (sectionId === 'yearly-progress-section') {
        btnId = 'yearly-progress-btn';
    }

    // إضافة الفئة النشطة إلى الزر المناسب
    document.getElementById(btnId).classList.add('active-section');
}

// عند تحميل المستند، نضيف مستمعي الأحداث
document.addEventListener('DOMContentLoaded', function() {
    // تأكد من تهيئة الجدول
    if (document.getElementById('yearly-progress-table')) {
        // تحديث rowspan للشهور
        updateMonthRowspans();

        // حذف جميع صفوف أزرار الإضافة (add-row-container)
        const addRowContainers = document.querySelectorAll('.add-row-container');
        addRowContainers.forEach(function(container) {
            container.remove();
        });

        // إعادة ترقيم الأسابيع
        renumberWeeks();
    }
});

// وظيفة لتحميل بيانات التدرجات السنوية المحفوظة
function loadYearlyProgressData(year) {
    // إذا لم يتم تمرير السنة، نستخدم السنة المحددة حالياً
    if (!year) {
        year = parseInt(document.getElementById('year-selector').value);
    }

    const yearlyProgressData = {{ yearly_progress|tojson }};

    // التحقق من وجود بيانات للسنة المحددة
    if (yearlyProgressData[year] && yearlyProgressData[year].length > 0) {
        // البحث عن جدول التدرجات السنوية
        const table = document.getElementById('yearly-progress-table');

        if (table) {
            const rows = table.querySelectorAll('tr:not(.add-row-container)');
            const progressItems = yearlyProgressData[year];

            // ترتيب الأشهر
            const monthOrder = [
                'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر',
                'جانفي', 'فيفري', 'مارس', 'أفريل', 'ماي'
            ];

            // ترتيب البيانات حسب الشهر ثم رقم الأسبوع
            progressItems.sort((a, b) => {
                const monthIndexA = monthOrder.indexOf(a.month);
                const monthIndexB = monthOrder.indexOf(b.month);

                if (monthIndexA !== monthIndexB) {
                    return monthIndexA - monthIndexB;
                }

                return a.week - b.week;
            });

            // حذف جميع الصفوف الحالية
            rows.forEach(row => {
                row.remove();
            });

            // إضافة الصفوف بالبيانات المحفوظة
            progressItems.forEach((item) => {
                // تحديد اللون بناءً على الشهر
                let color = 'rgba(255, 193, 7, 0.1)'; // لون افتراضي
                let bgColor = 'rgba(255, 193, 7, 0.3)';

                // تحديد اللون حسب الشهر
                switch (item.month) {
                    case 'سبتمبر':
                        color = 'rgba(255, 193, 7, 0.1)';
                        bgColor = 'rgba(255, 193, 7, 0.3)';
                        break;
                    case 'أكتوبر':
                        color = 'rgba(40, 167, 69, 0.1)';
                        bgColor = 'rgba(40, 167, 69, 0.3)';
                        break;
                    case 'نوفمبر':
                        color = 'rgba(23, 162, 184, 0.1)';
                        bgColor = 'rgba(23, 162, 184, 0.3)';
                        break;
                    case 'ديسمبر':
                        color = 'rgba(220, 53, 69, 0.1)';
                        bgColor = 'rgba(220, 53, 69, 0.3)';
                        break;
                    case 'جانفي':
                        color = 'rgba(111, 66, 193, 0.1)';
                        bgColor = 'rgba(111, 66, 193, 0.3)';
                        break;
                    case 'فيفري':
                        color = 'rgba(32, 201, 151, 0.1)';
                        bgColor = 'rgba(32, 201, 151, 0.3)';
                        break;
                    case 'مارس':
                        color = 'rgba(253, 126, 20, 0.1)';
                        bgColor = 'rgba(253, 126, 20, 0.3)';
                        break;
                    case 'أفريل':
                        color = 'rgba(13, 202, 240, 0.1)';
                        bgColor = 'rgba(13, 202, 240, 0.3)';
                        break;
                    case 'ماي':
                        color = 'rgba(102, 16, 242, 0.1)';
                        bgColor = 'rgba(102, 16, 242, 0.3)';
                        break;
                }

                // إنشاء صف جديد - نضع رقم الأسبوع من البيانات مؤقتاً، ثم نعيد ترقيمه لاحقاً
                const row = document.createElement('tr');
                row.setAttribute('data-month', item.month);
                row.setAttribute('data-week', item.week.toString());
                row.style.backgroundColor = color;

                // إضافة محتوى الصف
                row.innerHTML = `
                    <td class="week-number">${item.week}</td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td>
                        <button type="button" class="btn btn-sm btn-outline-primary merge-btn" onclick="mergeCells(this)">
                            <i class="fas fa-link"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger delete-row-btn" onclick="deleteRow(this)">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-success add-row-btn" onclick="addRow(this)" data-month="${item.month}" data-color="${color}" data-bg-color="${bgColor}">
                            <i class="fas fa-plus"></i>
                        </button>
                    </td>
                `;

                // إضافة الصف إلى الجدول
                table.appendChild(row);

                // الحصول على خلايا الحصة في الصف
                const lessonCells = Array.from(row.cells).filter(cell => cell.hasAttribute('contenteditable'));

                if (lessonCells.length >= 2) {
                    if (item.isMerged) {
                        // إذا كانت الخلايا مدمجة، استخدم الحصة الأولى فقط
                        lessonCells[0].setAttribute('colspan', '2');
                        lessonCells[0].textContent = item.firstLesson;
                        lessonCells[1].style.display = 'none';

                        // تحديث زر الدمج
                        const mergeBtn = row.querySelector('.merge-btn');
                        if (mergeBtn) {
                            mergeBtn.innerHTML = '<i class="fas fa-unlink"></i>';
                            mergeBtn.onclick = function() { unMergeCells(this); };
                            mergeBtn.title = 'فصل الخلايا';
                        }
                    } else {
                        // إذا لم تكن الخلايا مدمجة، استخدم كلتا الخليتين
                        lessonCells[0].textContent = item.firstLesson;
                        lessonCells[1].textContent = item.secondLesson;
                    }
                }
            });

            // تحديث هيكل الجدول بعد تحميل البيانات
            updateMonthRowspans();

            // إعادة ترقيم الأسابيع بشكل متسلسل
            renumberWeeks();
        }
    } else {
        // إعادة تهيئة الجدول إذا لم تكن هناك بيانات
        resetYearlyProgressTable();
    }
}

// وظيفة لإعادة تهيئة جدول التدرج السنوي للوضع الافتراضي عند عدم وجود بيانات
function resetYearlyProgressTable() {
    const table = document.getElementById('yearly-progress-table');
    if (table) {
        // حذف جميع الصفوف الحالية
        const rows = table.querySelectorAll('tr');
        rows.forEach(row => row.remove());

        // إضافة الأشهر الافتراضية مع نفس التنسيق
        // شهر سبتمبر
        addDefaultMonth(table, 'سبتمبر', 'rgba(255, 193, 7, 0.1)', 'rgba(255, 193, 7, 0.3)', 1);
        // شهر أكتوبر
        addDefaultMonth(table, 'أكتوبر', 'rgba(40, 167, 69, 0.1)', 'rgba(40, 167, 69, 0.3)', 5);
        // شهر نوفمبر
        addDefaultMonth(table, 'نوفمبر', 'rgba(23, 162, 184, 0.1)', 'rgba(23, 162, 184, 0.3)', 9);
        // شهر ديسمبر
        addDefaultMonth(table, 'ديسمبر', 'rgba(220, 53, 69, 0.1)', 'rgba(220, 53, 69, 0.3)', 13);
        // شهر جانفي
        addDefaultMonth(table, 'جانفي', 'rgba(111, 66, 193, 0.1)', 'rgba(111, 66, 193, 0.3)', 17);
        // شهر فيفري
        addDefaultMonth(table, 'فيفري', 'rgba(32, 201, 151, 0.1)', 'rgba(32, 201, 151, 0.3)', 21);
        // شهر مارس
        addDefaultMonth(table, 'مارس', 'rgba(253, 126, 20, 0.1)', 'rgba(253, 126, 20, 0.3)', 25);
        // شهر أفريل
        addDefaultMonth(table, 'أفريل', 'rgba(13, 202, 240, 0.1)', 'rgba(13, 202, 240, 0.3)', 29);
        // شهر ماي
        addDefaultMonth(table, 'ماي', 'rgba(102, 16, 242, 0.1)', 'rgba(102, 16, 242, 0.3)', 33);

        // تحديث rowspan للشهور
        updateMonthRowspans();
    }
}

// وظيفة لإضافة شهر افتراضي إلى الجدول
function addDefaultMonth(table, monthName, bgColor, headerBgColor, startWeek) {
    for (let i = 0; i < 4; i++) {
        const row = document.createElement('tr');
        row.setAttribute('data-month', monthName);
        row.setAttribute('data-week', (startWeek + i).toString());
        row.style.backgroundColor = bgColor;

        // إنشاء خلية الشهر فقط للصف الأول من كل شهر
        let monthCell = '';
        if (i === 0) {
            monthCell = `<td rowspan="4" class="align-middle fw-bold month-cell" style="writing-mode: vertical-lr; transform: rotate(180deg); background-color: ${headerBgColor}; width: 60px;">${monthName}</td>`;
        }

        // إضافة محتوى الصف
        row.innerHTML = `
            ${monthCell}
            <td class="week-number">${startWeek + i}</td>
            <td contenteditable="true"></td>
            <td contenteditable="true"></td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-primary merge-btn" onclick="mergeCells(this)">
                    <i class="fas fa-link"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-danger delete-row-btn" onclick="deleteRow(this)">
                    <i class="fas fa-trash-alt"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-success add-row-btn" onclick="addRow(this)" data-month="${monthName}" data-color="${bgColor}" data-bg-color="${headerBgColor}">
                    <i class="fas fa-plus"></i>
                </button>
            </td>
        `;

        // إضافة الصف إلى الجدول
        table.appendChild(row);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // تحميل البيانات الأولية للسنة الأولى
    loadYearlyProgressData(1);

    // إضافة مستمع حدث لتغيير السنة الدراسية
    const yearSelector = document.getElementById('year-selector');
    if (yearSelector) {
        yearSelector.addEventListener('change', function() {
            // استرجاع البيانات المحدثة من الخادم عند تغيير السنة
            const selectedYear = parseInt(this.value);
            loadYearlyProgressData(selectedYear);
        });
    }
});
</script>

<!-- سكريبت حفظ التدرجات السنوية -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // البحث عن زر الحفظ وإضافة معالج الحدث
    const saveButton = document.getElementById('save-progress-btn');
    if (saveButton) {
        saveButton.addEventListener('click', saveYearlyProgress);
    }

    // إضافة معالج الحدث لزر تفريغ الحقول
    const clearButton = document.getElementById('clear-lessons-btn');
    if (clearButton) {
        clearButton.addEventListener('click', clearLessonFields);
    }

    // تحميل البيانات للسنة الأولى افتراضياً
    loadYearlyProgressData(1);

    // إضافة مستمع حدث لتغيير السنة الدراسية
    const yearSelector = document.getElementById('year-selector');
    if (yearSelector) {
        yearSelector.addEventListener('change', function() {
            const selectedYear = parseInt(this.value);
            loadYearlyProgressData(selectedYear);
        });
    }
});

// وظيفة حفظ التدرجات السنوية
function saveYearlyProgress() {
    // الحصول على السنة المحددة
    const selectedYear = parseInt(document.getElementById('year-selector').value);

    // إعادة ترقيم الأسابيع للتأكد من أنها متسلسلة قبل الحفظ
    renumberWeeks();

    // تغيير حالة الزر لإظهار أن الحفظ قيد التقدم
    const saveButton = document.getElementById('save-progress-btn');
    const originalButtonText = saveButton.innerHTML;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري الحفظ...';
    saveButton.disabled = true;

    // الحصول على بيانات الجدول
    const table = document.getElementById('yearly-progress-table');
    const rows = table.querySelectorAll('tr:not(.add-row-container)');

    // التحقق من وجود صفوف في الجدول
    if (rows.length === 0) {
        // إذا لم تكن هناك صفوف، لا داعي للمتابعة
        saveButton.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i> لا توجد بيانات للحفظ';
        setTimeout(() => {
            saveButton.innerHTML = originalButtonText;
            saveButton.disabled = false;
        }, 2000);
        return;
    }

    const progressData = [];

    // حفظ معلومات هيكل الجدول
    const tableStructure = {
        deletedWeeks: [],
        maxWeek: 0
    };

    // تحديد أقصى رقم أسبوع
    rows.forEach(row => {
        const weekNumber = parseInt(row.getAttribute('data-week'));
        if (weekNumber > tableStructure.maxWeek) {
            tableStructure.maxWeek = weekNumber;
        }
    });

    // تحديد الأسابيع المحذوفة (الأرقام المفقودة)
    for (let i = 1; i <= tableStructure.maxWeek; i++) {
        const weekExists = Array.from(rows).some(row => parseInt(row.getAttribute('data-week')) === i);
        if (!weekExists) {
            tableStructure.deletedWeeks.push(i);
        }
    }

    // معالجة كل صف في الجدول
    rows.forEach(row => {
        const weekNumber = parseInt(row.getAttribute('data-week'));
        const month = row.getAttribute('data-month');

        // الحصول على خلايا المحتوى (الحصص)
        const contentCells = row.querySelectorAll('[contenteditable="true"]');

        // التحقق مما إذا كانت الخلايا مدمجة
        const isMerged = contentCells[0].hasAttribute('colspan');

        let firstLesson = contentCells[0].textContent.trim();
        let secondLesson = isMerged ? "" : contentCells[1].textContent.trim();

        // إضافة البيانات إلى المصفوفة
        progressData.push({
            week: weekNumber,
            month: month,
            firstLesson: firstLesson,
            secondLesson: secondLesson,
            isMerged: isMerged
        });
    });

    // تأكد من ترتيب البيانات حسب رقم الأسبوع
    progressData.sort((a, b) => a.week - b.week);

    // التحقق من وجود رمز CSRF في الصفحة
    const csrfToken = document.querySelector('input[name="csrf_token"]').value;

    // إرسال البيانات إلى الخادم باستخدام طلب Fetch API
    fetch('/save-yearly-progress', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            year: selectedYear, // استخدام السنة المحددة
            data: progressData,
            tableStructure: tableStructure // إضافة معلومات هيكل الجدول
        })
    })
    .then(response => response.json())
    .then(data => {
        // إعادة الزر إلى حالته الأصلية بعد نجاح الحفظ
        saveButton.innerHTML = '<i class="fas fa-check me-1"></i> تم الحفظ';

        // تحديث البيانات بعد الحفظ للتأكد من تزامن البيانات بين جميع المستويات
        if (data.status === 'success') {
            // تخزين السنة الحالية لإعادة تحميلها بعد الحفظ
            const currentYear = selectedYear;

            // حفظ مؤقت للبيانات المحلية - سيتم استبدالها عند إعادة تحميل البيانات
            window.latestYearlyProgressData = {
                year: currentYear,
                data: progressData
            };

            // إعادة تحميل البيانات مباشرة بعد الحفظ للتأكد من التزامن الفوري
            loadYearlyProgressData(currentYear);
        }

        // تغيير الزر مرة أخرى بعد ثانيتين
        setTimeout(() => {
            saveButton.innerHTML = originalButtonText;
            saveButton.disabled = false;
        }, 2000);
    })
    .catch(error => {
        console.error('Error saving yearly progress:', error);
        saveButton.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i> فشل الحفظ';

        // تغيير الزر مرة أخرى بعد ثانيتين
        setTimeout(() => {
            saveButton.innerHTML = originalButtonText;
            saveButton.disabled = false;
        }, 2000);
    });
}

// وظيفة تفريغ حقول الحصص في جدول التدرجات السنوية
function clearLessonFields() {
    const table = document.getElementById('yearly-progress-table');
    if (!table) return;

    const editableCells = table.querySelectorAll('[contenteditable="true"]');

    // تغيير نص الزر إلى حالة "جاري التفريغ"
    const clearButton = document.getElementById('clear-lessons-btn');
    if (clearButton) {
        const originalText = clearButton.innerHTML;
        clearButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري تفريغ الحقول';
        clearButton.disabled = true;

        // تفريغ محتوى جميع الخلايا
        editableCells.forEach(cell => {
            cell.textContent = '';
        });

        // الحصول على السنة المحددة
        const selectedYear = parseInt(document.getElementById('year-selector').value);

        // إعادة ترقيم الأسابيع للتأكد من أنها متسلسلة قبل الحفظ
        renumberWeeks();

        // الحصول على بيانات الجدول بعد التفريغ
        const rows = table.querySelectorAll('tr:not(.add-row-container)');
        const progressData = [];

        // جمع معلومات هيكل الجدول
        const tableStructure = {
            deletedWeeks: [],
            maxWeek: 0
        };

        // تحديد أقصى رقم أسبوع
        rows.forEach(row => {
            const weekNumber = parseInt(row.getAttribute('data-week'));
            if (weekNumber > tableStructure.maxWeek) {
                tableStructure.maxWeek = weekNumber;
            }
        });

        // تحديد الأسابيع المحذوفة (الأرقام المفقودة)
        for (let i = 1; i <= tableStructure.maxWeek; i++) {
            const weekExists = Array.from(rows).some(row => parseInt(row.getAttribute('data-week')) === i);
            if (!weekExists) {
                tableStructure.deletedWeeks.push(i);
            }
        }

        // معالجة كل صف في الجدول
        rows.forEach(row => {
            const weekNumber = parseInt(row.getAttribute('data-week'));
            const month = row.getAttribute('data-month');

            // الحصول على خلايا المحتوى (الحصص)
            const contentCells = row.querySelectorAll('[contenteditable="true"]');

            // التحقق مما إذا كانت الخلايا مدمجة
            const isMerged = contentCells[0].hasAttribute('colspan');

            let firstLesson = contentCells[0].textContent.trim();
            let secondLesson = isMerged ? "" : contentCells[1].textContent.trim();

            // إضافة البيانات إلى المصفوفة
            progressData.push({
                week: weekNumber,
                month: month,
                firstLesson: firstLesson,
                secondLesson: secondLesson,
                isMerged: isMerged
            });
        });

        // التحقق من وجود رمز CSRF في الصفحة
        const csrfToken = document.querySelector('input[name="csrf_token"]').value;

        // إرسال البيانات المفرغة إلى الخادم
        fetch('/save-yearly-progress', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify({
                year: selectedYear,
                data: progressData,
                tableStructure: tableStructure
            })
        })
        .then(response => response.json())
        .then(data => {
            // تغيير النص إلى "تم التفريغ" بعد الانتهاء
            clearButton.innerHTML = '<i class="fas fa-check me-1"></i> تم التفريغ';

            if (data.status === 'success') {
                // تخزين السنة الحالية لإعادة تحميلها بعد الحفظ
                const currentYear = selectedYear;

                // حفظ مؤقت للبيانات المحلية - سيتم استبدالها عند إعادة تحميل البيانات
                window.latestYearlyProgressData = {
                    year: currentYear,
                    data: progressData
                };

                // إعادة تحميل البيانات مباشرة بعد الحفظ للتأكد من التزامن الفوري
                loadYearlyProgressData(currentYear);
            }

            // إعادة الزر إلى حالته الأصلية بعد ثانيتين
            setTimeout(() => {
                clearButton.innerHTML = originalText;
                clearButton.disabled = false;
            }, 2000);
        })
        .catch(error => {
            console.error('Error saving cleared data:', error);
            clearButton.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i> فشل التفريغ';

            // إعادة الزر إلى حالته الأصلية بعد ثانيتين
            setTimeout(() => {
                clearButton.innerHTML = originalText;
                clearButton.disabled = false;
            }, 2000);
        });
    } else {
        // تفريغ الحقول إذا لم يكن الزر موجودًا
        editableCells.forEach(cell => {
            cell.textContent = '';
        });
    }
}
</script>

<!-- سكريبت حذف صفوف الإضافة -->
<script src="{{ url_for('static', filename='js/remove-add-row-containers.js') }}"></script>

<!-- سكريبت التزامن المحسن للتدرج السنوي -->
<script src="{{ url_for('static', filename='js/yearly-progress-sync.js') }}"></script>

<!-- سكريبت لضمان ربط زر الحفظ بوظيفة saveYearlyProgress -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // إعادة ربط زر الحفظ بالوظيفة المحسنة
    const saveButton = document.getElementById('save-progress-btn');
    if (saveButton) {
        // إزالة جميع المستمعين السابقين
        const newSaveButton = saveButton.cloneNode(true);
        saveButton.parentNode.replaceChild(newSaveButton, saveButton);

        // إضافة المستمع الجديد
        newSaveButton.addEventListener('click', function() {
            if (typeof saveYearlyProgress === 'function') {
                saveYearlyProgress();
            } else {
                console.error('وظيفة saveYearlyProgress غير معرفة!');
            }
        });
    }

    // تحميل البيانات للسنة الحالية
    // التأكد من أن وظيفة التحميل متاحة وتنفيذها
    if (typeof loadYearlyProgressData === 'function') {
        const yearSelector = document.getElementById('year-selector');
        const year = yearSelector ? parseInt(yearSelector.value) : 1;
        loadYearlyProgressData(year);
    }
});
</script>

<!-- أضف جافا سكريبت لحفظ الإعداد في قاعدة البيانات -->
<script>
// إضافة كود جديد لحفظ إعداد عدد الأسابيع في شهر سبتمبر
document.addEventListener('DOMContentLoaded', function() {
    // التأكد من وجود عنصر حقل عدد الأسابيع وزر الحفظ
    const septemberWeeksInput = document.getElementById('september-weeks');
    const saveSeptemberWeeksBtn = document.getElementById('save-september-weeks');

    if (septemberWeeksInput && saveSeptemberWeeksBtn) {
        // تحميل القيمة الحالية من الخادم
        fetch('/get-settings')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.settings && data.settings.september_weeks !== undefined) {
                    septemberWeeksInput.value = data.settings.september_weeks;
                }
            })
            .catch(error => {
                console.error('Error loading settings:', error);
            });

        // حفظ الإعداد عند النقر على زر الحفظ
        saveSeptemberWeeksBtn.addEventListener('click', function() {
            const septemberWeeks = parseInt(septemberWeeksInput.value) || 2;

            if (septemberWeeks < 0 || septemberWeeks > 4) {
                showMessage('يرجى إدخال قيمة بين 0 و 4', 'warning');
                return;
            }

            // حفظ النص الأصلي للزر
            const originalButtonText = saveSeptemberWeeksBtn.innerHTML;

            // تغيير نص الزر إلى "جاري الحفظ"
            saveSeptemberWeeksBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري الحفظ';
            saveSeptemberWeeksBtn.disabled = true;

            // إرسال البيانات إلى الخادم
            fetch('/save-settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                },
                body: JSON.stringify({
                    september_weeks: septemberWeeks
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // تغيير نص الزر إلى "تم الحفظ" بدلاً من إظهار رسالة النجاح
                    saveSeptemberWeeksBtn.innerHTML = '<i class="fas fa-check me-1"></i> تم الحفظ';

                    // إعادة الزر إلى حالته الأصلية بعد ثانيتين
                    setTimeout(() => {
                        saveSeptemberWeeksBtn.innerHTML = originalButtonText;
                        saveSeptemberWeeksBtn.disabled = false;
                    }, 2000);
                } else {
                    // في حالة الخطأ، نظهر رسالة الخطأ
                    showMessage(data.message || 'حدث خطأ أثناء حفظ الإعداد', 'danger');
                    saveSeptemberWeeksBtn.innerHTML = originalButtonText;
                    saveSeptemberWeeksBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error saving setting:', error);
                showMessage('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                saveSeptemberWeeksBtn.innerHTML = originalButtonText;
                saveSeptemberWeeksBtn.disabled = false;
            });
        });
    }

    // الحصول على CSRF token
    function getCsrfToken() {
        return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    }
});
</script>

<!-- إضافة ملف JavaScript للتعامل مع وضع الهاتف -->
<script src="{{ url_for('static', filename='js/mobile-edit.js') }}"></script>

<!-- إضافة ملف JavaScript لتحسين تجربة المستخدم في قسم التدرج السنوي على الهواتف المحمولة -->
<script src="{{ url_for('static', filename='js/mobile-yearly-progress.js') }}"></script>

{% endblock %}