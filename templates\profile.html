{% extends "base.html" %}

{% block content %}
<div class="container mt-5" dir="rtl">
    <div class="row">
        <!-- Right Column - Profile -->
        <div class="col-lg-4 mb-4">
            <!-- Profile Card -->
            <div class="card profile-card mb-4 border-0 shadow-sm hover-shadow">
                <div class="card-body text-center p-4">
                    <div class="profile-image-container mb-3">
                        {% if user.profile_picture %}
                            {% if '/static/images/' in user.profile_picture %}
                            <!-- صورة نموذجية -->
                            <img src="{{ user.profile_picture }}" 
                                 class="rounded-circle img-profile" 
                                 alt="{{ user.teacher_name }}">
                            {% else %}
                            <!-- صورة شخصية مرفوعة -->
                            <img src="{{ url_for('static', filename='uploads/' + user.profile_picture.replace('/static/uploads/', '')) }}" 
                                 class="rounded-circle img-profile" 
                                 alt="{{ user.teacher_name }}">
                            {% endif %}
                        {% else %}
                        <div class="rounded-circle mx-auto d-flex align-items-center justify-content-center bg-primary bg-opacity-50 text-white img-profile">
                            {{ user.teacher_name[0] }}
                        </div>
                        {% endif %}
                        
                        {% if current_user.id == user.id %}
                        <div class="default-image-options mt-2">
                            <button type="button" class="btn btn-sm btn-outline-primary default-image-btn {% if user.profile_picture and 'male-profile.png' in user.profile_picture %}selected-gender{% else %}btn-outline-primary{% endif %}" data-gender="male">
                                <i class="fas fa-male me-1"></i> أستاذ
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-primary default-image-btn {% if user.profile_picture and 'female-profile.png' in user.profile_picture %}selected-gender{% else %}btn-outline-primary{% endif %}" data-gender="female">
                                <i class="fas fa-female me-1"></i> أستاذة
                            </button>
                        </div>
                        {% endif %}
                    </div>
                    <h4 class="fw-bold mb-1">{{ user.teacher_name }}</h4>
                    <p class="text-muted small">{{ user.position or '' }}</p>
                    
                    {% if current_user.id != user.id %}
                    <div class="mt-3">
                        <a href="{{ url_for('messages', recipient_id=user.id) }}" class="btn btn-primary btn-sm w-100">
                            <i class="fas fa-envelope me-2"></i> إرسال رسالة
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Left Column - Information Cards -->
        <div class="col-lg-8">
            <!-- Personal Information Card -->
            <div class="card info-card border-0 shadow-sm hover-shadow mb-4">
                <div class="card-header bg-primary text-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0 fw-bold">
                        <i class="fas fa-user-circle me-2"></i>المعلومات الشخصية
                    </h5>
                    {% if current_user.id == user.id %}
                    <button type="button" class="btn btn-light btn-sm edit-section-btn" data-section="personal">
                        <i class="fas fa-edit me-1"></i> تعديل
                    </button>
                    {% endif %}
                </div>
                <div class="card-body p-4">
                    <!-- عرض المعلومات الشخصية -->
                    <div class="section-view" id="personal-view">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                    <div class="info-label text-muted">البريد الإلكتروني</div>
                                    <div class="info-value-container">
                                        <div class="info-value">{{ user.email or '<EMAIL>' }}</div>
                                    </div>
                            </div>
                            
                            <div class="info-item">
                                    <div class="info-label text-muted">رقم الهاتف</div>
                                    <div class="info-value-container">
                                        <div class="info-value">{{ user.phone_number or '8888888888' }}</div>
                                    </div>
                            </div>
                            
                            <div class="info-item">
                                    <div class="info-label text-muted">مكان العمل</div>
                                    <div class="info-value-container">
                                        <div class="info-value">{{ user.workplace or 'المدرسة 8' }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item">
                                    <div class="info-label text-muted">تاريخ الميلاد</div>
                                    <div class="info-value-container">
                                <div class="info-value">{% if user.birth_date %}{{ user.birth_date.strftime('%d') }} 
                                    {% set month_num = user.birth_date.strftime('%m')|int %}
                                    {% if month_num == 1 %}جانفي
                                    {% elif month_num == 2 %}فيفري
                                    {% elif month_num == 3 %}مارس
                                    {% elif month_num == 4 %}أفريل
                                    {% elif month_num == 5 %}ماي
                                    {% elif month_num == 6 %}جوان
                                    {% elif month_num == 7 %}جويلية
                                    {% elif month_num == 8 %}أوت
                                    {% elif month_num == 9 %}سبتمبر
                                    {% elif month_num == 10 %}أكتوبر
                                    {% elif month_num == 11 %}نوفمبر
                                    {% elif month_num == 12 %}ديسمبر
                                    {% endif %} 
                                    {{ user.birth_date.strftime('%Y') }}
                                            {% else %}غير محدد{% endif %}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="info-item">
                                    <div class="info-label text-muted">الحالة العائلية</div>
                                    <div class="info-value-container">
                                        <div class="info-value">
                                            {% if user.marital_status is none %}
                                                غير محدد
                                            {% else %}
                                                {{ 'متزوج(ة)' if user.marital_status else 'غير متزوج(ة)' }}
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                
                                {% if current_user.id == user.id %}
                                <div class="info-item">
                                    <div class="info-label text-muted">كلمة المرور</div>
                                    <div class="info-value-container">
                                        <div class="info-value">••••••••</div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- نموذج تعديل المعلومات الشخصية - مخفي افتراضيًا -->
                    <div class="section-edit d-none" id="personal-edit">
                        <form id="personal-form" class="needs-validation" novalidate>
                            <!-- إضافة رمز CSRF -->
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="{{ user.email }}" 
                                               required pattern="[a-zA-Z0-9._%+-]+@gmail\.com$">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="phone_number" class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" id="phone_number" name="phone_number" 
                                               value="{{ user.phone_number }}" 
                                               required pattern="[0-9]{10}" maxlength="10">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="workplace" class="form-label">مكان العمل</label>
                                        <input type="text" class="form-control" id="workplace" name="workplace" 
                                               value="{{ user.workplace }}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="birth_date" class="form-label">تاريخ الميلاد</label>
                                        <input type="date" class="form-control" id="birth_date" name="birth_date" 
                                               value="{% if user.birth_date %}{{ user.birth_date.strftime('%Y-%m-%d') }}{% endif %}">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="marital_status" class="form-label">الحالة العائلية</label>
                                        <select class="form-select" id="marital_status" name="marital_status">
                                            <option value="none" {% if user.marital_status is none %}selected{% endif %}>غير محدد</option>
                                            <option value="0" {% if user.marital_status == false %}selected{% endif %}>غير متزوج(ة)</option>
                                            <option value="1" {% if user.marital_status == true %}selected{% endif %}>متزوج(ة)</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="password" class="form-label">كلمة المرور</label>
                                        <div class="position-relative">
                                            <input type="password" class="form-control" id="password" name="password" 
                                                   value="••••••••">
                                            <button class="btn btn-link password-toggle-btn position-absolute top-0 start-0 h-100" type="button" data-target="password" style="left: 0 !important; right: auto !important;">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="password_confirm" class="form-label">كلمة المرور الجديدة</label>
                                        <div class="position-relative">
                                            <input type="password" class="form-control" id="password_confirm" name="password_confirm" 
                                                   placeholder="اترك هذا الحقل فارغاً إذا كنت لا ترغب في تغيير كلمة المرور">
                                            <button class="btn btn-link password-toggle-btn position-absolute top-0 start-0 h-100" type="button" data-target="password_confirm" style="left: 0 !important; right: auto !important;">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-end mt-3">
                                <button type="button" class="btn btn-secondary cancel-edit-btn" data-section="personal">إلغاء</button>
                                <button type="button" class="btn btn-success save-section-btn" data-section="personal">حفظ التغييرات</button>
                            </div>
                        </form>
                    </div>
                            </div>
                        </div>
                        
            <!-- Administrative Information Card -->
            <div class="card admin-info-card border-0 shadow-sm hover-shadow mb-4">
                <div class="card-header bg-primary text-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0 fw-bold">
                        <i class="fas fa-briefcase me-2"></i>المعلومات الإدارية
                    </h5>
                    {% if current_user.id == user.id %}
                    <button type="button" class="btn btn-light btn-sm edit-section-btn" data-section="admin">
                        <i class="fas fa-edit me-1"></i> تعديل
                    </button>
                    {% endif %}
                </div>
                <div class="card-body p-4">
                    <!-- عرض المعلومات الإدارية -->
                    <div class="section-view" id="admin-view">
                        <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                    <div class="info-label text-muted">المهنة</div>
                                    <div class="info-value-container">
                                        <div class="info-value">{{ user.position or 'غير محدد' }}</div>
                                    </div>
                                </div>
                                
                                <div class="info-item">
                                    <div class="info-label text-muted">الصنف / الدرجة</div>
                                    <div class="info-value-container">
                                        <div class="info-value">{{ user.category or 'غير محدد' }} {% if user.category %}/{% endif %} {{ user.grade or 'غير محدد' }}</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="info-item">
                                    <div class="info-label text-muted">سنوات الخبرة</div>
                                    <div class="info-value-container">
                                        <div class="info-value">{% if user.years_of_experience %}{{ user.years_of_experience }} سنة{% else %}غير محدد{% endif %}</div>
                                    </div>
                            </div>
                            
                            <div class="info-item">
                                    <div class="info-label text-muted">تاريخ بداية العمل</div>
                                    <div class="info-value-container">
                                <div class="info-value">{% if user.work_start_date %}{{ user.work_start_date.strftime('%d') }} 
                                    {% set month_num = user.work_start_date.strftime('%m')|int %}
                                    {% if month_num == 1 %}جانفي
                                    {% elif month_num == 2 %}فيفري
                                    {% elif month_num == 3 %}مارس
                                    {% elif month_num == 4 %}أفريل
                                    {% elif month_num == 5 %}ماي
                                    {% elif month_num == 6 %}جوان
                                    {% elif month_num == 7 %}جويلية
                                    {% elif month_num == 8 %}أوت
                                    {% elif month_num == 9 %}سبتمبر
                                    {% elif month_num == 10 %}أكتوبر
                                    {% elif month_num == 11 %}نوفمبر
                                    {% elif month_num == 12 %}ديسمبر
                                    {% endif %} 
                                    {{ user.work_start_date.strftime('%Y') }}
                                    {% else %}غير محدد{% endif %}
                                </div>
                            </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- نموذج تعديل المعلومات الإدارية - مخفي افتراضيًا -->
                    <div class="section-edit d-none" id="admin-edit">
                        <form id="admin-form" class="needs-validation" novalidate>
                            <!-- إضافة رمز CSRF -->
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="position" class="form-label">المهنة</label>
                                        <select class="form-select" id="position" name="position">
                                            <option value="">اختر المهنة</option>
                                            <option value="أستاذ(ة) تعليم متوسط" {{ 'selected' if user.position == 'أستاذ(ة) تعليم متوسط' else '' }}>أستاذ(ة) تعليم متوسط</option>
                                            <option value="أستاذ(ة) تعليم متوسط قسم أول" {{ 'selected' if user.position == 'أستاذ(ة) تعليم متوسط قسم أول' else '' }}>أستاذ(ة) تعليم متوسط قسم أول</option>
                                            <option value="أستاذ(ة) تعليم متوسط قسم ثاني" {{ 'selected' if user.position == 'أستاذ(ة) تعليم متوسط قسم ثاني' else '' }}>أستاذ(ة) تعليم متوسط قسم ثاني</option>
                                            <option value="أستاذ(ة) مميز(ة) في التعليم المتوسط" {{ 'selected' if user.position == 'أستاذ(ة) مميز(ة) في التعليم المتوسط' else '' }}>أستاذ(ة) مميز(ة) في التعليم المتوسط</option>
                                        </select>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label for="category" class="form-label">الصنف</label>
                                            <input type="number" class="form-control" id="category" name="category" 
                                                   value="{{ user.category or '' }}" min="1" max="99" 
                                                   maxlength="2" oninput="limitToTwoDigits(this)" pattern="[0-9]{1,2}">
                                        </div>
                                        <div class="col-6">
                                            <label for="grade" class="form-label">الدرجة</label>
                                            <input type="number" class="form-control" id="grade" name="grade" 
                                                   value="{{ user.grade or '' }}" min="1" max="99" 
                                                   maxlength="2" oninput="limitToTwoDigits(this)" pattern="[0-9]{1,2}">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="years_of_experience" class="form-label">سنوات الخبرة</label>
                                        <input type="number" class="form-control" id="years_of_experience" name="years_of_experience" 
                                               value="{{ user.years_of_experience or '' }}" min="0" max="99" 
                                               maxlength="2" oninput="limitToTwoDigits(this)" pattern="[0-9]{1,2}">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="work_start_date" class="form-label">تاريخ بداية العمل</label>
                                        <input type="date" class="form-control" id="work_start_date" name="work_start_date" 
                                               value="{% if user.work_start_date %}{{ user.work_start_date.strftime('%Y-%m-%d') }}{% endif %}">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-end mt-3">
                                <button type="button" class="btn btn-secondary cancel-edit-btn" data-section="admin">إلغاء</button>
                                <button type="button" class="btn btn-success save-section-btn" data-section="admin">حفظ التغييرات</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            {% if user.bio or current_user.id == user.id %}
            <div class="card bio-card border-0 shadow-sm hover-shadow">
                <div class="card-header bg-primary text-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0 fw-bold">
                        <i class="fas fa-info-circle me-2"></i>السيرة الذاتية
                    </h5>
                    {% if current_user.id == user.id %}
                    <button type="button" class="btn btn-light btn-sm edit-section-btn" data-section="bio">
                        <i class="fas fa-edit me-1"></i> تعديل
                    </button>
                    {% endif %}
                </div>
                <div class="card-body p-4">
                    <!-- عرض السيرة الذاتية -->
                    <div class="section-view" id="bio-view">
                        <p class="mb-0 info-value">{{ user.bio or 'لم يتم إضافة السيرة الذاتية بعد.' }}</p>
                    </div>
                    
                    <!-- نموذج تعديل السيرة الذاتية - مخفي افتراضيًا -->
                    <div class="section-edit d-none" id="bio-edit">
                        <form id="bio-form" class="needs-validation" novalidate>
                            <!-- إضافة رمز CSRF -->
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            
                            <div class="mb-3">
                                <label for="bio" class="form-label">السيرة الذاتية</label>
                                <textarea class="form-control" id="bio" name="bio" rows="5">{{ user.bio or '' }}</textarea>
                            </div>
                            
                            <div class="text-end mt-3">
                                <button type="button" class="btn btn-secondary cancel-edit-btn" data-section="bio">إلغاء</button>
                                <button type="button" class="btn btn-success save-section-btn" data-section="bio">حفظ التغييرات</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- الوضع المظلم للصفحة عند ظهور نافذة التعديل -->
<div class="modal-backdrop d-none" id="dark-overlay"></div>

<!-- Modal para cambiar la contraseña -->
<div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="changePasswordModalLabel">تغيير كلمة المرور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="change-password-form" class="needs-validation" novalidate>
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    
                    <div class="mb-3">
                        <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                        <div class="position-relative">
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                            <button class="btn btn-link password-toggle-btn position-absolute top-0 start-0 h-100" type="button" data-target="current_password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="invalid-feedback">
                            يرجى إدخال كلمة المرور الحالية
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                        <div class="position-relative">
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                            <button class="btn btn-link password-toggle-btn position-absolute top-0 start-0 h-100" type="button" data-target="new_password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="invalid-feedback">
                            يرجى إدخال كلمة المرور الجديدة
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="save-password-btn">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>

<style>
:root {
    --primary-color: #0d6efd;
    --primary-dark: #0b5ed7;
    --primary-light: #e6f0ff;
    --gray-dark: #343a40;
    --gray-light: #f8f9fa;
}

.card {
    border-radius: 10px;
    margin-bottom: 20px;
    transition: all 0.3s;
}

.hover-shadow:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.08) !important;
}

.card-header {
    border-top-left-radius: 10px !important;
    border-top-right-radius: 10px !important;
}

.img-profile {
    width: 110px;
    height: 110px;
    font-size: 45px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.08);
    border: 3px solid white;
    margin: 0 auto;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-image-container {
    position: relative;
    display: inline-block;
}

.profile-edit-icon {
    position: absolute;
    bottom: 0;
    right: 5px;
    background-color: var(--primary-color);
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0.8;
    transition: all 0.3s;
    z-index: 2;
}

.profile-edit-icon:hover {
    opacity: 1;
    transform: scale(1.1);
}

.profile-card:hover .img-profile {
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0,0,0,0.12);
}

.info-item {
    padding: 8px 0;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    position: relative;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-size: 14px;
    margin-bottom: 3px;
}

.info-value-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.info-value {
    font-weight: 600;
    color: var(--gray-dark);
    font-size: 16px;
    flex-grow: 1;
}

.edit-icon {
    color: var(--primary-color);
    font-size: 14px;
    opacity: 0.6;
    transition: all 0.2s;
    margin-right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 26px;
    height: 26px;
    border-radius: 50%;
}

.edit-icon:hover {
    opacity: 1;
    background-color: var(--primary-light);
    transform: scale(1.1);
}

.btn-primary {
    background-color: var(--primary-color);
    border: none;
    border-radius: 5px;
    padding: 7px 18px;
    transition: all 0.3s;
    font-size: 14px;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

.btn-light {
    background-color: rgba(255, 255, 255, 0.9);
    border: none;
    transition: all 0.2s;
}

.btn-light:hover {
    background-color: white;
    transform: translateY(-1px);
}

.btn-success {
    background-color: #198754;
    border: none;
    transition: all 0.2s;
}

.btn-success:hover {
    background-color: #146c43;
    transform: translateY(-1px);
}

.bio-card .card-body {
    line-height: 1.7;
    font-size: 16px;
}

.card-title {
    font-size: 18px;
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.reload-btn {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.btn-pulse {
    animation: btnPulse 1.5s infinite;
}

@keyframes btnPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 4px 15px rgba(13, 110, 253, 0.4);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
}

.highlight-update {
    animation: fadeHighlight 2s;
}

@keyframes fadeHighlight {
    0% { background-color: rgba(13, 110, 253, 0.2); }
    100% { background-color: transparent; }
}

@media (max-width: 992px) {
    .row {
        flex-direction: column;
    }
}

.default-image-options {
    margin-top: 10px;
}

.default-image-btn {
    transition: all 0.3s;
    font-size: 0.8rem;
    margin: 0 1px;
    padding: 2px 15px;
    border-radius: 2px;
}

.default-image-btn:hover {
    transform: translateY(-2px);
}

.selected-gender {
    background-color: #0d6efd;
    color: white;
    border-color: #0d6efd;
}

/* تنسيق أزرار اختيار نوع المستخدم (أستاذ/أستاذة) تم إزالته */
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات الحركة عند التمرير
    function animateOnScroll() {
        const cards = document.querySelectorAll('.card');
        cards.forEach(card => {
            const cardPosition = card.getBoundingClientRect().top;
            const screenPosition = window.innerHeight;
            
            if (cardPosition < screenPosition) {
                card.classList.add('card-visible');
            }
        });
    }
    
    window.addEventListener('scroll', animateOnScroll);
    animateOnScroll(); // تشغيل عند تحميل الصفحة
    
    // حفظ القيم الافتراضية للبريد الإلكتروني
    const emailInputs = document.querySelectorAll('input[type="email"]');
    emailInputs.forEach(input => {
        // حفظ القيمة الافتراضية للحقل
        input.defaultValue = input.value;
        
        input.addEventListener('input', function() {
            let email = this.value.toLowerCase();
            if (email && !email.endsWith('@gmail.com')) {
                // إضافة @gmail.com تلقائيًا إذا لم يكن موجودًا
                if (email.includes('@') && !email.endsWith('@gmail.com')) {
                    const username = email.split('@')[0];
                    this.value = username + '@gmail.com';
                }
            }
        });
    });
    
    // تطبيق تقييد الإدخال على الحقول الرقمية
    const numericInputs = document.querySelectorAll('input[type="number"]');
    numericInputs.forEach(input => {
        // إضافة مستمع لمنع كتابة أكثر من رقمين
        input.addEventListener('input', function() {
            limitToTwoDigits(this);
        });
        
        // منع إدخال حروف أو رموز غير رقمية
        input.addEventListener('keypress', function(e) {
            if (!/^\d$/.test(e.key)) {
                e.preventDefault();
            }
        });

        // منع إدخال أكثر من رقمين عند النسخ واللصق
        input.addEventListener('paste', function(e) {
            e.preventDefault();
            const pastedData = (e.clipboardData || window.clipboardData).getData('text');
            const numericData = pastedData.replace(/[^0-9]/g, '');
            if (numericData.length > 2) {
                this.value = numericData.slice(0, 2);
            } else {
                this.value = numericData;
            }
            limitToTwoDigits(this);
        });
    });
    
    // تفعيل وضع التعديل
    const editBtns = document.querySelectorAll('.edit-section-btn');
    editBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const section = this.getAttribute('data-section');
            // إخفاء قسم العرض وإظهار قسم التعديل
            document.getElementById(section + '-view').classList.add('d-none');
            document.getElementById(section + '-edit').classList.remove('d-none');
        });
    });
    
    // إلغاء التعديل
    const cancelBtns = document.querySelectorAll('.cancel-edit-btn');
    cancelBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const section = this.getAttribute('data-section');
            // إظهار قسم العرض وإخفاء قسم التعديل
            document.getElementById(section + '-view').classList.remove('d-none');
            document.getElementById(section + '-edit').classList.add('d-none');
            
            // إعادة تعيين صحة النموذج
            const form = document.getElementById(section + '-form');
            form.classList.remove('was-validated');
        });
    });
    
    // تحديد عدد الأرقام في حقول الصنف والدرجة وسنوات الخبرة
    function limitToTwoDigits(input) {
        // إزالة أي أحرف غير رقمية
        input.value = input.value.replace(/[^0-9]/g, '');
        
        // منع كتابة أكثر من رقمين
        if (input.value.length > 2) {
            input.value = input.value.slice(0, 2);
        }
        
        // التحقق من القيمة المسموح بها
        const num = parseInt(input.value);
        if (input.id === 'years_of_experience') {
            if (num > 99) input.value = 99;
        } else {
            if (num > 99) input.value = 99;
            if (num < 1 && input.value !== '') input.value = 1;
        }
    }
    
    // دالة لعرض تنبيه في الزاوية
    function showAlert(message, type) {
        const alertPlaceholder = document.createElement('div');
        alertPlaceholder.className = `alert alert-${type} alert-dismissible fade show`;
        alertPlaceholder.setAttribute('role', 'alert');
        alertPlaceholder.style.position = 'fixed';
        alertPlaceholder.style.top = '20px';
        alertPlaceholder.style.right = '20px';
        alertPlaceholder.style.zIndex = '1050';
        alertPlaceholder.style.maxWidth = '300px';
        alertPlaceholder.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
        alertPlaceholder.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
        `;
        
        document.body.appendChild(alertPlaceholder);
        
        // إخفاء التنبيه بعد 5 ثوانٍ
        setTimeout(() => {
            alertPlaceholder.classList.add('fade');
            setTimeout(() => {
                alertPlaceholder.remove();
            }, 300);
        }, 5000);
    }
    
    // التحقق من صحة النموذج
    (function() {
        'use strict';
        const forms = document.querySelectorAll('.needs-validation');
        
        Array.from(forms).forEach(form => {
            // التحقق من صحة النموذج قبل الإرسال
            const validateForm = function() {
                // التحقق من صحة البريد الإلكتروني إذا كان الحقل موجودًا
                const emailInput = form.querySelector('input[type="email"]');
                if (emailInput) {
                    const email = emailInput.value.toLowerCase();
                    if (!email.endsWith('@gmail.com')) {
                        showAlert('البريد الإلكتروني يجب أن يكون حساب Gmail', 'danger');
                        // إعادة القيمة السابقة للحقل
                        emailInput.value = emailInput.defaultValue;
                        return false;
                    }
                }

                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                    showAlert('يرجى تصحيح الأخطاء في النموذج', 'danger');
                    form.classList.add('was-validated');
                    return false;
                }
                form.classList.add('was-validated');
                return true;
            };
            
            // ربط التحقق بزر الحفظ
            const section = form.id.split('-')[0];
            const saveBtn = document.querySelector(`.save-section-btn[data-section="${section}"]`);
            
            if (saveBtn) {
                saveBtn.onclick = function(event) {
                    if (validateForm()) {
                        // استدعاء معالج النقر الأصلي إذا كان النموذج صحيحًا
                        const section = this.getAttribute('data-section');
                        const formId = section + '-form';
                        const form = document.getElementById(formId);
                        
                        // التحقق من صحة البريد الإلكتروني قبل الإرسال
                        const emailInput = form.querySelector('input[type="email"]');
                        if (emailInput) {
                            const email = emailInput.value.toLowerCase();
                            if (!email.endsWith('@gmail.com')) {
                                showAlert('البريد الإلكتروني يجب أن يكون حساب Gmail', 'danger');
                                // إعادة القيمة السابقة للحقل
                                emailInput.value = emailInput.defaultValue;
                                return;
                            }
                            // تحديث القيمة بأحرف صغيرة
                            emailInput.value = email;
                        }
                        
                        // تحسين سرعة التحقق من الحقول الرقمية
                        const numericInputs = form.querySelectorAll('input[type="number"]');
                        numericInputs.forEach(input => {
                            // التأكد من أن القيمة لا تتجاوز رقمين
                            if (input.value.length > 2) {
                                input.value = input.value.slice(0, 2);
                            }
                        });
                        
                        const formData = new FormData(form);
                        
                        // إضافة رمز CSRF إذا لم يكن موجودًا
                        if (!formData.has('csrf_token')) {
                            formData.append('csrf_token', "{{ csrf_token() }}");
                        }
                        
                        // إضافة معرف المستخدم إلى البيانات (هذا هو الجزء المفقود)
                        formData.append('user_id', "{{ user.id }}");
                        
                        // معالجة حقول كلمة المرور للقسم الشخصي
                        if (section === 'personal') {
                            const originalPassword = document.getElementById('password');
                            const newPassword = document.getElementById('password_confirm');
                            
                            // طباعة قيم كلمة المرور للتشخيص
                            console.log('كلمة المرور الحالية:', originalPassword.value === '••••••••' ? '[stars]' : '[filled]');
                            console.log('كلمة المرور الجديدة:', newPassword.value ? '[filled]' : '[empty]');
                            
                            // إذا تم إدخال كلمة مرور جديدة
                            if (newPassword && newPassword.value.trim() !== '') {
                                console.log('تم إدخال كلمة مرور جديدة');
                                
                                // كلمة المرور ستبقى في النموذج ولكن سنضيف أيضًا حقل تأكيد كلمة المرور
                                // هذا لأن الخادم يتوقع أن password و password_confirm متطابقين
                                formData.set('password', newPassword.value);
                                formData.set('password_confirm', newPassword.value);
                            } else {
                                // إذا كانت كلمة المرور الحالية معروضة كنجوم، نحذفها من البيانات
                                // لتجنب إرسالها للخادم
                                if (originalPassword.value === '••••••••') {
                                    console.log('كلمة المرور الحالية تحتوي على نجوم، نحذفها');
                                    formData.delete('password');
                                }
                                // تأكد من حذف حقل تأكيد كلمة المرور أيضًا
                                formData.delete('password_confirm');
                            }
                            
                            // عرض بيانات النموذج النهائية للتشخيص
                            console.log('بيانات النموذج النهائية:');
                            for (const pair of formData.entries()) {
                                console.log(pair[0] + ': ' + (pair[0] === 'password' || pair[0] === 'password_confirm' ? '******' : pair[1]));
                            }
                        }
                        
                        // عرض رسالة انتظار
                        this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جارٍ الحفظ...';
                        this.disabled = true;
                        
                        // إرسال البيانات باستخدام AJAX مع تحسين الأداء
                        fetch('/update_section/' + section, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            credentials: 'same-origin' // ضمان إرسال ملفات تعريف الارتباط
                        })
                        .then(response => {
                            if (!response.ok) {
                                // محاولة الحصول على تفاصيل الخطأ من الرد
                                return response.json()
                                .then(errorData => {
                                    throw new Error(errorData.message || `استجابة الخادم غير ناجحة (${response.status}): ${errorData.error || 'خطأ غير معروف'}`);
                                })
                                .catch(e => {
                                    // إذا لم يكن الرد بتنسيق JSON
                                    throw new Error(`استجابة الخادم غير ناجحة (${response.status}): يرجى التحقق من البيانات المدخلة`);
                                });
                            }
                            return response.json();
                        })
                        .then(data => {
                            if (data.success) {
                                // حفظ القيم الافتراضية الجديدة
                                for (const input of form.querySelectorAll('input, select, textarea')) {
                                    if (input.type === 'email') {
                                        input.defaultValue = input.value;
                                    }
                                    // حفظ قيم التواريخ الجديدة
                                    if (input.type === 'date') {
                                        input.defaultValue = input.value;
                                    }
                                }
                                
                                // تجميع البيانات المدخلة في كائن لاستخدامه في تحديث العرض
                                const formData = new FormData(form);
                                const formDataObject = {};
                                formData.forEach((value, key) => {
                                    formDataObject[key] = value;
                                });
                                
                                console.log('البيانات التي سيتم استخدامها للتحديث:', formDataObject);
                                
                                // تحديث العرض بدون إعادة تحميل الصفحة
                                updateDisplayAfterSave(section, formDataObject);
                                
                                // إخفاء نموذج التعديل وإظهار العرض
                                document.getElementById(section + '-view').classList.remove('d-none');
                                document.getElementById(section + '-edit').classList.add('d-none');
                                
                                // إعادة زر الحفظ للوضع الطبيعي
                                this.innerHTML = 'حفظ التغييرات';
                                this.disabled = false;
                            } else {
                                // إظهار رسالة خطأ
                                showAlert('حدث خطأ أثناء حفظ البيانات: ' + data.message, 'danger');
                                // إعادة زر الحفظ للوضع الطبيعي
                                this.innerHTML = 'حفظ التغييرات';
                                this.disabled = false;
                            }
                        })
                        .catch(error => {
                            console.error('خطأ:', error);
                            showAlert('حدث خطأ أثناء حفظ البيانات: ' + error.message, 'danger');
                            // إعادة زر الحفظ للوضع الطبيعي
                            this.innerHTML = 'حفظ التغييرات';
                            this.disabled = false;
                        });
                    }
                };
            }
        });
        
        // دالة لتحديث العرض بعد الحفظ بدون إعادة تحميل الصفحة
        function updateDisplayAfterSave(section, userData) {
            // تحديث عناصر العرض بناءً على القسم والبيانات المحدثة
            const viewSection = document.getElementById(section + '-view');
            
            // دالة مساعدة لتطبيق تأثير الإبراز على العناصر المحدثة
            function applyHighlight(element) {
                if (element) {
                    element.classList.add('highlight-update');
                    setTimeout(() => {
                        element.classList.remove('highlight-update');
                    }, 2000);
                }
            }
            
            if (section === 'personal') {
                // تحديث البريد الإلكتروني
                const emailValue = viewSection.querySelector('.row .col-md-6:first-child .info-item:nth-child(1) .info-value');
                if (emailValue && userData.email) {
                    emailValue.textContent = userData.email;
                    applyHighlight(emailValue);
                }
                
                // تحديث رقم الهاتف
                const phoneValue = viewSection.querySelector('.row .col-md-6:first-child .info-item:nth-child(2) .info-value');
                if (phoneValue && userData.phone_number) {
                    phoneValue.textContent = userData.phone_number;
                    applyHighlight(phoneValue);
                }
                
                // تحديث مكان العمل
                const workplaceValue = viewSection.querySelector('.row .col-md-6:first-child .info-item:nth-child(3) .info-value');
                if (workplaceValue && userData.workplace) {
                    workplaceValue.textContent = userData.workplace;
                    applyHighlight(workplaceValue);
                }
                
                // تحديث تاريخ الميلاد
                const birthDateValue = viewSection.querySelector('.row .col-md-6:last-child .info-item:first-child .info-value');
                if (birthDateValue && userData.birth_date) {
                    try {
                        // تأكد من أن البيانات هي string قبل التحويل
                        let dateStr = userData.birth_date;
                        if (dateStr instanceof Date) {
                            dateStr = dateStr.toISOString().split('T')[0];
                        }
                        
                        // تحويل التاريخ إلى التنسيق المطلوب
                        const date = new Date(dateStr);
                        if (!isNaN(date.getTime())) {
                            const day = date.getDate();
                            const year = date.getFullYear();
                            const month = date.getMonth() + 1;
                            
                            // تحويل الشهر إلى الاسم العربي
                            let monthName = "";
                            switch(month) {
                                case 1: monthName = "جانفي"; break;
                                case 2: monthName = "فيفري"; break;
                                case 3: monthName = "مارس"; break;
                                case 4: monthName = "أفريل"; break;
                                case 5: monthName = "ماي"; break;
                                case 6: monthName = "جوان"; break;
                                case 7: monthName = "جويلية"; break;
                                case 8: monthName = "أوت"; break;
                                case 9: monthName = "سبتمبر"; break;
                                case 10: monthName = "أكتوبر"; break;
                                case 11: monthName = "نوفمبر"; break;
                                case 12: monthName = "ديسمبر"; break;
                            }
                            
                            birthDateValue.textContent = `${day} ${monthName} ${year}`;
                            applyHighlight(birthDateValue);
                        }
                    } catch (e) {
                        console.error("خطأ في تحويل التاريخ:", e);
                    }
                }
                
                // محاولة تحديث الحالة العائلية
                const maritalStatusValue = viewSection.querySelector('.row .col-md-6:last-child .info-item:nth-child(2) .info-value');
                if (maritalStatusValue && userData.marital_status !== undefined) {
                    // تحويل القيمة إلى boolean أو التعامل معها كنص
                    let isMarried = false;
                    if (typeof userData.marital_status === 'string') {
                        isMarried = userData.marital_status === '1';
                    } else if (typeof userData.marital_status === 'boolean') {
                        isMarried = userData.marital_status;
                    } else if (typeof userData.marital_status === 'number') {
                        isMarried = userData.marital_status === 1;
                    }
                    
                    maritalStatusValue.textContent = isMarried ? 'متزوج(ة)' : 'غير متزوج(ة)';
                    applyHighlight(maritalStatusValue);
                    
                    console.log('تم تحديث الحالة العائلية:', userData.marital_status, 'النتيجة:', isMarried ? 'متزوج(ة)' : 'غير متزوج(ة)');
                }
                
                // إشعار بتغيير كلمة المرور إذا تم تغييرها
                if (userData.password && userData.password.trim() !== '') {
                    // تحديث عرض كلمة المرور لإظهار النجوم
                    const passwordValue = viewSection.querySelector('.row .col-md-6:last-child .info-item:nth-child(3) .info-value');
                    if (passwordValue) {
                        passwordValue.textContent = '••••••••';
                    }
                }
            } 
            else if (section === 'admin') {
                // تحديث المهنة
                const positionValue = viewSection.querySelector('.row .col-md-6:first-child .info-item:nth-child(1) .info-value');
                if (positionValue && userData.position) {
                    positionValue.textContent = userData.position;
                    applyHighlight(positionValue);
                    
                    // تحديث المهنة في القائمة المنسدلة في النافبار
                    const dropdownPositions = document.querySelectorAll('.dropdown-header small.text-white');
                    if (dropdownPositions.length > 0) {
                        dropdownPositions.forEach(element => {
                            element.textContent = userData.position;
                        });
                    }
                }
                
                // تحديث الصنف / الدرجة
                const categoryGradeValue = viewSection.querySelector('.row .col-md-6:first-child .info-item:nth-child(2) .info-value');
                if (categoryGradeValue) {
                    const category = userData.category || '-';
                    const grade = userData.grade || '-';
                    categoryGradeValue.textContent = `${category} / ${grade}`;
                    applyHighlight(categoryGradeValue);
                }
                
                // تحديث سنوات الخبرة
                const experienceValue = viewSection.querySelector('.row .col-md-6:last-child .info-item:nth-child(1) .info-value');
                if (experienceValue && userData.years_of_experience) {
                    experienceValue.textContent = `${userData.years_of_experience} سنة`;
                    applyHighlight(experienceValue);
                } else if (experienceValue && userData.years_of_experience === '') {
                    experienceValue.textContent = 'غير محدد';
                    applyHighlight(experienceValue);
                }
                
                // تحديث تاريخ بداية العمل
                const workStartDateValue = viewSection.querySelector('.row .col-md-6:last-child .info-item:nth-child(2) .info-value');
                if (workStartDateValue && userData.work_start_date) {
                    try {
                        // تأكد من أن البيانات هي string قبل التحويل
                        let dateStr = userData.work_start_date;
                        if (dateStr instanceof Date) {
                            dateStr = dateStr.toISOString().split('T')[0];
                        }
                        
                        // تحويل التاريخ إلى التنسيق المطلوب
                        const date = new Date(dateStr);
                        if (!isNaN(date.getTime())) {
                            const day = date.getDate();
                            const year = date.getFullYear();
                            const month = date.getMonth() + 1;
                            
                            // تحويل الشهر إلى الاسم العربي
                            let monthName = "";
                            switch(month) {
                                case 1: monthName = "جانفي"; break;
                                case 2: monthName = "فيفري"; break;
                                case 3: monthName = "مارس"; break;
                                case 4: monthName = "أفريل"; break;
                                case 5: monthName = "ماي"; break;
                                case 6: monthName = "جوان"; break;
                                case 7: monthName = "جويلية"; break;
                                case 8: monthName = "أوت"; break;
                                case 9: monthName = "سبتمبر"; break;
                                case 10: monthName = "أكتوبر"; break;
                                case 11: monthName = "نوفمبر"; break;
                                case 12: monthName = "ديسمبر"; break;
                            }
                            
                            workStartDateValue.textContent = `${day} ${monthName} ${year}`;
                            applyHighlight(workStartDateValue);
                        } else {
                            workStartDateValue.textContent = 'غير محدد';
                            applyHighlight(workStartDateValue);
                        }
                    } catch (e) {
                        console.error("خطأ في تحويل التاريخ:", e);
                    }
                } else if (workStartDateValue && userData.work_start_date === '') {
                    workStartDateValue.textContent = 'غير محدد';
                    applyHighlight(workStartDateValue);
                }
            }
            else if (section === 'bio') {
                // تحديث السيرة الذاتية
                const bioValue = viewSection.querySelector('p.info-value');
                if (bioValue) {
                    bioValue.textContent = userData.bio || 'لم يتم إضافة السيرة الذاتية بعد.';
                    applyHighlight(bioValue);
                }
            }
            
            console.log(`تم تحديث عرض القسم: ${section}`, userData);
        }
    })();

    // تغيير الصورة الشخصية
    const defaultImageButtons = document.querySelectorAll('.default-image-btn');
    defaultImageButtons.forEach(button => {
        button.addEventListener('click', function() {
            const gender = this.getAttribute('data-gender');
            
            // تغيير حالة الأزرار
            defaultImageButtons.forEach(btn => {
                btn.classList.remove('selected-gender');
                if (!btn.classList.contains('btn-outline-primary')) {
                    btn.classList.add('btn-outline-primary');
                }
            });
            this.classList.add('selected-gender');
            
            // إرسال طلب لتغيير الصورة
            const formData = new FormData();
            formData.append('default_gender', gender);
            formData.append('csrf_token', "{{ csrf_token() }}");
            formData.append('user_id', "{{ user.id }}");
            formData.append('image_type', gender);
            
            fetch('/update_default_profile_image', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // محاولة الوصول إلى عنصر الصورة
                const profileImgContainer = document.querySelector('.profile-image-container');
                const imgElement = profileImgContainer.querySelector('img.img-profile');
                
                if (data.success) {
                    // تحديث صورة الملف الشخصي على الفور
                    const imageUrl = data.profile_picture;
                    
                    // إنشاء عنصر الصورة الجديدة
                    const newImg = document.createElement('img');
                    newImg.src = imageUrl;
                    newImg.alt = "{{ user.teacher_name }}";
                    newImg.className = "rounded-circle img-profile";
                    
                    // إزالة الصورة القديمة أو الحرف الأول
                    if (imgElement) {
                        imgElement.replaceWith(newImg);
                    } else {
                        const initialLetter = profileImgContainer.querySelector('div.img-profile');
                        if (initialLetter) {
                            initialLetter.replaceWith(newImg);
                        }
                    }
                    
                    // تحديث صورة النافبار أيضًا إن وجدت الدالة
                    if (window.updateProfileImageNav) {
                        window.updateProfileImageNav(imageUrl);
                    }
                } else {
                    showAlert('حدث خطأ أثناء تغيير الصورة: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                showAlert('حدث خطأ أثناء تغيير الصورة', 'danger');
            });
        });
    });

    // تحديث معالجة أزرار إظهار/إخفاء كلمة المرور
    const togglePasswordButtons = document.querySelectorAll('.password-toggle-btn');
    togglePasswordButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault(); // منع إرسال النموذج
            
            const targetId = this.getAttribute('data-target');
            const targetInput = document.getElementById(targetId);
            
            if (!targetInput) {
                console.error('لم يتم العثور على عنصر الإدخال بالمعرف:', targetId);
                return;
            }
            
            // تبديل نوع الحقل بين النص وكلمة المرور
            if (targetInput.type === 'password') {
                targetInput.type = 'text';
                this.querySelector('i').className = 'fas fa-eye-slash';
            } else {
                targetInput.type = 'password';
                this.querySelector('i').className = 'fas fa-eye';
            }
        });
    });
});

// دالة لحفظ كلمة المرور الجديدة
function saveNewPassword() {
    const form = document.getElementById('change-password-form');
    
    // التحقق من صحة النموذج
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }
    
    const currentPassword = document.getElementById('current_password').value;
    const newPassword = document.getElementById('new_password').value;
    
    // إرسال طلب AJAX لتغيير كلمة المرور
    const formData = new FormData();
    formData.append('current_password', currentPassword);
    formData.append('new_password', newPassword);
    formData.append('csrf_token', "{{ csrf_token() }}");
    
    fetch('/change_password', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إغلاق المودال
            bootstrap.Modal.getInstance(document.getElementById('changePasswordModal')).hide();
        } else {
            showAlert(data.message || 'حدث خطأ أثناء تغيير كلمة المرور', 'danger');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('حدث خطأ أثناء تغيير كلمة المرور', 'danger');
    });
}
</script>
{% endblock %}


