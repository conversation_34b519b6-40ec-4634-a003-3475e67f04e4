{% extends "base.html" %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">تعديل معلوماتي الشخصية</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('self_edit') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        
                        <div class="mb-3">
                            <label for="teacher_name" class="form-label">اسم الأستاذ(ة)</label>
                            <input type="text" class="form-control text-right" id="teacher_name" name="teacher_name" 
                                   value="{{ current_user.teacher_name }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control text-right" id="email" name="email" 
                                   value="{{ current_user.email }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="current_password" class="form-label">كلمة المرور الحالية (للتحقق فقط عند تغيير كلمة المرور)</label>
                            <div class="input-group">
                                <input type="password" class="form-control text-right" id="current_password" name="current_password">
                                <span class="input-group-text toggle-password" style="cursor: pointer;"><i class="fas fa-eye"></i></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="new_password" class="form-label">كلمة المرور الجديدة (اتركه فارغًا لعدم التغيير)</label>
                            <div class="input-group">
                                <input type="password" class="form-control text-right" id="new_password" name="new_password">
                                <span class="input-group-text toggle-password" style="cursor: pointer;"><i class="fas fa-eye"></i></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_new_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                            <div class="input-group">
                                <input type="password" class="form-control text-right" id="confirm_new_password" name="confirm_new_password">
                                <span class="input-group-text toggle-password" style="cursor: pointer;"><i class="fas fa-eye"></i></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="workplace" class="form-label">مكان العمل</label>
                            <input type="text" class="form-control text-right" id="workplace" name="workplace" 
                                   value="{{ current_user.workplace }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="phone_number" class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-control text-right" id="phone_number" name="phone_number" 
                                   value="{{ current_user.phone_number }}" required>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                            <a href="{{ url_for('welcome') }}" class="btn btn-secondary">إلغاء</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.card-header {
    background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%);
    padding: 15px;
}

.form-control {
    display: block;
    width: 100%;
    padding: 12px;
    border-radius: 5px;
    border: 1px solid #ddd;
    background-color: #fff;
    opacity: 1;
    visibility: visible;
}

.form-control:focus {
    border-color: #1976d2;
    box-shadow: 0 0 0 0.2rem rgba(25, 118, 210, 0.25);
    outline: none;
}

.btn-primary {
    background-color: #1e88e5;
    border: none;
    padding: 10px 30px;
    transition: all 0.3s;
    color: #fff;
}

.btn-primary:hover {
    background-color: #1565c0;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: #757575;
    border: none;
    padding: 10px 30px;
    margin-right: 10px;
    transition: all 0.3s;
    color: #fff;
}

.btn-secondary:hover {
    background-color: #616161;
    transform: translateY(-2px);
}

.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    text-align: right;
    display: block;
}

.mb-3 {
    margin-bottom: 1rem !important;
    display: block;
}

.text-right {
    text-align: right !important;
}

.card-body {
    padding: 1.5rem;
}

.text-center {
    text-align: center !important;
}
</style>
{% endblock %}

{% block scripts %}
{{ super() }} {# Include scripts from base template if any #}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const togglePasswordIcons = document.querySelectorAll('.toggle-password');

    togglePasswordIcons.forEach(icon => {
        icon.addEventListener('click', function() {
            const passwordInput = this.previousElementSibling; // Get the input field right before the span
            const iconElement = this.querySelector('i');

            // Toggle the type
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            // Toggle the icon
            if (type === 'password') {
                iconElement.classList.remove('fa-eye-slash');
                iconElement.classList.add('fa-eye');
            } else {
                iconElement.classList.remove('fa-eye');
                iconElement.classList.add('fa-eye-slash');
            }
        });
    });
});
</script>
{% endblock %}