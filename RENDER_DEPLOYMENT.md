# دليل نشر المشروع على Render.com

## الخطوات المطلوبة:

### 1. إنشاء حساب على Render.com
- اذهب إلى [render.com](https://render.com)
- أنشئ حساب جديد أو سجل دخول

### 2. ربط المستودع
- اضغط على "New +" ثم "Web Service"
- اربط حساب GitHub الخاص بك
- اختر المستودع الذي يحتوي على هذا المشروع

### 3. إعداد قاعدة البيانات
- أنشئ قاعدة بيانات PostgreSQL جديدة:
  - اضغط على "New +" ثم "PostgreSQL"
  - اختر اسم لقاعدة البيانات
  - اختر المنطقة (يفضل نفس منطقة التطبيق)

### 4. إعداد متغيرات البيئة
في إعدادات Web Service، أضف المتغيرات التالية:

#### متغيرات إجبارية:
```
SECRET_KEY=<سيتم إنشاؤه تلقائياً>
DATABASE_URL=<سيتم ربطه تلقائياً بقاعدة البيانات>

CLOUDINARY_CLOUD_NAME=dho0fkwf4
CLOUDINARY_API_KEY=726728831258746
CLOUDINARY_API_SECRET=bKLcmXjcyRDO_Uw9tfzKeeffJc4

GOOGLE_CLIENT_ID=1074418425224-cg6kttp07a7vfgm7vjdtmpbvch3tng94.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=https://your-app-name.onrender.com/oauth2callback

VAPID_PUBLIC_KEY=BHuFRZ0YE9pQxjVQBfp80fT1wkG4bsjyUUBvQ-9_v2lPm0Jf_WMJcqfv_UEitq6SCOLstxL5q-Dz9bXcwlFdRG8
VAPID_PRIVATE_KEY=DKkJuZ1vLU3eHaX6dDX4TsZBwZF09Wl-_uJvBTWaOIw
VAPID_CLAIM_EMAIL=<EMAIL>

MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=rkyieuepofpiuvcj
MAIL_DEFAULT_SENDER=<EMAIL>
```

### 5. إعدادات التطبيق
- **Build Command**: `pip install -r requirements.txt`
- **Start Command**: `gunicorn app:app`
- **Environment**: Python 3

### 6. ربط قاعدة البيانات
- في إعدادات Web Service
- اذهب إلى "Environment"
- اربط قاعدة البيانات PostgreSQL التي أنشأتها

### 7. النشر
- اضغط على "Create Web Service"
- انتظر حتى يكتمل النشر

## ملاحظات مهمة:

1. **تحديث GOOGLE_REDIRECT_URI**: 
   - بعد النشر، استبدل `your-app-name` بالاسم الفعلي لتطبيقك على Render

2. **إنشاء الجداول**:
   - بعد النشر الأول، قد تحتاج لتشغيل `create_tables.py` لإنشاء جداول قاعدة البيانات

3. **الملفات الثابتة**:
   - Render يدعم الملفات الثابتة تلقائياً

4. **المراقبة**:
   - يمكنك مراقبة السجلات من لوحة تحكم Render

## استكشاف الأخطاء:

- تأكد من أن جميع متغيرات البيئة مضبوطة بشكل صحيح
- تحقق من السجلات في حالة وجود أخطاء
- تأكد من أن قاعدة البيانات متصلة بشكل صحيح
