/**
 * ملف JavaScript للتحكم بشريط السلام عليكم
 * تم تحديثه لاستخدام API بدلاً من التخزين المحلي
 */

// تنفيذ الكود بعد تحميل الصفحة بالكامل
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل ملف greeting-bar.js');

    // الحصول على العناصر
    const greetingBar = document.getElementById('greeting-bar');
    const editBtn = document.getElementById('edit-greeting-btn');
    const visibilityToggle = document.getElementById('greeting-visibility-toggle');

    // التحقق من وجود شريط السلام عليكم
    if (!greetingBar) {
        console.error('لم يتم العثور على عنصر شريط السلام عليكم (greeting-bar)');
        return;
    }

    console.log('تم العثور على شريط السلام عليكم');

    // التحقق من وجود أزرار التحكم (ستكون موجودة فقط للمدير والمشرفين)
    const isAdmin = editBtn !== null && visibilityToggle !== null;
    console.log('هل المستخدم مدير أو مشرف؟', isAdmin);

    // دالة للحصول على رمز CSRF من الصفحة
    function getCsrfToken() {
        // محاولة الحصول على رمز CSRF من عنصر meta
        const metaTag = document.querySelector('meta[name="csrf-token"]');
        if (metaTag) {
            return metaTag.getAttribute('content');
        }

        // محاولة الحصول على رمز CSRF من ملف تعريف الارتباط
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrf_token') {
                return value;
            }
        }

        // إذا لم يتم العثور على رمز CSRF، نعيد قيمة فارغة
        return '';
    }

    // دالة لتحديث عبارة الترحيب على الخادم
    function updateGreeting(newText) {
        console.log('جاري تحديث عبارة الترحيب على الخادم...');

        // التحقق من أن النص ليس فارغاً
        if (!newText.trim()) {
            console.error('لا يمكن تحديث عبارة الترحيب بنص فارغ');
            return Promise.reject('النص فارغ');
        }

        // الحصول على رمز CSRF
        const csrfToken = getCsrfToken();
        console.log('رمز CSRF:', csrfToken ? 'تم العثور عليه' : 'غير موجود');

        // إرسال طلب لتحديث العبارة
        return fetch('/api/update_greeting', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify({
                greeting: newText
            }),
            credentials: 'same-origin' // إرسال ملفات تعريف الارتباط مع الطلب
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('فشل في الاتصال بالخادم');
            }
            return response.json();
        });
    }

    // دالة لتحديث حالة ظهور الشريط على الخادم
    function updateGreetingVisibility(visible) {
        console.log('جاري تحديث حالة ظهور الشريط على الخادم...');
        console.log('القيمة المرسلة:', visible, 'نوع القيمة:', typeof visible);

        // الحصول على رمز CSRF
        const csrfToken = getCsrfToken();

        // إرسال طلب لتحديث حالة الظهور
        return fetch('/api/update_greeting_visibility', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify({
                visible: visible
            }),
            credentials: 'same-origin' // إرسال ملفات تعريف الارتباط مع الطلب
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('فشل في الاتصال بالخادم');
            }
            return response.json();
        });
    }

    // إضافة مستمع حدث للتشيك بوكس (فقط للمدير والمشرفين)
    if (isAdmin && visibilityToggle) {
        visibilityToggle.addEventListener('change', function() {
            console.log('تم تغيير حالة التشيك بوكس');

            // الحصول على حالة التشيك بوكس (مفعل = ظاهر، غير مفعل = مخفي)
            const isVisible = this.checked;
            console.log('هل الشريط يجب أن يكون ظاهراً؟', isVisible);

            // تحديث حالة الظهور على الخادم
            updateGreetingVisibility(isVisible)
                .then(data => {
                    console.log('استجابة الخادم:', data);

                    if (data.status === 'success') {
                        // تحديث حالة ظهور الشريط
                        if (isVisible) {
                            // إظهار الشريط
                            console.log('جاري إظهار الشريط...');
                            greetingBar.style.display = 'block';
                        } else {
                            // إخفاء الشريط
                            console.log('جاري إخفاء الشريط...');
                            greetingBar.style.display = 'none';
                        }

                        // تم إلغاء إظهار إشعار النجاح عند تغيير حالة ظهور الشريط
                        console.log(isVisible ? 'تم إظهار شريط الترحيب بنجاح' : 'تم إخفاء شريط الترحيب بنجاح');
                    } else {
                        // إظهار رسالة خطأ
                        alert(data.message || 'حدث خطأ أثناء تحديث حالة ظهور الشريط');

                        // إعادة التشيك بوكس إلى حالته السابقة
                        this.checked = !isVisible;
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحديث حالة ظهور الشريط:', error);
                    alert('حدث خطأ أثناء تحديث حالة ظهور الشريط');

                    // إعادة التشيك بوكس إلى حالته السابقة
                    this.checked = !isVisible;
                });
        });
    }

    // إضافة مستمع حدث لزر التعديل (فقط للمدير والمشرفين)
    if (isAdmin && editBtn) {
        editBtn.addEventListener('click', function() {
            console.log('تم النقر على زر التعديل');

            // الحصول على النص الحالي من الشريط
            const welcomeTextDiv = greetingBar.querySelector('.welcome-text');
            if (!welcomeTextDiv) {
                console.error('لم يتم العثور على عنصر النص داخل الشريط');
                return;
            }

            // جمع النص من جميع عناصر span
            let currentText = '';
            const spans = welcomeTextDiv.querySelectorAll('.animated-word');
            spans.forEach(span => {
                currentText += span.textContent + ' ';
            });
            currentText = currentText.trim(); // إزالة المسافة الزائدة في النهاية
            console.log('النص الحالي:', currentText);

            // فتح مودال التعديل
            const modal = new bootstrap.Modal(document.getElementById('editGreetingModal'));
            if (!modal) {
                console.error('لم يتم العثور على مودال التعديل');
                return;
            }

            // تعيين النص الحالي في حقل الإدخال
            const greetingInput = document.getElementById('greeting-text');
            if (greetingInput) {
                greetingInput.value = currentText;
            }

            modal.show();
        });
    }

    // إضافة مستمع حدث لزر الحفظ في المودال (فقط للمدير والمشرفين)
    const saveBtn = document.getElementById('save-greeting-btn');
    if (isAdmin && saveBtn) {
        saveBtn.addEventListener('click', function() {
            console.log('تم النقر على زر الحفظ');

            // الحصول على النص الجديد من حقل الإدخال
            const greetingInput = document.getElementById('greeting-text');
            if (!greetingInput) {
                console.error('لم يتم العثور على حقل إدخال النص');
                return;
            }

            const newText = greetingInput.value.trim();
            console.log('النص الجديد:', newText);

            // التحقق من أن النص ليس فارغاً
            if (newText) {
                // تحديث العبارة على الخادم
                updateGreeting(newText)
                    .then(data => {
                        if (data.status === 'success') {
                            // تحديث نص الشريط
                            const welcomeTextDiv = greetingBar.querySelector('.welcome-text');
                            if (welcomeTextDiv) {
                                // تقسيم النص الجديد إلى كلمات
                                const words = newText.split(' ');

                                // إفراغ محتوى العنصر
                                welcomeTextDiv.innerHTML = '';

                                // إضافة كل كلمة كعنصر span جديد
                                words.forEach(word => {
                                    const span = document.createElement('span');
                                    span.className = 'animated-word';
                                    span.textContent = word;
                                    welcomeTextDiv.appendChild(span);
                                });
                            }

                            // إغلاق المودال
                            const modal = bootstrap.Modal.getInstance(document.getElementById('editGreetingModal'));
                            if (modal) {
                                modal.hide();
                            }

                            // تم إلغاء إظهار إشعار النجاح عند تحديث نص الشريط
                            console.log('تم تحديث عبارة الترحيب بنجاح');
                        } else {
                            // إظهار رسالة خطأ
                            alert(data.message || 'حدث خطأ أثناء تحديث عبارة الترحيب');
                        }
                    })
                    .catch(error => {
                        console.error('خطأ في تحديث عبارة الترحيب:', error);
                        alert('حدث خطأ أثناء تحديث عبارة الترحيب');
                    });
            }
        });
    }

    // تعيين حالة ظهور الشريط بناءً على قيمة greeting_visible من الخادم
    fetch('/api/get_greeting')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                console.log('بيانات الشريط من الخادم:', data.data);

                // تحديث نص الشريط
                const welcomeTextDiv = greetingBar.querySelector('.welcome-text');
                if (welcomeTextDiv && data.data.greeting) {
                    // تقسيم النص الجديد إلى كلمات
                    const words = data.data.greeting.split(' ');

                    // إفراغ محتوى العنصر
                    welcomeTextDiv.innerHTML = '';

                    // إضافة كل كلمة كعنصر span جديد
                    words.forEach(word => {
                        const span = document.createElement('span');
                        span.className = 'animated-word';
                        span.textContent = word;
                        welcomeTextDiv.appendChild(span);
                    });
                }

                // تحديث حالة ظهور الشريط
                const isVisible = data.data.visible;
                console.log('حالة ظهور الشريط من الخادم:', isVisible);

                if (isVisible === false) {
                    // إخفاء الشريط
                    greetingBar.style.display = 'none';

                    // تحديث حالة التشيك بوكس
                    if (visibilityToggle) {
                        visibilityToggle.checked = false;
                    }
                } else {
                    // إظهار الشريط
                    greetingBar.style.display = 'block';

                    // تحديث حالة التشيك بوكس
                    if (visibilityToggle) {
                        visibilityToggle.checked = true;
                    }
                }
            }
        })
        .catch(error => {
            console.error('خطأ في جلب بيانات الشريط:', error);
        });
});
