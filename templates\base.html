<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>فضاء أساتذة العلوم الفيزيائية</title>
    <!-- إضافة أيقونة التبويب (Favicon) الخضراء -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/green-favicon.ico') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='images/green-favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='images/green-favicon-16x16.png') }}">
    <!-- استدعاء مكتبة Bootstrap 5 - هام لعمل التبويبات! -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='styles.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='horizontal-buttons.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='mobile-styles.css') }}" rel="stylesheet">
    {% block head %}{% endblock %}
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .auth-container {
            max-width: 500px;
            margin: auto;
            padding: 2rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .flash-message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            min-width: 300px;
        }
        .form-control {
            margin-bottom: 1rem;
        }
        .phone-input {
            display: flex;
            flex-direction: row;
            gap: 10px;
        }
        .phone-prefix {
            width: 80px;
            margin-right: auto;
        }
        .admin-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            padding: 1rem;
        }
        .navbar {
            background: linear-gradient(120deg, rgba(255, 255, 255, 0.9), rgba(240, 245, 255, 0.9)) !important;
            padding: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: relative;
            overflow: visible; /* تغيير من hidden إلى visible لإظهار الدوائر */
        }
        .navbar-brand {
            color: #1e88e5 !important;
            font-size: 1.5rem;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            transform: translateY(-2px);
        }

        .navbar-brand:hover .brand-icon {
            transform: rotate(10deg) scale(1.1);
            box-shadow: 0 5px 12px rgba(0,0,0,0.3);
        }

        .brand-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(45deg, #198754, #5cb85c); /* تغيير اللون إلى الأخضر */
            width: 50px; /* زيادة الحجم */
            height: 50px; /* زيادة الحجم */
            border-radius: 50%;
            box-shadow: 0 3px 8px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .brand-icon i {
            color: white;
            font-size: 1.5rem; /* زيادة حجم الأيقونة */
            transition: all 0.3s ease;
            animation: pulse 1.5s infinite; /* جعل الأيقونة متحركة دائمًا */
        }

        /* إزالة التأثير عند التحويم لأن الأيقونة متحركة دائمًا */
        .navbar-brand:hover .brand-icon i {
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
        .nav-link {
            color: #1e88e5 !important;
            font-weight: 500;
            padding: 0.5rem 1.2rem !important;
            margin: 0 0.3rem;
            border-radius: 20px;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            position: relative;
            z-index: 2;
        }
        .nav-link:hover {
            color: #1565c0 !important;
            background-color: rgba(30, 136, 229, 0.1);
            transform: translateY(-1px);
        }
        .navbar-nav {
            flex-grow: 1;
        }

        /* تعديل توزيع العناصر في شريط التنقل */
        .navbar > .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-collapse {
            justify-content: space-between;
        }

        .navbar .nav-item.dropdown.ms-auto {
            margin-right: auto;
            margin-left: auto;
        }
        .nav-item {
            display: flex;
            align-items: center;
        }
        .navbar-toggler {
            border-color: rgba(255,255,255,0.5);
        }
        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(255, 255, 255, 0.8)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }
        .badge {
            font-size: 0.7rem;
            padding: 0.35em 0.65em;
        }
        /* Message styles */
        .message-item {
            transition: all 0.3s ease;
            border: 1px solid #dee2e6 !important;
        }
        .message-item:hover {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .delete-message-btn {
            opacity: 0.7;
            transition: all 0.2s ease;
        }
        .delete-message-btn:hover {
            opacity: 1;
            transform: scale(1.1);
        }
        .attachment-section {
            background-color: #f8f9fa;
            padding: 0.5rem;
            border-radius: 4px;
            margin-top: 0.5rem;
        }
        .btn {
            font-size: 1rem;
        }
        .table th {
            font-weight: 600;
        }
    </style>
    <style>
        /* تنسيقات صورة البروفايل في شريط التنقل */
        .header-profile-img {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #1565c0;
            border: 2px solid rgba(255,255,255,0.8);
            overflow: hidden;
            position: relative;
            margin-right: 0;
            margin-left: 5px;
            transition: all 0.3s;
        }

        .header-profile-img:hover {
            transform: scale(1.05);
            border-color: white;
        }

        /* شريط التنقل */
        .navbar-collapse {
            justify-content: space-between;
            align-items: center;
        }

        /* مساحة إضافية للأيقونة على اليسار */
        .d-none.d-lg-block {
            margin-left: 20px;
        }

        .header-profile-link {
            display: flex;
            align-items: center;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 20px;
            color: white;
            transition: all 0.2s;
        }

        .header-profile-link:hover {
            background-color: rgba(255,255,255,0.1);
        }

        .fallback-text {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            font-size: 14px;
            font-weight: 600;
            color: white;
        }

        .header-profile-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .user-name {
            margin-right: 10px;
            font-weight: 500;
            color: white;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* تنسيق القائمة المنسدلة */
        .profile-dropdown .dropdown-menu,
        .dropdown-menu {
            margin-top: 10px;
            border-radius: 15px;
            border: none;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            min-width: 260px;
            padding: 0;
            transform: translateY(10px);
            transition: all 0.3s;
            opacity: 0.95;
            background: #ffffff;
            overflow: hidden;
        }

        .dropdown-header {
            background: linear-gradient(135deg, #1e88e5, #1565c0);
            color: white;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            padding: 10px;
        }

        .dropdown-header h6 {
            font-size: 0.9rem;
        }

        .dropdown-header small {
            font-size: 0.8rem;
            opacity: 0.9;
        }

        .dropdown-profile-img {
            width: 45px;
            height: 45px;
            overflow: hidden;
            position: relative;
            margin-bottom: 8px;
        }

        .dropdown-profile-img img,
        .dropdown-profile-img .fallback-text {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .dropdown-profile-img .fallback-text {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(255,255,255,0.2);
            font-size: 24px;
            font-weight: bold;
        }

        .dropdown-item {
            padding: 8px 20px;
            transition: all 0.2s;
            text-align: right;
            font-weight: 500;
            color: #3a3a3a;
            position: relative;
            z-index: 1;
            overflow: hidden;
        }

        .dropdown-item:hover {
            background: linear-gradient(to right, rgba(30, 136, 229, 0.1), transparent);
            color: #1565c0;
            transform: translateX(-5px);
        }

        .dropdown-item:active {
            background-color: #1e88e5;
            color: white;
        }

        .dropdown-item i {
            transition: all 0.3s;
            margin-left: 5px;
        }

        .dropdown-item:hover i {
            color: #1565c0;
            transform: scale(1.2);
        }

        .dropdown-divider {
            margin: 4px 15px;
            opacity: 0.8;
            border-color: #e0e6ed;
        }
    </style>
    <style>
        /* تنسيق زر عرض/إخفاء كلمة المرور */
        .position-relative {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            border: none;
            background: transparent;
            padding: 0 10px;
            color: #6c757d;
            z-index: 10;
        }

        .password-toggle:hover {
            color: #0d6efd;
        }

        .password-toggle:focus {
            box-shadow: none;
            outline: none;
        }

        /* تحديد هامش للنص داخل حقول كلمة المرور لإفساح المجال للزر */
        #current_password, #new_password {
            padding-left: 40px;
        }
    </style>
    <style>
        /* تنسيق زر عرض/إخفاء كلمة المرور */
        .password-toggle-btn {
            color: #6c757d;
            padding: 0 10px;
            border: none;
            background: transparent;
            text-decoration: none;
            transition: color 0.2s;
        }

        .password-toggle-btn:hover {
            color: #0d6efd;
        }

        .password-toggle-btn:focus {
            box-shadow: none;
        }

        /* الدوائر الزخرفية */
        .decoration-circle {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(45deg, rgba(30, 136, 229, 0.15), rgba(100, 181, 246, 0.15));
            z-index: 1;
            pointer-events: none; /* لتجنب التداخل مع العناصر الأخرى */
        }

        .circle-1 {
            width: 150px;
            height: 150px;
            top: -30px;
            right: -30px;
            animation: float 8s ease-in-out infinite;
            opacity: 0.8;
        }

        .circle-2 {
            width: 80px;
            height: 80px;
            bottom: 20px;
            right: 30%;
            animation: float 6s ease-in-out 1s infinite;
            opacity: 0.8;
        }

        .circle-3 {
            width: 120px;
            height: 120px;
            bottom: -30px;
            left: 10%;
            animation: float 7s ease-in-out 0.5s infinite;
            opacity: 0.8;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-15px);
            }
        }

        /* تأثيرات حركية للأزرار */
        .btn-profile, .btn-logout {
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .btn-logout:hover {
            transform: rotate(360deg);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-profile:hover {
            transform: scale(1.15);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-profile i, .btn-logout i {
            transition: all 0.3s ease;
        }

        .btn-profile:hover i {
            animation: pulse-light 1.5s infinite;
        }

        .btn-logout:hover i {
            animation: bounce 1s infinite;
        }

        /* تأثيرات حركية لزر الرسائل */
        .btn-message {
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .btn-message::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%);
            opacity: 0;
            transition: all 0.4s ease;
        }

        .btn-message:hover::after {
            opacity: 1;
            transform: scale(1.5);
        }

        .btn-message:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-message i {
            transition: all 0.3s ease;
        }

        .btn-message:hover i {
            animation: envelope-shake 1s infinite;
        }

        /* تأثير وميض عند وصول رسالة جديدة */
        @keyframes message-flash {
            0% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.5); }
            50% { box-shadow: 0 0 20px rgba(255, 193, 7, 0.8); }
            100% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.5); }
        }

        .new-message-flash {
            animation: message-flash 0.6s ease-in-out 3;
        }

        @keyframes envelope-shake {
            0%, 100% { transform: rotate(0); }
            25% { transform: rotate(-10deg); }
            75% { transform: rotate(10deg); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-5px);
            }
            60% {
                transform: translateY(-3px);
            }
        }

        @keyframes pulse-light {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* تحسين شكل الإشعار */
        .notification-badge-pill {
            display: none; /* إخفاء هذا التنسيق لاستخدام التنسيق البسيط */
        }

        @keyframes badge-pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.15); }
            100% { transform: scale(1); }
        }

        /* تأثير وميض قوي عند وصول رسالة جديدة */
        @keyframes badge-flash {
            0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
            100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
        }

        .new-badge-flash {
            animation: none;
        }
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <nav class="navbar navbar-expand-lg">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('welcome') }}" title="الصفحة الرئيسية">
                    <span class="brand-icon">
                        <i class="fas fa-chalkboard-teacher"></i>
                    </span>
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <!-- القائمة الرئيسية -->
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        {% if current_user.is_authenticated %}
                            <li class="nav-item">
                                <a class="nav-link d-flex align-items-center justify-content-center" href="{{ url_for('welcome') }}" title="الرئيسية">
                                    <i class="fas fa-home" style="font-size: 1.4rem;"></i>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link d-flex align-items-center justify-content-center" href="{{ url_for('dashboard') }}" title="جدول التوقيت">
                                    <i class="fas fa-calendar-alt" style="font-size: 1.4rem;"></i>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link d-flex align-items-center justify-content-center" href="{{ url_for('educational_files') }}" title="الملفات التعليمية">
                                    <i class="fas fa-file-alt" style="font-size: 1.4rem;"></i>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link d-flex align-items-center justify-content-center" href="{{ url_for('whatsapp_chat') }}" title="الدردشة">
                                    <i class="fab fa-whatsapp" style="font-size: 1.4rem;"></i>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link d-flex align-items-center justify-content-center" href="{{ url_for('progress_page_for_user', user_id=current_user.id) }}" title="تقدم البرنامج">
                                    <i class="fas fa-chart-line" style="font-size: 1.4rem;"></i>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link d-flex align-items-center justify-content-center" href="{{ url_for('concerns_page') }}" title="انشغالات الأساتذة">
                                    <i class="fas fa-comment-dots" style="font-size: 1.4rem;"></i>
                                </a>
                            </li>
                            {% if current_user.is_admin %}
                            <li class="nav-item">
                                <a class="nav-link d-flex align-items-center justify-content-center" href="{{ url_for('teacher_schedules') }}" title="التوقيت الأسبوعي للأستاذ(ة)">
                                    <i class="fas fa-clock" style="font-size: 1.4rem; color: #28a745;"></i>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link d-flex align-items-center justify-content-center" href="{{ url_for('messages') }}" title="الرسائل">
                                    <i class="fas fa-envelope" style="font-size: 1.4rem; color: #28a745;"></i>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link d-flex align-items-center justify-content-center" href="{{ url_for('progress_dashboard') }}" title="مدى التقدم">
                                    <i class="fas fa-chart-bar" style="font-size: 1.4rem; color: #28a745;"></i>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link d-flex align-items-center justify-content-center" href="{{ url_for('admin_panel') }}" title="لوحة المشرف">
                                    <i class="fas fa-users-cog" style="font-size: 1.4rem; color: #28a745;"></i>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link d-flex align-items-center justify-content-center" href="{{ url_for('admin_concerns') }}" title="إدارة انشغالات الأساتذة">
                                    <i class="fas fa-tasks" style="font-size: 1.4rem; color: #28a745;"></i>
                                </a>
                            </li>
                            {% endif %}
                        {% endif %}
                    </ul>
                </div>
            </div>
        </nav>

        <!-- حاوية الأزرار الثلاثة (أفقية في جميع أحجام الشاشات) -->
        {% if request.path != '/login' and request.path != '/register' %}
        <div class="vertical-buttons-container">
            <!-- زر الرسائل (الأول في الترتيب العمودي) -->
            {% if current_user.is_authenticated %}
            <a href="{{ url_for('messages') }}" class="btn btn-warning btn-message" title="الرسائل">
                <i class="fas fa-envelope" style="color: white;"></i>
                <span id="nav-unread-count" class="badge bg-danger" style="position: absolute; top: 0; right: 0; font-size: 0.8rem; border-radius: 50%; width: 18px; height: 18px; display: {{ 'flex' if current_user.get_unread_messages_count() > 0 else 'none' }}; align-items: center; justify-content: center; padding: 0;">
                    {{ current_user.get_unread_messages_count() if current_user.get_unread_messages_count() > 0 else '' }}
                </span>
            </a>
            {% endif %}

            <!-- زر الملف الشخصي (الثاني في الترتيب العمودي) -->
            <div class="dropdown">
                <a href="#" class="btn btn-primary btn-profile rounded-circle" id="profileDropdownMobile" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-user"></i>
                </a>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="profileDropdownMobile" style="border-radius: 15px; overflow: hidden; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2); border: none; margin-top: 10px;">
                    {% if current_user.is_authenticated %}
                    <div class="dropdown-header text-center p-3" style="background: linear-gradient(45deg, #1976d2, #64b5f6); color: white;">
                        <div class="dropdown-profile-img mx-auto mb-2" style="width: 70px; height: 70px; border-radius: 50%; overflow: hidden; border: 3px solid white; margin: 0 auto;">
                            {% if current_user.profile_picture %}
                                {% if '/static/images/' in current_user.profile_picture %}
                                    <img src="{{ current_user.profile_picture }}" alt="{{ current_user.teacher_name if current_user.teacher_name else 'المستخدم' }}" class="rounded-circle" style="width: 100%; height: 100%; object-fit: cover;">
                                {% else %}
                                    <img src="{{ url_for('static', filename='uploads/' + current_user.profile_picture.replace('/static/uploads/', '')) }}" alt="{{ current_user.teacher_name if current_user.teacher_name else 'المستخدم' }}" class="rounded-circle" style="width: 100%; height: 100%; object-fit: cover;">
                                {% endif %}
                            {% else %}
                                <img src="/static/images/male-profile.png" alt="{{ current_user.teacher_name if current_user.teacher_name else 'المستخدم' }}" class="rounded-circle" style="width: 100%; height: 100%; object-fit: cover;">
                            {% endif %}
                        </div>
                        <h6 class="mb-0 fw-bold">{{ current_user.teacher_name if current_user.teacher_name else 'المستخدم' }}</h6>
                        <small>{{ current_user.position or 'أستاذ(ة) تعليم متوسط' }}</small>
                    </div>
                    <div class="dropdown-divider m-0"></div>
                    <li><a class="dropdown-item py-2" href="{{ url_for('profile', user_id=current_user.id) }}"><i class="fas fa-user-circle me-2 text-primary"></i> معلومات الأستاذ</a></li>
                    <li>
                        <a class="dropdown-item py-2" href="{{ url_for('messages') }}">
                            <i class="fas fa-envelope me-2 text-danger"></i> الرسائل
                            <span id="dropdown-unread-count-mobile" class="badge bg-danger rounded-pill ms-2" style="display: {{ 'inline' if current_user.get_unread_messages_count() > 0 else 'none' }};">
                                {{ current_user.get_unread_messages_count() if current_user.get_unread_messages_count() > 0 else '' }}
                            </span>
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item py-2" href="{{ url_for('whatsapp_chat') }}">
                            <i class="fab fa-whatsapp me-2" style="color: rgb(30, 136, 229);"></i> الدردشة
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item py-2" href="{{ url_for('concerns_page') }}">
                            <i class="fas fa-comment-dots me-2" style="color: rgb(30, 136, 229);"></i> انشغالات الأساتذة
                        </a>
                    </li>
                    <div class="dropdown-divider m-0"></div>
                    <li><a class="dropdown-item py-2" href="#" data-bs-toggle="modal" data-bs-target="#changePasswordModal"><i class="fas fa-key me-2 text-warning"></i> تغيير كلمة المرور</a></li>
                    <li><a class="dropdown-item py-2" href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt me-2 text-danger"></i> تسجيل الخروج</a></li>
                    {% else %}
                    <div class="dropdown-header text-center p-3" style="background: linear-gradient(45deg, #1976d2, #64b5f6); color: white;">
                        <div class="dropdown-profile-img mx-auto mb-2" style="width: 70px; height: 70px; border-radius: 50%; overflow: hidden; border: 3px solid white; margin: 0 auto;">
                            <img src="/static/images/male-profile.png" alt="المستخدم" class="rounded-circle" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <h6 class="mb-0 fw-bold">مرحبا بك</h6>
                        <small>الرجاء تسجيل الدخول</small>
                    </div>
                    <div class="dropdown-divider m-0"></div>
                    <li><a class="dropdown-item py-2" href="{{ url_for('login') }}"><i class="fas fa-sign-in-alt me-2 text-success"></i> تسجيل الدخول</a></li>
                    <li><a class="dropdown-item py-2" href="{{ url_for('register') }}"><i class="fas fa-user-plus me-2 text-primary"></i> إنشاء حساب</a></li>
                    {% endif %}
                </ul>
            </div>

            <!-- زر الخروج (الثالث في الترتيب العمودي) -->
            {% if current_user.is_authenticated %}
            <a href="{{ url_for('logout') }}" class="btn btn-danger btn-logout" title="تسجيل الخروج">
                <i class="fas fa-sign-out-alt"></i>
            </a>
            {% endif %}
        </div>

        <!-- إضافة عنصر صوت للتنبيه -->
        {% if current_user.is_authenticated %}
        <audio id="notification-sound" preload="auto" style="display: none;">
            <source src="{{ url_for('static', filename='sounds/notification.mp3') }}" type="audio/mpeg">
        </audio>
        {% endif %}
        {% endif %}

        <div class="flash-messages">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% set displayed_messages = [] %}
                    {% for category, message in messages %}
                        {% if message not in displayed_messages %}
                            <div class="alert alert-{{ category if category != 'message' else 'info' }}" role="alert"
                                 style="position: fixed; top: 20px; right: 20px; z-index: 1050; padding: 8px 16px; border-radius: 4px; font-size: 14px; max-width: 250px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                {{ message }}
                            </div>
                            {% set _ = displayed_messages.append(message) %}
                        {% endif %}
                    {% endfor %}
                {% endif %}
            {% endwith %}
        </div>
        {% block content %}{% endblock %}
    </div>

    {% if current_user.is_authenticated %}
    <!-- Modal تغيير كلمة المرور -->
    <div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="changePasswordModalLabel">تغيير كلمة المرور</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="change-password-form">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                        <div class="mb-3">
                            <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                            <div class="position-relative">
                                <input type="password" class="form-control" id="current_password" name="current_password" value="••••••••" readonly>
                                <button type="button" class="btn password-toggle" data-target="current_password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                            <div class="position-relative">
                                <input type="password" class="form-control" id="new_password" name="new_password"
                                       placeholder="اترك هذا الحقل فارغاً إذا كنت لا ترغب في تغيير كلمة المرور">
                                <button type="button" class="btn password-toggle" data-target="new_password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="save-password-btn">حفظ التغييرات</button>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- إضافة jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- مكتبة Bootstrap JS - هامة لعمل التبويبات! -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/messages.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Select alerts within the flash-messages container
            var flashAlerts = document.querySelectorAll('.flash-messages .alert');
            flashAlerts.forEach(function(alert) {
                // Automatically remove the alert after 4 seconds
                setTimeout(function() {
                    alert.style.transition = 'opacity 0.5s ease-out';
                    alert.style.opacity = '0';
                    setTimeout(function() { alert.remove(); }, 500); // Remove after fade animation
                }, 4000); // 4-second delay before starting fade out
            });

            // تحديث عدد الرسائل غير المقروءة في القائمة المنسدلة أيضًا
            function updateDropdownUnreadCount() {
                const navBadge = document.getElementById('nav-unread-count');
                const dropdownBadge = document.getElementById('dropdown-unread-count');
                const mobileBadge = document.getElementById('dropdown-unread-count-mobile');

                if (navBadge) {
                    // تحديث شارة القائمة المنسدلة للشاشات الكبيرة
                    if (dropdownBadge) {
                        if (navBadge.style.display !== 'none') {
                            dropdownBadge.textContent = navBadge.textContent;
                            dropdownBadge.style.display = 'inline-flex';
                        } else {
                            dropdownBadge.style.display = 'none';
                        }
                    }

                    // تحديث شارة القائمة المنسدلة للشاشات الصغيرة
                    if (mobileBadge) {
                        if (navBadge.style.display !== 'none') {
                            mobileBadge.textContent = navBadge.textContent;
                            mobileBadge.style.display = 'inline-flex';
                        } else {
                            mobileBadge.style.display = 'none';
                        }
                    }
                }
            }

            // تحديث كل ثانية
            setInterval(updateDropdownUnreadCount, 1000);
            updateDropdownUnreadCount(); // تحديث فوري
        });
    </script>
    {% if current_user.is_authenticated %}
    <script>
    function updateNavUnreadCount() {
        fetch('{{ url_for("get_unread_count") }}')
            .then(response => response.json())
            .then(data => {
                const badge = document.getElementById('nav-unread-count');
                if (!badge) return;

                // تخزين القيمة القديمة لعدد الرسائل
                const oldCount = window.lastNavCount || 0;

                if (data.count > 0) {
                    // تحديث عدد الرسائل
                    badge.textContent = data.count;
                    badge.style.display = 'flex';

                    // التحقق مما إذا كان هناك رسائل جديدة (زيادة في العدد)
                    if (data.count > oldCount) {
                        console.log('تم استلام رسائل جديدة! العدد القديم:', oldCount, 'العدد الجديد:', data.count);

                        // تشغيل الصوت فوراً عند وصول رسالة جديدة
                        playNotificationSound();

                        // تحديث الرسائل إذا كنا في صفحة الرسائل
                        if (window.updateReceivedMessages) {
                            window.updateReceivedMessages();
                        }
                    }
                } else {
                    badge.style.display = 'none';
                }

                // تحديث العداد المخزن
                window.lastNavCount = data.count;
            })
            .catch(error => {
                console.error('خطأ في التحقق من الرسائل:', error);
            });
    }

    // دالة منفصلة لتشغيل صوت التنبيه
    function playNotificationSound() {
        const audio = document.getElementById('notification-sound');
        if (!audio) {
            console.log('عنصر الصوت غير موجود!');
            return;
        }

        // إيقاف الصوت إذا كان قيد التشغيل
        audio.pause();
        // إعادة تعيين الصوت للبداية
        audio.currentTime = 0;

        // محاولة تشغيل الصوت بشكل متكرر إذا فشل
        const attemptPlay = function(retryCount = 3) {
            const playPromise = audio.play();

            if (playPromise !== undefined) {
                playPromise.then(_ => {
                    console.log('تم تشغيل صوت التنبيه بنجاح');
                }).catch(error => {
                    console.log('تم منع تشغيل الصوت:', error);
                    if (retryCount > 0) {
                        // محاولة التشغيل مرة أخرى بعد فترة قصيرة
                        setTimeout(() => attemptPlay(retryCount - 1), 300);
                    }
                });
            }
        };

        // بدء المحاولة الأولى
        attemptPlay();
    }

    // تحديث العدد كل 5 ثواني
    setInterval(updateNavUnreadCount, 5000);
    updateNavUnreadCount(); // تحديث فوري عند تحميل الصفحة

    // التأكد من تحميل صوت التنبيه
    document.addEventListener('DOMContentLoaded', function() {
        const audio = document.getElementById('notification-sound');
        if (audio) {
            // التأكد من تحميل الصوت مسبقًا
            audio.load();
            console.log('تم تحميل ملف الصوت');
        }
    });
    </script>
    {% endif %}
    <script>
        // تسجيل Service Worker
        if ('serviceWorker' in navigator && 'PushManager' in window) {
            navigator.serviceWorker.register('/static/sw.js')
                .then(function(registration) {
                    console.log('Service Worker تم تسجيله:', registration);

                    // طلب إذن الإشعارات
                    Notification.requestPermission().then(function(permission) {
                        if (permission === 'granted') {
                            // الحصول على مفتاح VAPID العام
                            fetch('/vapid-public-key')
                                .then(response => response.json())
                                .then(data => {
                                    // تسجيل الاشتراك في الإشعارات
                                    registration.pushManager.subscribe({
                                        userVisibleOnly: true,
                                        applicationServerKey: urlBase64ToUint8Array(data.publicKey)
                                    })
                                    .then(function(subscription) {
                                        // إرسال الاشتراك إلى الخادم
                                        fetch('/subscribe', {
                                            method: 'POST',
                                            body: JSON.stringify(subscription),
                                            headers: {
                                                'Content-Type': 'application/json'
                                            }
                                        });
                                    });
                                });
                        }
                    });
                })
                .catch(function(error) {
                    console.log('فشل تسجيل Service Worker:', error);
                });
        }

        // تحويل مفتاح VAPID من Base64 إلى Uint8Array
        function urlBase64ToUint8Array(base64String) {
            const padding = '='.repeat((4 - base64String.length % 4) % 4);
            const base64 = (base64String + padding)
                .replace(/\-/g, '+')
                .replace(/_/g, '/');

            const rawData = window.atob(base64);
            const outputArray = new Uint8Array(rawData.length);

            for (let i = 0; i < rawData.length; ++i) {
                outputArray[i] = rawData.charCodeAt(i);
            }
            return outputArray;
        }
    </script>
    <style>
        .dropdown-toggle::after {
            display: none;
        }

        /* تخصيص الزر عند فتح القائمة */
        .dropdown-toggle.show {
            background-color: rgba(255, 255, 255, 0.15);
        }

        /* تأثيرات المؤشر */
        .nav-link, .dropdown-item, .header-profile-img {
            cursor: pointer;
        }

        /* تنسيق زر عرض/إخفاء كلمة المرور */
        .password-toggle-btn {
            color: #6c757d;
            padding: 0 10px;
            border: none;
            background: transparent;
            text-decoration: none;
            transition: color 0.2s;
        }

        .password-toggle-btn:hover {
            color: #0d6efd;
        }

        .password-toggle-btn:focus {
            box-shadow: none;
        }

        /* تنسيق الأزرار الثلاثة في وضع الهاتف - أفقية ومحاذية لليسار */
        @media (max-width: 768px) {
            .vertical-buttons-container {
                top: 25px !important; /* زيادة المسافة من الأعلى لتتناسب مع الزر الأخضر */
                left: 10px !important;
                gap: 10px !important;
                display: flex !important;
                flex-direction: row !important;
                position: absolute !important;
                z-index: 1050 !important;
            }

            .vertical-buttons-container .btn {
                width: 35px !important;
                height: 35px !important;
                font-size: 0.8rem !important;
            }
        }

        /* تأكيد إضافي على أن الأزرار أفقية في وضع الهاتف */
        @media (max-width: 768px) {
            .vertical-buttons-container {
                flex-direction: row !important;
            }
        }
    </style>
    <script>
        // التأكد من أن الأزرار أفقية في جميع أحجام الشاشات
        function ensureHorizontalButtons() {
            const buttonsContainer = document.querySelector('.vertical-buttons-container');
            if (buttonsContainer) {
                buttonsContainer.style.flexDirection = 'row';

                if (window.innerWidth <= 768) {
                    // تنسيق للشاشات الصغيرة
                    buttonsContainer.style.top = '15px'; // لتتناسب مع الزر الأخضر
                    buttonsContainer.style.left = '10px';
                    buttonsContainer.style.gap = '10px';
                } else {
                    // تنسيق للشاشات الكبيرة
                    buttonsContainer.style.top = '20px';
                    buttonsContainer.style.left = '20px';
                    buttonsContainer.style.gap = '15px';
                }

                buttonsContainer.style.position = 'absolute';
                buttonsContainer.style.zIndex = '1050';
            }
        }

        // مراقب لأي تغيير في صورة البروفايل وتحديثها في النافبار
        document.addEventListener('DOMContentLoaded', function() {
            // التأكد من أن الأزرار أفقية في جميع أحجام الشاشات عند تحميل الصفحة
            ensureHorizontalButtons();

            // التأكد من أن الأزرار أفقية في جميع أحجام الشاشات عند تغيير حجم النافذة
            window.addEventListener('resize', ensureHorizontalButtons);
            // تعريف حدث مخصص للتحديث
            window.updateProfileImageNav = function(newImageUrl) {
                // تحديث جميع صور البروفايل في النافبار
                const profileImages = document.querySelectorAll('.header-profile-img img');
                const dropdownImages = document.querySelectorAll('.dropdown-profile-img img');

                if (newImageUrl) {
                    // إذا كان هناك صورة جديدة
                    profileImages.forEach(img => {
                        img.src = newImageUrl;
                    });

                    dropdownImages.forEach(img => {
                        img.src = newImageUrl;
                    });

                    // إخفاء النص البديل في حالة وجوده
                    document.querySelectorAll('.header-profile-img .fallback-text').forEach(el => {
                        el.style.display = 'none';
                    });

                    document.querySelectorAll('.dropdown-profile-img .fallback-text').forEach(el => {
                        el.style.display = 'none';
                    });
                } else {
                    // في حالة إزالة الصورة، إظهار النص البديل
                    const userInitial = '{{ current_user.teacher_name[0] }}';

                    document.querySelectorAll('.header-profile-img').forEach(container => {
                        container.innerHTML = '<div class="fallback-text">' + userInitial + '</div>';
                    });

                    document.querySelectorAll('.dropdown-profile-img').forEach(container => {
                        container.innerHTML = '<div class="fallback-text rounded-circle">' + userInitial + '</div>';
                    });
                }
            };

            {% if current_user.is_authenticated %}
            // معالجة زر عرض/إخفاء كلمة المرور
            document.querySelectorAll('.password-toggle').forEach(button => {
                button.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-target');
                    const passwordInput = document.getElementById(targetId);

                    if (passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        this.innerHTML = '<i class="fas fa-eye-slash"></i>';
                    } else {
                        passwordInput.type = 'password';
                        this.innerHTML = '<i class="fas fa-eye"></i>';
                    }
                });
            });

            // معالجة إظهار مربع حوار تغيير كلمة المرور
            const changePasswordModal = document.getElementById('changePasswordModal');
            if (changePasswordModal) {
                changePasswordModal.addEventListener('shown.bs.modal', function () {
                    // التركيز على حقل كلمة المرور الجديدة تلقائياً
                    document.getElementById('new_password').focus();
                });
            }

            // معالجة زر حفظ تغيير كلمة المرور
            const savePasswordBtn = document.getElementById('save-password-btn');
            if (savePasswordBtn) {
                savePasswordBtn.addEventListener('click', function() {
                    const form = document.getElementById('change-password-form');
                    const newPassword = document.getElementById('new_password').value;

                    // إذا كان حقل كلمة المرور الجديدة فارغًا، نعرض تنبيهًا ونتوقف
                    if (!newPassword) {
                        showCustomAlert('لم يتم إجراء أي تغيير. كلمة المرور الجديدة فارغة.', 'info');
                        const modal = bootstrap.Modal.getInstance(document.getElementById('changePasswordModal'));
                        if (modal) modal.hide();
                        return;
                    }

                    // إظهار مؤشر التحميل
                    savePasswordBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
                    savePasswordBtn.disabled = true;

                    // إرسال طلب AJAX لتغيير كلمة المرور
                    fetch('/profile/update_password', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': form.querySelector('input[name="csrf_token"]').value
                        },
                        body: JSON.stringify({
                            new_password: newPassword
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        // إعادة زر الحفظ إلى حالته الطبيعية
                        savePasswordBtn.innerHTML = 'حفظ التغييرات';
                        savePasswordBtn.disabled = false;

                        if (data.success) {
                            // إغلاق النافذة المنبثقة
                            const modal = bootstrap.Modal.getInstance(document.getElementById('changePasswordModal'));
                            modal.hide();

                            // إظهار رسالة النجاح
                            showCustomAlert('تم حفظ التغييرات بنجاح', 'success');

                            // إعادة تعيين النموذج
                            form.reset();
                            // إعادة تعيين حقل كلمة المرور الحالية
                            document.getElementById('current_password').value = '••••••••';
                        } else {
                            showCustomAlert(data.message || 'حدث خطأ أثناء تغيير كلمة المرور', 'danger');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        savePasswordBtn.innerHTML = 'حفظ التغييرات';
                        savePasswordBtn.disabled = false;
                        showCustomAlert('حدث خطأ أثناء تغيير كلمة المرور', 'danger');
                    });
                });
            }

            // دالة لعرض رسالة تنبيه مخصصة
            function showCustomAlert(message, type) {
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type}`;
                alertDiv.style.position = 'fixed';
                alertDiv.style.top = '20px';
                alertDiv.style.right = '20px';
                alertDiv.style.zIndex = '1050';
                alertDiv.style.padding = '8px 16px';
                alertDiv.style.borderRadius = '4px';
                alertDiv.style.fontSize = '14px';
                alertDiv.style.maxWidth = '250px';
                alertDiv.style.textAlign = 'center';
                alertDiv.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                alertDiv.innerHTML = message;

                document.body.appendChild(alertDiv);

                setTimeout(() => {
                    alertDiv.style.transition = 'opacity 0.5s ease-in-out';
                    alertDiv.style.opacity = '0';
                    setTimeout(() => alertDiv.remove(), 500);
                }, 3000);
            }
            {% endif %}
        });
    </script>
</body>
</html>
