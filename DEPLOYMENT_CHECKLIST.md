# قائمة التحقق من النشر على Render.com

## ✅ التعديلات المطلوبة (تمت):

### 1. ملفات التكوين:
- ✅ `config.py` - تم تحديثه لاستخدام متغيرات البيئة
- ✅ `render.yaml` - تم إنشاؤه لتكوين Render
- ✅ `runtime.txt` - تم إنشاؤه لتحديد إصدار Python
- ✅ `build.sh` - تم إنشاؤه لبناء التطبيق
- ✅ `.env.example` - تم إنشاؤه لتوضيح المتغيرات المطلوبة

### 2. إعدادات الأمان:
- ✅ `app.py` - تم تحديث إعدادات الأمان للإنتاج
- ✅ HTTPS cookies في الإنتاج
- ✅ متغيرات البيئة للمعلومات الحساسة

### 3. قاعدة البيانات:
- ✅ دعم PostgreSQL جاهز
- ✅ `create_tables.py` جاهز لإنشاء الجداول

## 🚀 خطوات النشر:

### 1. رفع الكود إلى GitHub:
```bash
git add .
git commit -m "تحضير المشروع للنشر على Render"
git push origin main
```

### 2. إنشاء قاعدة البيانات على Render:
- اذهب إلى [render.com](https://render.com)
- اضغط "New +" → "PostgreSQL"
- اختر اسم: `flask-app-db`
- اختر المنطقة المناسبة

### 3. إنشاء Web Service:
- اضغط "New +" → "Web Service"
- اربط مستودع GitHub
- اختر المستودع

### 4. إعدادات Web Service:
- **Name**: `flask-app` (أو أي اسم تريده)
- **Environment**: Python 3
- **Build Command**: `./build.sh`
- **Start Command**: `gunicorn app:app --bind 0.0.0.0:$PORT`

### 5. ربط قاعدة البيانات:
- في إعدادات Web Service
- اذهب إلى "Environment"
- اربط قاعدة البيانات PostgreSQL

### 6. إضافة متغيرات البيئة:
```
SECRET_KEY=<سيتم إنشاؤه تلقائياً>
CLOUDINARY_CLOUD_NAME=dho0fkwf4
CLOUDINARY_API_KEY=726728831258746
CLOUDINARY_API_SECRET=bKLcmXjcyRDO_Uw9tfzKeeffJc4
GOOGLE_CLIENT_ID=1074418425224-cg6kttp07a7vfgm7vjdtmpbvch3tng94.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=https://your-app-name.onrender.com/oauth2callback
VAPID_PUBLIC_KEY=BHuFRZ0YE9pQxjVQBfp80fT1wkG4bsjyUUBvQ-9_v2lPm0Jf_WMJcqfv_UEitq6SCOLstxL5q-Dz9bXcwlFdRG8
VAPID_PRIVATE_KEY=DKkJuZ1vLU3eHaX6dDX4TsZBwZF09Wl-_uJvBTWaOIw
VAPID_CLAIM_EMAIL=<EMAIL>
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=rkyieuepofpiuvcj
MAIL_DEFAULT_SENDER=<EMAIL>
```

### 7. النشر:
- اضغط "Create Web Service"
- انتظر حتى يكتمل البناء والنشر

## ⚠️ ملاحظات مهمة:

1. **تحديث GOOGLE_REDIRECT_URI**: 
   - بعد النشر، استبدل `your-app-name` بالاسم الفعلي لتطبيقك

2. **إنشاء مدير أول**:
   - استخدم `reset_admin.py` لإنشاء حساب مدير

3. **مراقبة السجلات**:
   - تابع السجلات من لوحة تحكم Render للتأكد من عدم وجود أخطاء

4. **النسخ الاحتياطية**:
   - Render يوفر نسخ احتياطية تلقائية لقاعدة البيانات

## 🔧 استكشاف الأخطاء:

- تحقق من السجلات في Render Dashboard
- تأكد من أن جميع متغيرات البيئة مضبوطة
- تأكد من ربط قاعدة البيانات بشكل صحيح
