{% extends "base.html" %}
{% block title %}التقدم{% endblock %}
{% block head %}
<script src="{{ url_for('static', filename='js/mobile-progress.js') }}"></script>
{% endblock %}
{% block content %}
<div class="container-fluid py-4">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" id="csrf-token">

    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-4 col-lg-3">
            <div class="card border-0 shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center py-3">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-chart-line me-2"></i>
                        ملخص التقدم
                    </h5>
                </div>

                <!-- محتوى القائمة الجانبية -->
                <div class="card-body p-0">
                    <!-- الأسبوع الحالي حسب التدرج السنوي -->
                    <div class="p-3 border-bottom">
                        <h6 class="mb-3 fw-bold text-primary d-flex align-items-center">
                            <i class="fas fa-calendar-week me-2"></i>
                            الأسبوع الحالي حسب التدرج السنوي
                        </h6>
                        <div class="p-3 bg-light rounded-3 text-center">
                            <div class="mb-2">
                                <span class="badge bg-primary fs-4 px-4 py-3 rounded-pill shadow-sm" id="current-week-display">30</span>
                            </div>
                            <div class="text-muted small mt-2">
                                <i class="fas fa-info-circle me-1"></i>
                                <span id="current-month-week">الأسبوع الثاني من شهر أفريل</span>
                            </div>
                            <div class="d-none">
                                <span id="currentMonth"></span>
                                <span id="currentWeek"></span>
                                <span id="currentTotalWeek"></span>
                            </div>
                        </div>
                    </div>

                    <!-- آخر الحصص التعليمية -->
                    <div id="last-lessons-container" class="p-3">
                        <h6 class="mb-3 fw-bold text-primary d-flex align-items-center">
                            <i class="fas fa-book-open me-2"></i>
                            أسبوع آخر الحصص التعليمية
                        </h6>
                        <!-- سيتم ملء هذا القسم ديناميكيًا -->
                        <div class="text-center p-3 text-muted small" id="last-lessons-loading">
                            <div class="d-flex justify-content-center align-items-center">
                                <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                    <span class="visually-hidden">جاري التحميل...</span>
                                </div>
                                <span>جاري تحميل البيانات...</span>
                            </div>
                        </div>
                        <div id="last-lessons-content">
                            <!-- سيتم ملؤه بواسطة JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الجدول الرئيسي -->
        <div class="col-md-8 col-lg-9">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <h5 class="mb-0"><i class="fas fa-list-alt me-2"></i>آخر حصة تعليمية</h5>
                        <button type="button" class="btn btn-outline-light ms-3" id="yearly-progress-btn">
                            <i class="fas fa-calendar-alt me-1"></i> التدرج السنوي
                        </button>
                    </div>
                    <button type="button" class="btn btn-outline-light" id="btn-add-progress">
                        <i class="fas fa-plus me-1"></i> إضافة حصة
                    </button>
                </div>
                <div class="card-body">
                    <div class="progress-content">
                        <div id="no-progress-alert" class="alert alert-info {{ 'show' if not progress_items else 'd-none' }}">
                            <i class="fas fa-info-circle me-2"></i>
                            لا يوجد سجلات تقدم حاليًا. يمكنك إضافة سجل جديد بالنقر على زر "إضافة حصة".
                        </div>

                        <div class="table-responsive {{ 'd-none' if not progress_items else '' }}" id="progress-table-container">
                            <table class="table table-bordered table-hover align-middle" id="progress-table">
                                <thead class="table-light">
                                    <tr>
                                        <th width="5%" class="text-center">#</th>
                                        <th width="15%">المستوى الدراسي</th>
                                        <th width="10%" class="text-center">الشهر</th>
                                        <th width="10%" class="text-center">الأسبوع</th>
                                        <th width="40%">عنوان الحصة</th>
                                        <th width="20%" class="text-center">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم ملء هذه المنطقة ديناميكيًا -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة/تعديل سجل التقدم -->
<div class="modal fade" id="progressModal" tabindex="-1" aria-labelledby="progressModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="progressModalLabel">إضافة سجل تقدم جديد</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="progressForm">
                    <input type="hidden" id="progress-id" name="progress_id" value="">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <input type="hidden" id="month" name="month" value="">
                    <input type="hidden" id="week" name="week" value="">
                    <input type="hidden" id="yearly-progress-id" name="yearly_progress_id" value="0">
                    <input type="hidden" id="is-merged" name="is_merged" value="false">
                    <input type="hidden" id="lesson-type" name="lesson_type" value="custom">

                    <div class="mb-3">
                        <label for="level-select" class="form-label">المستوى الدراسي</label>
                        <select class="form-select" id="level-select" name="level" required>
                            <option value="" selected disabled>-- اختر المستوى --</option>
                            <option value="السنة الأولى متوسط">السنة الأولى متوسط</option>
                            <option value="السنة الثانية متوسط">السنة الثانية متوسط</option>
                            <option value="السنة الثالثة متوسط">السنة الثالثة متوسط</option>
                            <option value="السنة الرابعة متوسط">السنة الرابعة متوسط</option>
                        </select>
                        <div class="invalid-feedback">
                            يرجى اختيار المستوى الدراسي
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="lesson-title-select" class="form-label">عنوان الحصة</label>
                        <select class="form-select" id="lesson-title-select" name="lesson_title" required>
                            <option value="" selected disabled>-- اختر المستوى أولاً --</option>
                        </select>
                        <div class="invalid-feedback">
                            يرجى اختيار عنوان الحصة
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="save-progress">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal التدرج السنوي -->
<div class="modal fade" id="yearlyProgressModal" tabindex="-1" aria-labelledby="yearlyProgressModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="yearlyProgressModalLabel">التدرج السنوي</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex align-items-center gap-3 mb-3">
                    <!-- قائمة منسدلة لاختيار السنة الدراسية -->
                    <label for="year-selector-progress" class="form-label mb-0">السنة الدراسية:</label>
                    <select id="year-selector-progress" class="form-select" style="width: auto;">
                        <option value="1">السنة الأولى متوسط</option>
                        <option value="2">السنة الثانية متوسط</option>
                        <option value="3">السنة الثالثة متوسط</option>
                        <option value="4">السنة الرابعة متوسط</option>
                    </select>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered text-center yearly-progress-table">
                        <colgroup>
                            <col style="width: 60px;">
                            <col style="width: 80px;">
                            <col>
                            <col>
                        </colgroup>
                        <thead>
                            <tr class="bg-primary text-white">
                                <th scope="col">الشهر</th>
                                <th scope="col">الأسبوع</th>
                                <th scope="col">الحصـــة الأولـــــى</th>
                                <th scope="col">الحصـــة الثانيــــة</th>
                            </tr>
                        </thead>
                        <tbody id="yearly-progress-table-viewer">
                            <!-- سيتم ملء البيانات ديناميكيًا هنا -->
                            <tr>
                                <td colspan="4" class="text-center">
                                    <i class="fas fa-spinner fa-spin me-2"></i>جاري تحميل البيانات...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<style>
    /* تنسيقات القائمة الجانبية */
    .card {
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .card-header.bg-primary {
        background: linear-gradient(45deg, #0d6efd, #0a58ca) !important;
    }

    .hover-card {
        transition: all 0.3s ease;
    }

    .hover-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
    }

    .rounded-pill {
        border-radius: 50rem !important;
    }

    .progress-status-badge {
        display: inline-flex;
        align-items: center;
        font-weight: 600;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }

    #current-week-display {
        font-weight: 700;
        transition: all 0.3s ease;
        box-shadow: 0 4px 10px rgba(13, 110, 253, 0.3);
    }

    #current-week-display:hover {
        transform: scale(1.05);
    }

    /* الألوان والتأثيرات */
    .text-success .progress-status-badge {
        color: #198754;
    }

    .text-danger .progress-status-badge {
        color: #dc3545;
    }

    .text-info .progress-status-badge {
        color: #0dcaf0;
    }

    /* تحسين عرض الجدول */
    .table {
        vertical-align: middle;
    }

    .table th {
        font-weight: 600;
        border-bottom-width: 2px;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const progressModal = new bootstrap.Modal(document.getElementById('progressModal'));
        const progressForm = document.getElementById('progressForm');
        const btnAddProgress = document.getElementById('btn-add-progress');
        const levelSelect = document.getElementById('level-select');
        const lessonTitleSelect = document.getElementById('lesson-title-select');
        const progressTable = document.getElementById('progress-table');
        const csrfToken = document.getElementById('csrf-token').value;
        const currentWeekDisplay = document.getElementById('current-week-display');

        // تخزين البيانات الحالية
        let progressItems = [];

        // متغير لتتبع حالة تحميل البيانات
        let isTableLoading = false;

        // سجل سنوي للحصص حسب المستوى
        let yearlyProgressData = {
            'السنة الأولى متوسط': [],
            'السنة الثانية متوسط': [],
            'السنة الثالثة متوسط': [],
            'السنة الرابعة متوسط': []
        };

        // أسماء الشهور وترتيبها
        const monthOrder = [
            'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر',
            'جانفي', 'فيفري', 'مارس', 'أفريل', 'ماي'
        ];

        // المستويات المتاحة
        const levelOrder = [
            'السنة الأولى متوسط',
            'السنة الثانية متوسط',
            'السنة الثالثة متوسط',
            'السنة الرابعة متوسط'
        ];

        // تحويل اسم الشهر بالعربية إلى رقمه الترتيبي
        function getMonthOrder(monthName) {
            const monthsOrder = {
                'سبتمبر': 1,
                'أكتوبر': 2,
                'نوفمبر': 3,
                'ديسمبر': 4,
                'جانفي': 5,
                'فيفري': 6,
                'مارس': 7,
                'أفريل': 8,
                'ماي': 9,
                'جوان': 10
            };

            return monthsOrder[monthName] || 0;
        }

        // تحديث إحصائيات التقدم
        function updateProgressStats() {
            // الحصول على قيمة september_weeks
            const septemberWeeks = parseInt("{{ september_weeks }}", 10) || 2;

            // الحصول على التاريخ الحالي
            const now = new Date();
            const currentMonth = now.getMonth(); // 0-11 (0=January)

            // تحويل شهر النظام إلى الشهر الأكاديمي
            let academicMonth, arabicMonth;

            if (currentMonth >= 8) { // سبتمبر (8) إلى ديسمبر (11)
                academicMonth = currentMonth - 8 + 1; // 1-4
                switch (academicMonth) {
                    case 1: arabicMonth = 'سبتمبر'; break;
                    case 2: arabicMonth = 'أكتوبر'; break;
                    case 3: arabicMonth = 'نوفمبر'; break;
                    case 4: arabicMonth = 'ديسمبر'; break;
                }
            } else { // جانفي (0) إلى جوان (5)
                academicMonth = currentMonth + 5; // 5-10
                switch (academicMonth) {
                    case 5: arabicMonth = 'جانفي'; break;
                    case 6: arabicMonth = 'فيفري'; break;
                    case 7: arabicMonth = 'مارس'; break;
                    case 8: arabicMonth = 'أفريل'; break;
                    case 9: arabicMonth = 'ماي'; break;
                    case 10: arabicMonth = 'جوان'; break;
                }
            }

            // حساب الأسبوع الحالي في الشهر (1-4)
            const dayOfMonth = now.getDate();
            let weekInMonth;
            if (dayOfMonth <= 7) weekInMonth = 1;
            else if (dayOfMonth <= 14) weekInMonth = 2;
            else if (dayOfMonth <= 21) weekInMonth = 3;
            else weekInMonth = 4;

            // حساب الأسبوع الكلي
            let currentTotalWeek = calculateTotalWeek(arabicMonth, weekInMonth);

            // تحديث القيم في الواجهة
            document.getElementById('currentMonth').textContent = arabicMonth;
            document.getElementById('currentWeek').textContent = weekInMonth;
            document.getElementById('currentTotalWeek').textContent = currentTotalWeek;

            console.log('تحديث إحصائيات التقدم:', {
                arabicMonth,
                weekInMonth,
                currentTotalWeek,
                septemberWeeks
            });

            return {
                currentMonth: arabicMonth,
                currentWeek: weekInMonth,
                currentTotalWeek: currentTotalWeek
            };
        }

        // حساب الأسبوع الحالي
        function calculateCurrentWeek() {
            const result = updateProgressStats();
            return result.currentTotalWeek;
        }

        // حساب الأسبوع الكلي لعنصر التقدم
        function calculateTotalWeek(monthName, week) {
            // الحصول على قيمة september_weeks
            const septemberWeeks = parseInt("{{ september_weeks }}", 10) || 2;
            console.log("قيمة أسابيع سبتمبر:", septemberWeeks);

            // تحويل اسم الشهر بالعربية إلى رقم
            const monthOrder = getMonthOrder(monthName);

            // حساب الأسبوع الكلي
            let totalWeek = parseInt(week || 0, 10);

            // إضافة أسابيع الشهور السابقة
            for (let m = 1; m < monthOrder; m++) {
                if (m === 1) { // سبتمبر
                    totalWeek += septemberWeeks;
                } else {
                    // بقية الشهور لها 4 أسابيع افتراضياً
                    totalWeek += 4;
                }
            }

            console.log("حساب الأسبوع الكلي:", {
                monthName,
                week,
                monthOrder,
                septemberWeeks,
                totalWeek
            });

            return totalWeek;
        }

        // تحديث عرض الأسبوع الحالي
        function updateCurrentWeekDisplay() {
            // استدعاء دالة تحديث إحصائيات التقدم
            const result = updateProgressStats();
            const { currentMonth, currentWeek, currentTotalWeek } = result;

            // تحديث عرض الأسبوع الكلي
            if (currentWeekDisplay) {
                currentWeekDisplay.textContent = currentTotalWeek;
            }

            // تحويل رقم الأسبوع إلى نص وصفي
            let weekText;
            switch(currentWeek) {
                case 1: weekText = 'الأول'; break;
                case 2: weekText = 'الثاني'; break;
                case 3: weekText = 'الثالث'; break;
                case 4: weekText = 'الرابع'; break;
                default: weekText = currentWeek;
            }

            // تحديث النص التوضيحي
            const currentMonthWeek = document.getElementById('current-month-week');
            if (currentMonthWeek) {
                currentMonthWeek.innerHTML = `الأسبوع ${weekText} من شهر ${currentMonth}`;
            }

            console.log('تم تحديث عرض الأسبوع الحالي:', {
                currentMonth,
                currentWeek,
                currentTotalWeek,
                weekText,
                septemberWeeks: parseInt("{{ september_weeks }}", 10) || 2
            });
        }

        // تحديث عرض الأسبوع الحالي عند تحميل الصفحة
        updateCurrentWeekDisplay();

        // دالة لتحميل بيانات التدرج السنوي
        function loadYearlyProgressData() {
            // تحميل بيانات التدرج السنوي من الخادم لجميع المستويات
            for (let year = 1; year <= 4; year++) {
                fetch(`/get-yearly-progress/${year}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            // تحديد المستوى المناسب بناءً على السنة
                            let levelName;
                            switch(year) {
                                case 1: levelName = 'السنة الأولى متوسط'; break;
                                case 2: levelName = 'السنة الثانية متوسط'; break;
                                case 3: levelName = 'السنة الثالثة متوسط'; break;
                                case 4: levelName = 'السنة الرابعة متوسط'; break;
                            }

                            // حفظ البيانات في المتغير المحلي
                            yearlyProgressData[levelName] = data.data;
                        }
                    })
                    .catch(error => {
                        console.error('Error loading yearly progress data:', error);
                    });
            }
        }

        // تحميل البيانات السنوية عند تحميل الصفحة
        loadYearlyProgressData();

        // عند تغيير المستوى، قم بتحديث قائمة عناوين الحصص
        levelSelect.addEventListener('change', function() {
            const selectedLevel = levelSelect.value;
            updateLessonTitles(selectedLevel);

            // إزالة التنسيق والرسالة عند الاختيار
            if (selectedLevel) {
                levelSelect.classList.remove('is-invalid');
            }
        });

        // عند تغيير عنوان الحصة، قم بتحديث الحقول المخفية
        lessonTitleSelect.addEventListener('change', function() {
            const selectedLevel = levelSelect.value;
            const selectedOption = lessonTitleSelect.options[lessonTitleSelect.selectedIndex];

            // إزالة التنسيق والرسالة عند الاختيار
            if (lessonTitleSelect.value) {
                lessonTitleSelect.classList.remove('is-invalid');
            }

            if (selectedLevel && selectedOption) {
                const index = selectedOption.dataset.index;
                const lessonType = selectedOption.dataset.type;

                if (index !== undefined && yearlyProgressData[selectedLevel][index]) {
                    const lessonData = yearlyProgressData[selectedLevel][index];

                    // تحديث الحقول المخفية
                    document.getElementById('month').value = lessonData.month;
                    document.getElementById('week').value = lessonData.week;
                    document.getElementById('yearly-progress-id').value = index;
                    document.getElementById('is-merged').value = lessonData.isMerged;
                    document.getElementById('lesson-type').value = lessonType || 'custom';
                }
            }
        });

        // تحديث قائمة عناوين الحصص بناءً على المستوى المختار
        function updateLessonTitles(level) {
            // تفريغ قائمة الخيارات
            lessonTitleSelect.innerHTML = '';

            // إضافة الخيار الافتراضي
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = '-- اختر عنوان الحصة --';
            defaultOption.disabled = true;
            defaultOption.selected = true;
            lessonTitleSelect.appendChild(defaultOption);

            // إذا لم يتم اختيار مستوى، أو لم يتم تحميل البيانات بعد، فلا تُظهر خيارات
            if (!level || !yearlyProgressData[level] || yearlyProgressData[level].length === 0) {
                return;
            }

            // إضافة عناوين الحصص المتاحة
            yearlyProgressData[level].forEach((item, index) => {
                if (item.isMerged) {
                    // إذا كانت الحصتان مدمجتين، أضف خيارًا واحدًا
                    if (item.firstLesson) {
                        const option = document.createElement('option');
                        option.value = item.firstLesson;
                        option.textContent = item.firstLesson;
                        option.dataset.index = index;
                        option.dataset.type = 'merged';
                        lessonTitleSelect.appendChild(option);
                    }
                } else {
                    // إذا كانت الحصتان منفصلتين، أضف خيارين
                    if (item.firstLesson) {
                        const option1 = document.createElement('option');
                        option1.value = item.firstLesson;
                        option1.textContent = item.firstLesson;
                        option1.dataset.index = index;
                        option1.dataset.type = 'first';
                        lessonTitleSelect.appendChild(option1);
                    }

                    if (item.secondLesson) {
                        const option2 = document.createElement('option');
                        option2.value = item.secondLesson;
                        option2.textContent = item.secondLesson;
                        option2.dataset.index = index;
                        option2.dataset.type = 'second';
                        lessonTitleSelect.appendChild(option2);
                    }
                }
            });
        }

        // فتح نافذة إضافة سجل جديد
        btnAddProgress.addEventListener('click', function() {
            // إعادة تعيين النموذج
            progressForm.reset();
            document.getElementById('progress-id').value = '';
            document.getElementById('progressModalLabel').textContent = 'إضافة سجل تقدم جديد';

            // إزالة فئة التحقق والتنسيقات الخطأ
            progressForm.classList.remove('was-validated');
            levelSelect.classList.remove('is-invalid');
            lessonTitleSelect.classList.remove('is-invalid');

            // فتح النافذة المنبثقة
            progressModal.show();
        });

        // حفظ سجل التقدم
        document.getElementById('save-progress').addEventListener('click', function() {
            // إضافة فئة was-validated للنموذج لإظهار رسائل التحقق
            progressForm.classList.add('was-validated');

            // التحقق من صحة البيانات
            if (!levelSelect.value || !lessonTitleSelect.value) {
                // إضافة تنسيق خطأ للحقول الفارغة
                if (!levelSelect.value) {
                    levelSelect.classList.add('is-invalid');
                } else {
                    levelSelect.classList.remove('is-invalid');
                }

                if (!lessonTitleSelect.value) {
                    lessonTitleSelect.classList.add('is-invalid');
                } else {
                    lessonTitleSelect.classList.remove('is-invalid');
                }

                return; // توقف العملية إذا كانت الحقول غير مكتملة
            }

            // تجميع البيانات
            const formData = new FormData(progressForm);

            // إرسال البيانات إلى الخادم
            fetch('/save-progress', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // إغلاق النافذة المنبثقة
                    progressModal.hide();

                    // تحديث الجدول
                    loadProgressTable();

                    // إظهار رسالة نجاح
                    // showToast('تم حفظ سجل التقدم بنجاح', 'success'); // تم تعطيل رسالة النجاح

                    // إعادة تعيين التحقق
                    progressForm.classList.remove('was-validated');
                    levelSelect.classList.remove('is-invalid');
                    lessonTitleSelect.classList.remove('is-invalid');
                } else {
                    // إظهار رسالة خطأ
                    showToast(data.message || 'حدث خطأ أثناء حفظ البيانات', 'danger');
                }
            })
            .catch(error => {
                console.error('Error saving progress:', error);
                showToast('حدث خطأ أثناء الاتصال بالخادم', 'danger');
            });
        });

        // تحميل بيانات الجدول وإعدادات النظام
        function loadProgressTable() {
            // منع تحميل البيانات مرة أخرى إذا كانت عملية التحميل جارية
            if (isTableLoading) return;

            isTableLoading = true;

            // عرض مؤشر التحميل
            const tableBody = progressTable.querySelector('tbody');
            tableBody.innerHTML = '<tr><td colspan="6" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>جاري تحميل البيانات...</td></tr>';

            // عرض مؤشر التحميل في قسم آخر الحصص التعليمية أيضاً
            const lastLessonsLoading = document.getElementById('last-lessons-loading');
            const lastLessonsContent = document.getElementById('last-lessons-content');
            if (lastLessonsLoading && lastLessonsContent) {
                lastLessonsLoading.style.display = 'block';
                lastLessonsContent.style.display = 'none';
            }

            // تحميل بيانات التقدم
            fetch('/get-progress')
                .then(response => response.json())
                .then(data => {
                    isTableLoading = false;
                    if (data.status === 'success') {
                        // تحديث الجدول بالبيانات الجديدة
                        updateProgressTable(data.progress);
                    } else {
                        showToast(data.message || 'حدث خطأ أثناء تحميل البيانات', 'danger');

                        // إخفاء مؤشر التحميل وعرض رسالة في حالة الخطأ
                        const lastLessonsLoading = document.getElementById('last-lessons-loading');
                        const lastLessonsContent = document.getElementById('last-lessons-content');

                        if (lastLessonsLoading && lastLessonsContent) {
                            lastLessonsLoading.style.display = 'none';
                            lastLessonsContent.style.display = 'block';
                            lastLessonsContent.innerHTML = `
                                <div class="small text-center text-muted">
                                    <i class="fas fa-exclamation-circle me-1"></i>
                                    حدث خطأ أثناء تحميل البيانات
                                </div>
                            `;
                        }
                    }
                })
                .catch(error => {
                    isTableLoading = false;
                    console.error('Error loading progress data:', error);
                    showToast('حدث خطأ أثناء الاتصال بالخادم', 'danger');

                    // إخفاء مؤشر التحميل وعرض رسالة في حالة الخطأ
                    const lastLessonsLoading = document.getElementById('last-lessons-loading');
                    const lastLessonsContent = document.getElementById('last-lessons-content');

                    if (lastLessonsLoading && lastLessonsContent) {
                        lastLessonsLoading.style.display = 'none';
                        lastLessonsContent.style.display = 'block';
                        lastLessonsContent.innerHTML = `
                            <div class="small text-center text-muted">
                                <i class="fas fa-exclamation-circle me-1"></i>
                                حدث خطأ أثناء تحميل البيانات
                            </div>
                        `;
                    }
                });
        }

        // تحديث جدول سجلات التقدم
        function updateProgressTable(progressItemsData) {
            // حفظ نسخة من البيانات لاستخدامها لاحقًا
            progressItems = [...progressItemsData];

            const tableBody = progressTable.querySelector('tbody');
            const tableContainer = document.getElementById('progress-table-container');
            const noProgressAlert = document.getElementById('no-progress-alert');

            // إفراغ الجدول
            tableBody.innerHTML = '';

            if (!progressItems || progressItems.length === 0) {
                // عرض التنبيه إذا لم تكن هناك سجلات
                tableContainer.classList.add('d-none');
                noProgressAlert.classList.remove('d-none');

                // تحديث عناصر القائمة الجانبية في حالة عدم وجود بيانات
                const lastLessonsContent = document.getElementById('last-lessons-content');
                const lastLessonsLoading = document.getElementById('last-lessons-loading');

                if (lastLessonsLoading && lastLessonsContent) {
                    // إخفاء مؤشر التحميل
                    lastLessonsLoading.style.display = 'none';
                    lastLessonsContent.style.display = 'block';

                    // عرض رسالة مناسبة
                    lastLessonsContent.innerHTML = `
                        <div class="small text-center text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            لا توجد بيانات متاحة
                        </div>
                    `;
                }

                return;
            }

            // إخفاء التنبيه وإظهار الجدول
            tableContainer.classList.remove('d-none');
            noProgressAlert.classList.add('d-none');

            // نسخ المصفوفة لمنع تعديل البيانات الأصلية
            const itemsCopy = [...progressItems];

            // تنظيم البيانات لعرض أحدث حصة لكل مستوى
            const latestProgressByLevel = {};

            // تحديد أحدث سجل لكل مستوى
            itemsCopy.forEach(item => {
                const level = item.level;
                const createdAt = new Date(item.created_at);

                if (!latestProgressByLevel[level] || new Date(latestProgressByLevel[level].created_at) < createdAt) {
                    latestProgressByLevel[level] = item;
                }
            });

            // إضافة الصفوف الجديدة (حصة واحدة لكل مستوى)
            const levels = Object.keys(latestProgressByLevel);

            // ترتيب المستويات
            levels.sort((a, b) => {
                return levelOrder.indexOf(a) - levelOrder.indexOf(b);
            });

            levels.forEach((level, index) => {
                const item = latestProgressByLevel[level];
                const row = document.createElement('tr');
                row.dataset.id = item.id;

                row.innerHTML = `
                    <td class="text-center">${index + 1}</td>
                    <td>
                        <span class="badge bg-primary me-1">${item.level}</span>
                    </td>
                    <td class="text-center">${item.month}</td>
                    <td class="text-center">
                        <span class="badge rounded-pill bg-secondary">${item.week}</span>
                    </td>
                    <td>${item.lesson_title}</td>
                    <td class="text-center">
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-outline-primary btn-edit-progress" data-id="${item.id}" title="تعديل">
                                <i class="fas fa-edit"></i> <span class="btn-text">تعديل</span>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger btn-delete-progress" data-id="${item.id}" title="حذف">
                                <i class="fas fa-trash-alt"></i> <span class="btn-text">حذف</span>
                            </button>
                        </div>
                    </td>
                `;

                tableBody.appendChild(row);
            });

            // تحديث عرض آخر الحصص في القائمة الجانبية
            updateLastLessonsDisplay(latestProgressByLevel);

            // إعادة تعيين معالجات الأحداث بعد تحديث الجدول
            setupEventHandlers();
        }

        // تحديث عرض آخر الحصص التعليمية في القائمة الجانبية
        function updateLastLessonsDisplay(latestProgressByLevel) {
            console.log("تحديث عرض آخر الحصص التعليمية", latestProgressByLevel);

            const lastLessonsContent = document.getElementById('last-lessons-content');
            const lastLessonsLoading = document.getElementById('last-lessons-loading');

            // التأكد من وجود العناصر
            if (!lastLessonsContent || !lastLessonsLoading) {
                console.error("عناصر القائمة الجانبية غير موجودة");
                return;
            }

            // إظهار المحتوى وإخفاء مؤشر التحميل باستخدام خاصية style مباشرة
            lastLessonsLoading.style.display = 'none';
            lastLessonsContent.style.display = 'block';

            // تفريغ المحتوى
            lastLessonsContent.innerHTML = '';

            // الحصول على الأسبوع الحالي
            const currentWeek = calculateCurrentWeek();
            console.log("الأسبوع الحالي:", currentWeek);

            // ترتيب المستويات
            const levels = Object.keys(latestProgressByLevel).sort((a, b) => {
                return levelOrder.indexOf(a) - levelOrder.indexOf(b);
            });
            console.log("المستويات المتاحة:", levels);

            // إذا لم تكن هناك مستويات، عرض رسالة
            if (levels.length === 0) {
                lastLessonsContent.innerHTML = `
                    <div class="small text-center text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        لا توجد بيانات متاحة
                    </div>
                `;
                return;
            }

            // إنشاء بطاقة لكل مستوى
            levels.forEach(level => {
                const item = latestProgressByLevel[level];
                console.log(`معالجة المستوى: ${level}`, item);

                // التأكد من وجود بيانات صالحة
                if (!item || !item.month || !item.week) {
                    console.warn(`بيانات غير صالحة للمستوى ${level}`, item);
                    return;
                }

                // نحسب الفرق بين الأسبوع الحالي والأسبوع من السجل مباشرة
                const lessonWeek = parseInt(item.week);
                console.log(`أسبوع الحصة: ${lessonWeek} (${item.month} - الأسبوع ${item.week})`);

                // حساب الفرق بين الأسبوع الحالي وأسبوع الحصة
                const weekDifference = currentWeek - lessonWeek;
                console.log(`الفرق بين الأسبوع الحالي وأسبوع الحصة: ${weekDifference}`);

                // تحديد حالة التقدم
                let statusClass = '';

                if (weekDifference > 0) {
                    // متأخر عن البرنامج
                    statusClass = 'danger';
                } else if (weekDifference < 0) {
                    // متقدم عن البرنامج
                    statusClass = 'success';
                } else {
                    // مساير للبرنامج (الفرق = 0)
                    statusClass = 'info';
                }

                // تحديد نص حالة التقدم
                let statusText = '';
                const absDifference = Math.abs(weekDifference);

                if (weekDifference > 0) {
                    // متأخر عن البرنامج
                    statusText = `متأخر بـ ${absDifference} أسبوع`;
                } else if (weekDifference < 0) {
                    // متقدم عن البرنامج
                    statusText = `متقدم بـ ${absDifference} أسبوع`;
                } else {
                    // مساير للبرنامج (الفرق = 0)
                    statusText = 'مساير للبرنامج';
                }

                // إنشاء العنصر
                const levelCard = document.createElement('div');
                levelCard.className = `border-start border-${statusClass} border-4 mb-3 shadow-sm bg-white rounded-3 hover-card`;

                levelCard.innerHTML = `
                    <div class="p-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="badge bg-primary rounded-pill px-3 py-2 fs-6">${level}</span>
                            <div class="ms-2">
                                <span class="badge bg-primary rounded-pill fs-5 px-3 py-2 shadow-sm">${item.week}</span>
                            </div>
                        </div>
                        <div class="text-${statusClass} small mt-2 text-center d-flex align-items-center justify-content-center">
                            <div class="progress-status-badge bg-${statusClass} bg-opacity-10 px-3 py-1 rounded-pill">
                                <i class="fas fa-${weekDifference > 0 ? 'arrow-down' : weekDifference < 0 ? 'arrow-up' : 'check'} me-1"></i>
                                ${statusText}
                            </div>
                        </div>
                    </div>
                `;

                lastLessonsContent.appendChild(levelCard);
                console.log(`تمت إضافة بطاقة للمستوى: ${level}`);
            });
        }

        // إعداد معالجات الأحداث للأزرار في الجدول
        function setupEventHandlers() {
            // معالجات أحداث أزرار التعديل
            document.querySelectorAll('.btn-edit-progress').forEach(button => {
                button.addEventListener('click', function() {
                    const progressId = this.dataset.id;
                    editProgress(progressId);
                });
            });

            // معالجات أحداث أزرار الحذف
            document.querySelectorAll('.btn-delete-progress').forEach(button => {
                button.addEventListener('click', function() {
                    const progressId = this.dataset.id;
                    deleteProgress(progressId);
                });
            });
        }

        // تعديل سجل تقدم
        function editProgress(progressId) {
            // إزالة فئة التحقق والتنسيقات الخطأ
            progressForm.classList.remove('was-validated');
            levelSelect.classList.remove('is-invalid');
            lessonTitleSelect.classList.remove('is-invalid');

            // تحميل بيانات السجل من الخادم
            fetch(`/get-progress-item/${progressId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // تعبئة النموذج بالبيانات
                        document.getElementById('progress-id').value = data.progress.id;
                        document.getElementById('progressModalLabel').textContent = 'تعديل سجل التقدم';
                        document.getElementById('month').value = data.progress.month;
                        document.getElementById('week').value = data.progress.week;
                        document.getElementById('yearly-progress-id').value = data.progress.yearly_progress_id || 0;
                        document.getElementById('is-merged').value = data.progress.is_merged || false;
                        document.getElementById('lesson-type').value = data.progress.lesson_type || 'custom';

                        // تعيين المستوى
                        levelSelect.value = data.progress.level;

                        // تحديث قائمة عناوين الحصص
                        updateLessonTitles(data.progress.level);

                        // انتظار قليلاً لضمان تحديث القائمة
                        setTimeout(() => {
                            // تعيين عنوان الحصة
                            // البحث عن الخيار الذي يطابق عنوان الحصة
                            const options = Array.from(lessonTitleSelect.options);
                            const matchingOption = options.find(option => option.value === data.progress.lesson_title);

                            // إذا تم العثور على خيار مطابق، حدده
                            if (matchingOption) {
                                lessonTitleSelect.value = matchingOption.value;
                            } else {
                                // إذا لم يتم العثور على خيار مطابق، أضف خيارًا جديدًا
                                const newOption = document.createElement('option');
                                newOption.value = data.progress.lesson_title;
                                newOption.textContent = data.progress.lesson_title;
                                lessonTitleSelect.appendChild(newOption);
                                lessonTitleSelect.value = data.progress.lesson_title;
                            }
                        }, 300);

                        // فتح النافذة المنبثقة
                        progressModal.show();
                    } else {
                        showToast(data.message || 'حدث خطأ أثناء تحميل بيانات السجل', 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error loading progress details:', error);
                    showToast('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                });
        }

        // حذف سجل تقدم
        function deleteProgress(progressId) {
            // إزالة استخدام confirm واستمرار مباشرة بالحذف
            fetch(`/delete-progress/${progressId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // تحديث الجدول
                    loadProgressTable();

                    // إظهار رسالة نجاح
                    // showToast('تم حذف السجل بنجاح', 'success'); // تم تعطيل رسالة النجاح
                } else {
                    showToast(data.message || 'حدث خطأ أثناء حذف السجل', 'danger');
                }
            })
            .catch(error => {
                console.error('Error deleting progress:', error);
                showToast('حدث خطأ أثناء الاتصال بالخادم', 'danger');
            });
        }

        // عرض رسالة توست
        function showToast(message, type) {
            // إنشاء عنصر التنبيه
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.style.position = 'fixed';
            alertDiv.style.top = '20px';
            alertDiv.style.right = '20px';
            alertDiv.style.zIndex = '1050';
            alertDiv.style.padding = '12px 20px';
            alertDiv.style.borderRadius = '6px';
            alertDiv.style.boxShadow = '0 3px 10px rgba(0,0,0,0.15)';
            alertDiv.style.maxWidth = '300px';
            alertDiv.style.textAlign = 'center';
            alertDiv.style.opacity = '0';
            alertDiv.style.transition = 'opacity 0.3s ease-in-out';

            // إضافة أيقونة مناسبة للرسالة
            let icon = 'check-circle';
            if (type === 'danger') icon = 'exclamation-circle';
            else if (type === 'info') icon = 'info-circle';

            alertDiv.innerHTML = `
                <i class="fas fa-${icon} me-2"></i>
                ${message}
            `;

            // إضافة العنصر إلى الصفحة
            document.body.appendChild(alertDiv);

            // عرض العنصر (بعد إضافته للصفحة لتفعيل الانتقال)
            setTimeout(() => {
                alertDiv.style.opacity = '1';
            }, 10);

            // إخفاء العنصر بعد فترة زمنية
            setTimeout(() => {
                alertDiv.style.opacity = '0';
                setTimeout(() => {
                    alertDiv.remove();
                }, 300);
            }, 3000);
        }

        // تحميل جدول التقدم عند تحميل الصفحة
        // تأخير قليل للتأكد من تهيئة جميع العناصر
        setTimeout(() => {
            // إخفاء مؤشر التحميل لعناصر القائمة الجانبية
            const lastLessonsLoading = document.getElementById('last-lessons-loading');
            const lastLessonsContent = document.getElementById('last-lessons-content');

            if (lastLessonsContent && lastLessonsLoading) {
                console.log("تهيئة عناصر القائمة الجانبية...");
            }

            // تحميل إعدادات عدد أسابيع سبتمبر أولاً
            fetch('/get-settings')
                .then(response => response.json())
                .then(settingsData => {
                    if (settingsData.status === 'success' && settingsData.settings) {
                        // الحصول على إعداد عدد أسابيع سبتمبر من الإعدادات
                        const septemberWeeks = settingsData.settings.september_weeks || 2;
                        // حفظ القيمة في localStorage للاستخدام اللاحق
                        localStorage.setItem('september_weeks', septemberWeeks);
                        console.log(`تم تحميل إعداد عدد أسابيع سبتمبر: ${septemberWeeks}`);

                        // تحديث عرض الأسبوع الحالي فوراً بعد تحميل الإعدادات
                        updateCurrentWeekDisplay();
                    }

                    // تحميل بيانات جدول التقدم
                    loadProgressTable();
                })
                .catch(error => {
                    console.error('خطأ في تحميل الإعدادات:', error);
                    // استخدام القيمة الافتراضية في حال وجود خطأ
                    loadProgressTable();
                });
        }, 200); // زيادة التأخير إلى 200 مللي ثانية

        // إعداد معالجات الأحداث الأولية
        setupEventHandlers();

        // إضافة معالج للنقر على زر التدرج السنوي
        const yearlyProgressBtn = document.getElementById('yearly-progress-btn');
        const yearlyProgressModal = new bootstrap.Modal(document.getElementById('yearlyProgressModal'));
        const yearSelectorProgress = document.getElementById('year-selector-progress');

        // إضافة المستمع عند النقر على زر التدرج السنوي
        yearlyProgressBtn.addEventListener('click', function() {
            // تحميل التدرج السنوي للسنة المختارة
            const selectedYear = parseInt(yearSelectorProgress.value);
            loadYearlyProgressDataForViewer(selectedYear);
            yearlyProgressModal.show();
        });

        // مستمع لتغيير السنة في القائمة المنسدلة
        yearSelectorProgress.addEventListener('change', function() {
            const selectedYear = parseInt(yearSelectorProgress.value);
            loadYearlyProgressDataForViewer(selectedYear);
        });

        // وظيفة لتحميل بيانات التدرج السنوي للعرض فقط
        function loadYearlyProgressDataForViewer(year) {
            // تحديث واجهة المستخدم لإظهار أنه يتم تحميل البيانات
            const table = document.getElementById('yearly-progress-table-viewer');
            table.innerHTML = '<tr><td colspan="4" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>جاري تحميل البيانات...</td></tr>';

            // الحصول على البيانات من الخادم
            fetch(`/get-yearly-progress/${year}`)
                .then(response => response.json())
                .then(response => {
                    if (response.status === 'success') {
                        const progressItems = response.data;

                        // تفريغ الجدول
                        table.innerHTML = '';

                        // ترتيب الأشهر
                        const monthOrder = [
                            'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر',
                            'جانفي', 'فيفري', 'مارس', 'أفريل', 'ماي'
                        ];

                        // التحقق من وجود بيانات
                        if (progressItems && progressItems.length > 0) {
                            // ترتيب البيانات حسب الشهر ثم رقم الأسبوع
                            progressItems.sort((a, b) => {
                                const monthIndexA = monthOrder.indexOf(a.month);
                                const monthIndexB = monthOrder.indexOf(b.month);

                                if (monthIndexA !== monthIndexB) {
                                    return monthIndexA - monthIndexB;
                                }

                                return a.week - b.week;
                            });

                            // تنظيم البيانات حسب الشهر
                            const monthsData = {};
                            monthOrder.forEach(month => {
                                monthsData[month] = progressItems.filter(item => item.month === month);
                            });

                            // عرض البيانات حسب الشهر
                            monthOrder.forEach(month => {
                                const monthItems = monthsData[month];
                                if (monthItems && monthItems.length > 0) {
                                    // تحديد لون خلفية الشهر
                                    let bgColor = 'rgba(255, 193, 7, 0.3)';
                                    let rowColor = 'rgba(255, 193, 7, 0.1)';

                                    // تحديد اللون حسب الشهر
                                    switch (month) {
                                        case 'سبتمبر':
                                            bgColor = 'rgba(255, 193, 7, 0.3)';
                                            rowColor = 'rgba(255, 193, 7, 0.1)';
                                            break;
                                        case 'أكتوبر':
                                            bgColor = 'rgba(40, 167, 69, 0.3)';
                                            rowColor = 'rgba(40, 167, 69, 0.1)';
                                            break;
                                        case 'نوفمبر':
                                            bgColor = 'rgba(23, 162, 184, 0.3)';
                                            rowColor = 'rgba(23, 162, 184, 0.1)';
                                            break;
                                        case 'ديسمبر':
                                            bgColor = 'rgba(220, 53, 69, 0.3)';
                                            rowColor = 'rgba(220, 53, 69, 0.1)';
                                            break;
                                        case 'جانفي':
                                            bgColor = 'rgba(111, 66, 193, 0.3)';
                                            rowColor = 'rgba(111, 66, 193, 0.1)';
                                            break;
                                        case 'فيفري':
                                            bgColor = 'rgba(32, 201, 151, 0.3)';
                                            rowColor = 'rgba(32, 201, 151, 0.1)';
                                            break;
                                        case 'مارس':
                                            bgColor = 'rgba(253, 126, 20, 0.3)';
                                            rowColor = 'rgba(253, 126, 20, 0.1)';
                                            break;
                                        case 'أفريل':
                                            bgColor = 'rgba(13, 202, 240, 0.3)';
                                            rowColor = 'rgba(13, 202, 240, 0.1)';
                                            break;
                                        case 'ماي':
                                            bgColor = 'rgba(102, 16, 242, 0.3)';
                                            rowColor = 'rgba(102, 16, 242, 0.1)';
                                            break;
                                    }

                                    // إنشاء صفوف الشهر
                                    monthItems.forEach((item, index) => {
                                        const row = document.createElement('tr');
                                        row.style.backgroundColor = rowColor;

                                        // إنشاء خلايا الصف
                                        if (index === 0) {
                                            // خلية الشهر (تمتد على طول جميع الصفوف في الشهر) - فقط للصف الأول من كل شهر
                                            const monthCell = document.createElement('td');
                                            monthCell.rowSpan = monthItems.length;
                                            monthCell.className = 'align-middle fw-bold month-cell';
                                            monthCell.style.cssText = `writing-mode: vertical-lr; transform: rotate(180deg); background-color: ${bgColor}; width: 60px;`;
                                            monthCell.textContent = month;
                                            row.appendChild(monthCell);
                                        }

                                        // خلية رقم الأسبوع
                                        const weekCell = document.createElement('td');
                                        weekCell.textContent = item.week;
                                        row.appendChild(weekCell);

                                        if (item.isMerged) {
                                            // إذا كانت الخلايا مدمجة، سنعرض الحصة الأولى فقط ممتدة على عمودين
                                            const firstLessonCell = document.createElement('td');
                                            firstLessonCell.colSpan = 2;
                                            firstLessonCell.textContent = item.firstLesson;
                                            row.appendChild(firstLessonCell);
                                        } else {
                                            // عرض الحصتين بشكل منفصل
                                            const firstLessonCell = document.createElement('td');
                                            firstLessonCell.textContent = item.firstLesson;
                                            row.appendChild(firstLessonCell);

                                            const secondLessonCell = document.createElement('td');
                                            secondLessonCell.textContent = item.secondLesson;
                                            row.appendChild(secondLessonCell);
                                        }

                                        // إضافة الصف إلى الجدول
                                        table.appendChild(row);
                                    });
                                }
                            });

                            // إذا لم يكن هناك بيانات بعد الترتيب، نعرض رسالة
                            if (table.children.length === 0) {
                                table.innerHTML = '<tr><td colspan="4" class="text-center">لا توجد بيانات للتدرج السنوي لهذه السنة الدراسية</td></tr>';
                            }
                        } else {
                            // إذا لم تكن هناك بيانات، نعرض رسالة
                            table.innerHTML = '<tr><td colspan="4" class="text-center">لا توجد بيانات للتدرج السنوي لهذه السنة الدراسية</td></tr>';
                        }
                    } else {
                        // إذا كان هناك خطأ، نظهر رسالة الخطأ
                        table.innerHTML = `<tr><td colspan="4" class="text-center text-danger"><i class="fas fa-exclamation-triangle me-2"></i>${response.message || 'حدث خطأ أثناء تحميل البيانات'}</td></tr>`;
                    }
                })
                .catch(error => {
                    console.error('Error loading yearly progress:', error);
                    table.innerHTML = '<tr><td colspan="4" class="text-center text-danger"><i class="fas fa-exclamation-triangle me-2"></i>حدث خطأ أثناء الاتصال بالخادم</td></tr>';
                });
        }
    });
</script>
{% endblock %}