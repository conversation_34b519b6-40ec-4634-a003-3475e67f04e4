{% if messages %}
<form id="{{ 'received-messages-form' if is_received else 'sent-messages-form' }}">
    <div class="messages-container">
        {% for message in messages %}
        <div class="message-item mb-3 p-3 border rounded{% if not message.is_read and is_received %} bg-light{% endif %}" data-message-id="{{ message.id }}">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    {% if is_received %}
                    <a href="{{ url_for('profile', user_id=message.sender_id) }}" class="teacher-name-link">
                        <strong>من: {{ message.sender.teacher_name }}</strong>
                    </a>
                    {% else %}
                    <a href="{{ url_for('profile', user_id=message.recipient_id) }}" class="teacher-name-link">
                        <strong>إلى: {{ message.recipient.teacher_name }}</strong>
                    </a>
                    {% endif %}
                    <small class="text-muted d-block">{{ message.timestamp.strftime('%Y-%m-%d %I:%M %p') }}</small>
                </div>
                <div class="d-flex align-items-center">
                    {% if is_received and not message.is_read %}
                    <span class="badge bg-primary me-2">جديد</span>
                    {% endif %}
                    <button type="button" class="btn btn-link text-danger p-0" onclick="deleteMessage({{ message.id }})" title="حذف الرسالة">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </div>
            </div>
            <div class="message-content mt-3">
                <p class="mb-2 message-text">{{ message.content }}</p>
                {% if message.attachment and message.attachment_name %}
                <div class="attachment-section">
                    <div class="d-flex align-items-center p-2 bg-light rounded border">
                        <i class="fas fa-paperclip text-secondary me-1"></i>
                        <div class="d-flex flex-column flex-grow-1">
                            <a href="{{ url_for('download_attachment', message_id=message.id) }}" class="text-primary text-decoration-none">
                                <span class="attachment-name fw-bold">{{ message.attachment_name }}</span>
                            </a>
                            <small class="text-muted">
                                {% set ext = message.attachment_name.split('.')[-1].lower() %}
                                {% if ext in ['jpg', 'jpeg', 'png', 'gif', 'bmp'] %}
                                <i class="fas fa-image text-info ms-1"></i> صورة
                                {% elif ext in ['pdf'] %}
                                <i class="fas fa-file-pdf text-danger ms-1"></i> ملف PDF
                                {% elif ext in ['doc', 'docx'] %}
                                <i class="fas fa-file-word text-primary ms-1"></i> ملف Word
                                {% elif ext in ['xls', 'xlsx'] %}
                                <i class="fas fa-file-excel text-success ms-1"></i> ملف Excel
                                {% elif ext in ['ppt', 'pptx'] %}
                                <i class="fas fa-file-powerpoint text-warning ms-1"></i> ملف PowerPoint
                                {% elif ext in ['zip', 'rar'] %}
                                <i class="fas fa-file-archive text-secondary ms-1"></i> ملف مضغوط
                                {% elif ext in ['txt'] %}
                                <i class="fas fa-file-alt text-secondary ms-1"></i> ملف نصي
                                {% else %}
                                <i class="fas fa-file text-secondary ms-1"></i> ملف
                                {% endif %}
                            </small>
                        </div>
                        <a href="{{ url_for('download_attachment', message_id=message.id) }}" class="btn btn-sm btn-outline-primary ms-2" title="تحميل الملف">
                            <i class="fas fa-download"></i>
                        </a>
                    </div>
                </div>
                {% endif %}
            </div>
            {% if not message.is_read and is_received %}
            <div class="text-end mt-2">
                <button type="button" class="btn btn-sm btn-success mark-as-read" onclick="markAsRead({{ message.id }})">
                    <i class="fas fa-check me-1"></i>
                    تم القراءة
                </button>
            </div>
            {% endif %}
        </div>
        {% endfor %}
    </div>
</form>
{% else %}
<p class="text-center text-muted">لا توجد رسائل</p>
{% endif %}

<style>
.teacher-name-link {
    color: #1e88e5;
    text-decoration: none;
    transition: all 0.3s ease;
}

.teacher-name-link:hover {
    color: #1565c0;
    text-decoration: none;
    transform: translateX(-3px);
}
</style>

<script>
// تحويل الروابط والبريد الإلكتروني في نص الرسائل إلى روابط قابلة للنقر
function convertLinksInMessages() {
    // الحصول على جميع فقرات محتوى الرسائل
    const messageParagraphs = document.querySelectorAll('.message-text');

    // تعبير منتظم للبحث عن الروابط
    const urlRegex = /(https?:\/\/[^\s]+)|(www\.[^\s]+)|([a-zA-Z0-9][-a-zA-Z0-9]*\.[a-zA-Z0-9][-a-zA-Z0-9]*\.[a-zA-Z]{2,}[^\s]*)|([a-zA-Z0-9][-a-zA-Z0-9]*\.[a-zA-Z]{2,}[^\s]*)/g;

    // تعبير منتظم للبحث عن البريد الإلكتروني
    const emailRegex = /([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9._-]+)/g;

    // معالجة كل فقرة
    messageParagraphs.forEach(paragraph => {
        let html = paragraph.innerHTML;

        // تجنب تحويل الروابط التي تم تحويلها بالفعل
        if (html.indexOf('<a href') !== -1) return;

        // تحويل الروابط
        html = html.replace(urlRegex, function(url) {
            let href = url;
            if (url.startsWith('www.')) {
                href = 'http://' + url;
            } else if (!url.startsWith('http://') && !url.startsWith('https://') && url.includes('.')) {
                // إذا كان الرابط لا يبدأ بـ http:// أو https:// ويحتوي على نقطة (مثل google.com)
                href = 'http://' + url;
            }
            return `<a href="${href}" target="_blank" class="text-primary" style="text-decoration: underline; word-break: break-all; display: inline-block;">${url}</a>`;
        });

        // تحويل البريد الإلكتروني
        html = html.replace(emailRegex, function(email) {
            return `<a href="mailto:${email}" class="text-primary" style="text-decoration: underline; word-break: break-all; display: inline-block;">${email}</a>`;
        });

        // تحديث محتوى الفقرة
        paragraph.innerHTML = html;
    });
}

// تنفيذ الدالة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    convertLinksInMessages();
});

function markAsRead(messageId) {
    fetch(`/mark_as_read/${messageId}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // تحديث واجهة المستخدم
            const messageElem = document.querySelector(`.message-item[data-message-id="${messageId}"]`);
            if (messageElem) {
                // إزالة خلفية الرسالة غير المقروءة
                messageElem.classList.remove('bg-light');

                // إزالة شارة "جديد"
                const badge = messageElem.querySelector('.badge.bg-primary');
                if (badge) badge.remove();

                // إزالة زر "تم القراءة"
                const markAsReadBtn = messageElem.querySelector('.mark-as-read');
                if (markAsReadBtn) markAsReadBtn.parentNode.remove();
            }

            // تحديث عدد الرسائل غير المقروءة
            updateReceivedMessages();
        } else {
            showMessage(data.message || 'حدث خطأ أثناء تحديد الرسالة كمقروءة', 'danger');
        }
    })
    .catch(error => {
        console.error('Error marking message as read:', error);
        showMessage('حدث خطأ أثناء الاتصال بالخادم', 'danger');
    });
}
</script>
