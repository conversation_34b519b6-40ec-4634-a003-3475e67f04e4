/* خلفية متحركة احترافية لصفحات الدخول والتسجيل - محسنة للأداء */
body.auth-page {
    background-color: #0c4a8a;
    background-image:
        linear-gradient(rgba(25, 118, 210, 0.7), rgba(25, 118, 210, 0.7)),
        linear-gradient(90deg, rgba(255,255,255,.07) 1px, transparent 1px),
        linear-gradient(0deg, rgba(255,255,255,.07) 1px, transparent 1px);
    background-size: 100% 100%, 20px 20px, 20px 20px;
    background-position: 0 0, 10px 10px, 10px 10px;
    position: relative;
    overflow: hidden;
    will-change: transform; /* تحسين أداء الرسوم المتحركة */
}

/* إضافة تأثير الجسيمات المتحركة - محسن للأداء */
body.auth-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 30px 30px;
    background-position: 0 0;
    animation: moveBackground 60s linear infinite;
    pointer-events: none;
    will-change: background-position; /* تحسين أداء الرسوم المتحركة */
}

/* إضافة تأثير الدوائر المتحركة - محسن للأداء */
body.auth-page::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 10% 20%, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0) 200px),
        radial-gradient(circle at 90% 80%, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0) 200px);
    pointer-events: none;
    will-change: opacity, transform; /* تحسين أداء الرسوم المتحركة */
}

/* تحريك الخلفية - محسن للأداء */
@keyframes moveBackground {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 500px 500px;
    }
}

/* تحسين مظهر الإطار - محسن للأداء */
.login-card {
    /* إزالة backdrop-filter لتحسين الأداء */
    background-color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 10px 25px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    /* تقليل تأثير الرسوم المتحركة للإطار */
    transform: translateY(0);
    transition: transform 0.3s ease;
}

/* تحسين أداء الإطار عند التحويم */
.login-card:hover {
    transform: translateY(-5px);
}

/* تحسين مظهر الأزرار */
.btn-primary {
    background: linear-gradient(135deg, #1976d2, #1565c0);
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1565c0, #0d47a1);
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
