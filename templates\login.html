{% extends "base.html" %}
{% block head %}
<link href="{{ url_for('static', filename='background.css') }}" rel="stylesheet">
<!-- إضافة ملف CSS للهواتف المحمولة -->
<link href="{{ url_for('static', filename='css/auth-mobile.css') }}" rel="stylesheet">
<script>
    // تحسين سرعة تحميل الصفحة بإضافة الفئة مباشرة
    document.addEventListener('DOMContentLoaded', function() {
        document.body.classList.add('auth-page');
    });
</script>
{% endblock %}
{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h3 class="text-center text-white mb-0">الجمهورية الجزائرية الديمقراطية الشعبية</h3>
            <h4 class="text-center text-white mb-0">وزارة التربية الوطنية</h4>
        </div>

        <div class="login-body">
            <div class="welcome-text-container">
                <div class="welcome-text">
                    <span class="animated-word">مرحبا</span>
                    <span class="animated-word">بك</span>
                    <span class="animated-word">في</span>
                    <span class="animated-word">فضاء</span>
                    <span class="animated-word">أساتذة</span>
                    <span class="animated-word">العلوم</span>
                    <span class="animated-word">الفيزيائية</span>
                </div>
            </div>

                    <form method="POST" action="{{ url_for('login') }}" id="login-form">
                        <!-- إضافة رمز CSRF للحماية من هجمات التزوير -->
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <div class="mb-3">
                    <input type="email" class="form-control text-right" id="email" name="email"
                           placeholder="أدخل بريدك الإلكتروني" required
                           oninvalid="this.setCustomValidity('يرجى إدخال بريد إلكتروني صحيح')"
                           oninput="this.setCustomValidity('')">
                        </div>
                        <div class="mb-3">
                    <div class="password-field-container">
                        <input type="password" class="form-control text-right password-input" id="password" name="password"
                               placeholder="كلمة المرور" required
                               oninvalid="this.setCustomValidity('يرجى إدخال كلمة المرور')"
                               oninput="this.setCustomValidity('')">
                        <button type="button" id="toggle-password" class="toggle-password" aria-label="عرض/إخفاء كلمة المرور">
                            <i class="password-icon fa fa-eye-slash"></i>
                        </button>
                    </div>
                    <div class="password-links">
                        <a href="#" class="forgot-password mt-1" id="forgot-password-link">نسيت كلمة المرور؟</a>
                    </div>
                </div>

                <div class="captcha-container">
                    <div class="captcha-box">
                        <div class="captcha-code">
                            <span id="captcha-text">{{ captcha }}</span>
                        </div>
                        </div>
                    <div class="captcha-input-container">
                        <input type="text" class="form-control text-center captcha-input" id="captcha" name="captcha"
                               placeholder="أدخل أرقام رمز التحقق أعلاه" required
                               oninvalid="this.setCustomValidity('يرجى إدخال رمز التحقق المكون من 4 أرقام')"
                               oninput="this.setCustomValidity('')"
                               pattern="[0-9]{4}" maxlength="4">
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center mt-2">
                    <a href="{{ url_for('register') }}" class="register-link">ليس لديك حساب؟ <span class="register-here">سجل هنا</span></a>
                    <button type="submit" class="btn btn-login">دخول</button>
            </div>
            </form>
        </div>
    </div>
</div>

<style>
/* إخفاء شريط التنقل */
.navbar {
    display: none !important;
}

body {
    background-color: #f5f5f5;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 15px;
}

.login-card {
    width: 100%;
    max-width: 400px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    background-color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.5);
    opacity: 0;
    transform: translateY(-20px);
    animation: cardDropIn 0.5s ease-out forwards;
    will-change: opacity, transform; /* تحسين أداء الرسوم المتحركة */
}

/* تعديلات للشاشات الصغيرة */
@media (max-width: 576px) {
    .login-container {
        padding: 10px;
    }

    .login-card {
        max-width: 100%;
    }

    .login-body {
        padding: 15px 20px;
    }

    .welcome-text {
        font-size: 20px;
    }

    .animated-word {
        margin: 0 2px;
    }

    .login-header h3 {
        font-size: 18px;
    }

    .login-header h4 {
        font-size: 16px;
    }
}

/* تبسيط الرسوم المتحركة لتحسين الأداء */
@keyframes cardDropIn {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    background: #1e88e5;
    padding: 15px 20px;
    text-align: center;
}

.login-body {
    padding: 15px 25px;
}

.welcome-text-container {
    text-align: center;
    margin-bottom: 5px;
}

.form-control {
    border: 1px solid #ddd;
    padding: 10px 15px;
    margin-bottom: 10px;
    border-radius: 5px;
    text-align: right;
}

.form-control:focus {
    border-color: #1976d2;
    box-shadow: 0 0 0 0.2rem rgba(25, 118, 210, 0.25);
}

.btn-login {
    background-color: #1e88e5;
    color: white;
    border: none;
    padding: 8px 30px;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.3s;
}

.btn-login:hover {
    background-color: #1565c0;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.captcha-container {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    margin-top: 10px;
    margin-bottom: 8px;
    text-align: center;
}

.captcha-box {
    display: inline-block;
    background-color: white;
    padding: 5px 20px;
    border-radius: 5px;
    margin-bottom: 2px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

.captcha-code {
    font-size: 24px;
    font-weight: bold;
    color: #1976d2;
    letter-spacing: 2px;
}

.register-link {
    color: #000000;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.register-link:hover {
    color: #27ae60;
}

.register-here {
    color: #27ae60;
}

.forgot-password {
    color: #1976d2;
    text-decoration: none;
    font-size: 14px;
    float: right;
    display: block;
    margin-top: 2px;
    margin-bottom: 0;
}

.password-links {
    display: flex;
    justify-content: flex-end;
    width: 100%;
}

.text-right {
    text-align: right;
}

/* Ajustes adicionales para coincidir mejor con la imagen de referencia */
.login-header {
    background: #1e88e5; /* Color azul sólido como en la imagen */
    padding: 15px 20px;
}

.login-header h3 {
    font-size: 18px;
    margin-bottom: 5px;
    white-space: nowrap;
}

.login-header h4 {
    font-size: 16px;
    padding-top: 2px;
}

.btn-login {
    background-color: #1e88e5;
    padding: 8px 30px;
    font-size: 16px;
}

.form-control::placeholder {
    opacity: 0.7;
}

/* Redondeo de bordes para que coincida mejor con la imagen */
.login-card {
    border-radius: 12px;
}

input.form-control {
    padding: 10px 15px;
}

/* Modificación de la bienvenida */
.welcome-text {
    font-size: 22px;
    font-weight: 800;
    margin-bottom: 5px;
    border-bottom: none;
    padding-bottom: 0;
    letter-spacing: 0.5px;
    font-family: Arial, sans-serif;
    display: flex;
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
}

.animated-word {
    display: inline-block;
    color: transparent;
    background: linear-gradient(to right, #1565c0, #0d47a1, #1976d2);
    -webkit-background-clip: text;
    background-clip: text;
    text-shadow: 0 0 3px rgba(13, 71, 161, 0.4);
    opacity: 0;
    white-space: nowrap;
    margin: 0 3px;
    position: relative;
    will-change: opacity, transform; /* تحسين أداء الرسوم المتحركة */
}

.animated-word:nth-child(1) {
    animation: wordAnimation 3s ease-in-out 0s infinite;
}
.animated-word:nth-child(2) {
    animation: wordAnimation 3s ease-in-out 0.1s infinite;
}
.animated-word:nth-child(3) {
    animation: wordAnimation 3s ease-in-out 0.2s infinite;
}
.animated-word:nth-child(4) {
    animation: wordAnimation 3s ease-in-out 0.3s infinite;
}
.animated-word:nth-child(5) {
    animation: wordAnimation 3s ease-in-out 0.4s infinite;
}
.animated-word:nth-child(6) {
    animation: wordAnimation 3s ease-in-out 0.5s infinite;
}
.animated-word:nth-child(7) {
    animation: wordAnimation 3s ease-in-out 0.6s infinite;
}

/* تبسيط الرسوم المتحركة للكلمات لتحسين الأداء */
@keyframes wordAnimation {
    0%, 100% {
        opacity: 0;
        transform: translateY(5px);
        filter: blur(3px);
    }
    15%, 50% {
        opacity: 1;
        transform: translateY(0);
        filter: blur(0);
    }
    65% {
        opacity: 0;
        transform: translateY(-5px);
        filter: blur(3px);
    }
}

.password-field-container {
    position: relative;
    display: flex;
    align-items: center;
}

.toggle-password {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    color: #777;
    font-size: 16px;
    transition: color 0.3s;
    z-index: 5;
}

.toggle-password:hover {
    color: #1976d2;
}

.toggle-password:focus {
    outline: none;
}

.password-input {
    margin-bottom: 0;
    padding-left: 40px; /* لإفساح المجال لرمز العين */
}

.captcha-input-container {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-top: 2px;
}

.captcha-input {
    max-width: 70%;
    text-align: center;
    font-size: 20px; /* تكبير حجم الخط */
    font-weight: 600; /* جعل الخط أكثر سمكًا */
    letter-spacing: 3px; /* زيادة المسافة بين الأرقام */
}

/* إضافة نمط خاص للنص الإرشادي في حقل رمز التحقق */
.captcha-input::placeholder {
    font-size: 14px; /* حجم خط مماثل لحقل كلمة المرور */
    font-weight: normal;
    letter-spacing: normal;
    opacity: 0.7;
}

/* تنسيقات إطار استعادة كلمة المرور */
#resetPasswordModal .modal-content {
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border: none;
    overflow: hidden;
}

#resetPasswordModal .modal-header {
    background-color: #1e88e5;
    color: white;
    border-bottom: none;
    padding: 15px 20px;
    direction: rtl;
}

#resetPasswordModal .modal-title {
    font-size: 18px;
    font-weight: 600;
    margin-right: 20px;
}

#resetPasswordModal .btn-close {
    margin: -0.5rem auto -0.5rem -0.5rem;
}

#resetPasswordModal .modal-body {
    padding: 15px;
    position: relative;
}

/* تحسين تنسيقات التنبيه */
#reset-instructions {
    background-color: rgba(25, 118, 210, 0.1);
    border-color: rgba(25, 118, 210, 0.2);
    color: #0d47a1;
    font-size: 14px;
    padding: 12px;
    border-radius: 8px;
    text-align: right;
    position: relative;
    z-index: 10;
    display: block !important;
    opacity: 1 !important;
    margin-bottom: 15px;
}

#reset-instructions i {
    margin-left: 5px;
}

#resetPasswordModal .form-label {
    text-align: right;
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
    color: #555;
}

#resetPasswordModal .modal-footer {
    border-top: none;
    padding: 10px 15px 15px;
    justify-content: space-between;
}

#resetPasswordModal .btn-primary {
    background-color: #1e88e5;
    border: none;
    padding: 8px 22px;
    border-radius: 5px;
}

#resetPasswordModal .btn-primary:hover {
    background-color: #1565c0;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

#resetPasswordModal .btn-secondary {
    background-color: #f5f5f5;
    color: #333;
    border: none;
    padding: 8px 22px;
    border-radius: 5px;
}

#resetPasswordModal .btn-secondary:hover {
    background-color: #e0e0e0;
    transform: translateY(-2px);
}

/* إزالة التحريك للأعلى للسماح بالتوسيط القياسي */
.modal-dialog-centered {
    /* transform: translateY(-30%); */ /* تم التعليق */
    transition: transform 0.3s ease-out;
}

.modal.show .modal-dialog-centered {
     /* transform: translateY(-10%); */ /* تم التعليق */
     /* لا حاجة لتغيير التحويل عند العرض إذا لم يكن هناك تحويل أولي */
}

/* زيادة عرض المودال الصغير قليلاً */
#resetPasswordModal .modal-dialog.modal-sm {
    max-width: 380px; /* زيادة العرض من القيمة الافتراضية (عادة 300px) */
}

/* تأثير ظهور الرسائل */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تعديلات إضافية للشاشات الصغيرة جدًا */
@media (max-width: 380px) {
    .d-flex.justify-content-between.align-items-center {
        flex-direction: column;
        gap: 15px;
    }

    .register-link {
        order: 2;
        margin-top: 10px;
    }

    .btn-login {
        order: 1;
        width: 100%;
    }

    .captcha-input {
        max-width: 100%;
    }

    .password-links {
        margin-bottom: 5px;
    }

    .captcha-container {
        padding: 10px;
    }

    .form-control {
        padding: 10px;
    }
}
</style>

<script>
// التحقق من صحة المدخلات في نموذج تسجيل الدخول
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('login-form');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const captchaInput = document.getElementById('captcha');
    const togglePasswordButton = document.getElementById('toggle-password');
    const passwordIcon = document.querySelector('.password-icon');

    // وظيفة إظهار وإخفاء كلمة المرور
    togglePasswordButton.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);

        // تغيير أيقونة العين
        if (type === 'text') {
            passwordIcon.classList.remove('fa-eye-slash');
            passwordIcon.classList.add('fa-eye');
        } else {
            passwordIcon.classList.remove('fa-eye');
            passwordIcon.classList.add('fa-eye-slash');
        }
    });

    // التحقق من البريد الإلكتروني
    emailInput.addEventListener('input', function() {
        let email = this.value.toLowerCase();
        if (email && !email.endsWith('@gmail.com')) {
            this.setCustomValidity('يجب أن يكون البريد الإلكتروني من نوع Gmail');
        } else {
            this.setCustomValidity('');
        }
    });

    // التحقق من رمز التحقق
    captchaInput.addEventListener('input', function() {
        if (this.value && !/^\d+$/.test(this.value)) {
            this.setCustomValidity('يرجى إدخال أرقام فقط');
        } else if (this.value && this.value.length !== 4 && this.value.length > 0) {
            this.setCustomValidity('يجب إدخال 4 أرقام بالضبط');
        } else {
            this.setCustomValidity('');
        }
    });

    // إضافة مستمع الحدث لفتح النافذة المنبثقة
    const forgotPasswordLink = document.getElementById('forgot-password-link');
    if (forgotPasswordLink) {
        forgotPasswordLink.addEventListener('click', function(e) {
            e.preventDefault();
            const resetModal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
            resetModal.show();
        });
    }

    // تم إزالة مستمع الحدث addEventListener لتجنب استدعاء resetPassword مرتين
    // يتم الآن استدعاء الوظيفة فقط عبر السمة onclick في زر الإرسال

    // تنسيقات وتأثيرات إطار استعادة كلمة المرور
    const resetModal = document.getElementById('resetPasswordModal');
    if (resetModal) {
        resetModal.addEventListener('show.bs.modal', function(event) {
            // تنظيف رسائل الأخطاء فقط عند فتح المودال
            const messageElement = document.getElementById('reset-message');
            messageElement.classList.add('d-none');
            document.getElementById('reset-password-form').reset();

            // إضافة فئة للتحكم في المظهر
            setTimeout(() => {
                resetModal.classList.add('modal-open-animation');
            }, 50);
        });

        resetModal.addEventListener('hidden.bs.modal', function(event) {
            resetModal.classList.remove('modal-open-animation');
        });
    }
});

// وظيفة إرسال طلب استعادة كلمة المرور - تم تعريفها خارج المستمع لتكون متاحة عالمياً
function resetPassword() {
    const email = document.getElementById('reset-email').value;
    const messageElement = document.getElementById('reset-message');
    const submitResetBtn = document.getElementById('submit-reset');

    // تصحيح: إظهار رسائل تشخيصية مفصلة
    console.log('بدء وظيفة resetPassword()');
    console.log('البريد الإلكتروني المدخل:', email);
    console.log('عنصر الرسائل:', messageElement);
    console.log('زر الإرسال:', submitResetBtn);

    if (!email) {
        showResetMessage('danger', 'يرجى إدخال البريد الإلكتروني');
        return;
    }

    // إظهار رسالة "جاري المعالجة" دون التأثير على التنبيه الأصلي
    messageElement.classList.remove('d-none');
    messageElement.className = 'alert alert-info mt-3';
    messageElement.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> جاري معالجة طلبك...';
    submitResetBtn.disabled = true;

    console.log('إرسال طلب استعادة كلمة المرور إلى الخادم...');

    // إرسال الطلب إلى الخادم مباشرة بدون تأخير
    try {
        // إرسال الطلب إلى الخادم
        fetch('/reset_password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `email=${encodeURIComponent(email)}`
        })
        .then(response => {
            console.log('استجابة الخادم الأولية:', response);
            console.log('حالة الاستجابة:', response.status);
            console.log('هل الاستجابة ناجحة؟', response.ok);

            return response.json().catch(error => {
                console.error('خطأ في قراءة JSON من الاستجابة:', error);
                throw new Error('فشل في قراءة JSON: ' + error.message);
            });
        })
        .then(data => {
            console.log('بيانات الاستجابة الكاملة:', data);
            if (data.status === 'success') {
                showResetMessage('success', data.message || 'تم إرسال كلمة المرور الجديدة إلى بريدك الإلكتروني بنجاح');
                // إعادة تعيين النموذج فقط
                document.getElementById('reset-password-form').reset();
            } else {
                showResetMessage('danger', data.message || 'حدث خطأ غير معروف');
            }
        })
        .catch(error => {
            console.error('خطأ أثناء طلب استعادة كلمة المرور:', error);
            console.error('تفاصيل الخطأ:', error.message);
            showResetMessage('danger', 'حدث خطأ أثناء معالجة طلبك: ' + error.message);
        })
        .finally(() => {
            console.log('انتهت عملية طلب استعادة كلمة المرور');
            submitResetBtn.disabled = false;
        });
    } catch (error) {
        console.error('خطأ غير متوقع في وظيفة resetPassword:', error);
        console.error('تفاصيل الخطأ:', error.message);
        console.error('تتبع الخطأ:', error.stack);
        showResetMessage('danger', 'حدث خطأ غير متوقع: ' + error.message);
        submitResetBtn.disabled = false;
    }
}

// وظيفة عرض رسائل الاستجابة - تم تعريفها خارج المستمع لتكون متاحة عالمياً
function showResetMessage(type, message) {
    const messageElement = document.getElementById('reset-message');
    messageElement.classList.remove('d-none', 'alert-success', 'alert-danger', 'alert-info');
    messageElement.classList.add(`alert-${type}`);
    messageElement.innerHTML = message;

    // إخفاء الرسالة تلقائيًا بعد ثانيتين (لجميع أنواع الرسائل)
    setTimeout(function() {
        // التأكد من أن العنصر لا يزال موجودًا قبل محاولة إخفائه
        const currentMessageElement = document.getElementById('reset-message');
        if (currentMessageElement && !currentMessageElement.classList.contains('d-none')) {
             currentMessageElement.classList.add('d-none');
        }
    }, 3000); // 3000 مللي ثانية = 3 ثواني
    // تحريك للرسالة لجذب الانتباه
    messageElement.style.animation = 'none';
    setTimeout(function() {
        messageElement.style.animation = 'fadeInUp 0.3s ease forwards';
    }, 10);
}
</script>

<!-- Modal استعادة كلمة المرور -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1" aria-labelledby="resetPasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resetPasswordModalLabel">استعادة كلمة المرور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <!-- تنبيه توضيحي ثابت - تعديلات لضمان الظهور -->
                <div class="alert alert-info" id="reset-instructions" style="display: block !important; visibility: visible !important;">
                    <i class="fas fa-info-circle me-2"></i>
                    أدخل بريدك الإلكتروني لاستعادة كلمة المرور. سيتم إرسال كلمة مرور جديدة إلى بريدك الإلكتروني.
                </div>
                <form id="reset-password-form" onsubmit="event.preventDefault(); resetPassword();">
                    <div class="mb-3">
                        <input type="email" class="form-control text-right" id="reset-email" name="email"
                               placeholder="أدخل بريدك الإلكتروني" required>
                    </div>
                    <!-- رسائل النظام منفصلة عن التنبيه -->
                    <div id="reset-message" class="alert d-none mt-3"></div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="submit-reset" onclick="resetPassword()">إرسال</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
