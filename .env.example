# متغيرات البيئة المطلوبة للمشروع

# مفتاح سري للتطبيق (سيتم إنشاؤه تلقائياً في Render)
SECRET_KEY=your-secret-key-here

# إعدادات قاعدة البيانات (اختياري - إذا لم يتم تعيينه سيتم استخدام قاعدة البيانات الخارجية الحالية)
# DATABASE_URL=postgresql://username:password@host:port/database

# إعدادات Cloudinary
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# إعدادات Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=https://your-app-name.onrender.com/oauth2callback

# إعدادات Web Push
VAPID_PUBLIC_KEY=your-vapid-public-key
VAPID_PRIVATE_KEY=your-vapid-private-key
VAPID_CLAIM_EMAIL=<EMAIL>

# إعدادات البريد الإلكتروني
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=465
MAIL_USE_TLS=False
MAIL_USE_SSL=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>
MAIL_DEBUG=False

# إعدادات Twilio (اختيارية)
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
TWILIO_PHONE_NUMBER=your-twilio-number
