/**
 * ملف JavaScript لإصلاح مشكلة عرض منطقة الإدخال وبطاقة الآية الكريمة في صفحة الواتساب على الهواتف المحمولة
 * يضمن ظهور منطقة الإدخال فقط عند اختيار محادثة وعرض بطاقة الآية الكريمة بشكل صحيح
 */

document.addEventListener('DOMContentLoaded', function() {
    // التحقق مما إذا كنا في صفحة الواتساب
    if (window.location.pathname.includes('/whatsapp')) {
        // متغير لتتبع ما إذا كان المستخدم قد اختار محادثة في وضع الهاتف
        let chatSelectedInMobileMode = false;

        // الحصول على عناصر الواجهة
        const inputArea = document.getElementById('inputArea');
        const messageInput = document.getElementById('messageInput');
        const chatItems = document.querySelectorAll('.chat-item');
        const noChatSelected = document.querySelector('.no-chat-selected');
        const noChatContent = document.querySelector('.no-chat-content');
        const sidebar = document.getElementById('sidebar');
        const chatArea = document.getElementById('chatArea');
        const quoteContainer = document.getElementById('quoteContainer');
        const closeQuoteBtn = document.getElementById('closeQuoteBtn');

        // معالجة لوحة المفاتيح على الأجهزة المحمولة
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        if (isMobile) {
            // تثبيت الارتفاع الأصلي للنافذة
            const originalHeight = window.innerHeight;
            document.documentElement.style.setProperty('--original-height', `${originalHeight}px`);

            const chatWindow = document.getElementById('messagesContainer');
            const composeInput = document.getElementById('messageInput');
            const chatFooter = document.getElementById('inputArea');

            if (composeInput) {
                // حدث التركيز على حقل الكتابة (ظهور لوحة المفاتيح)
                composeInput.addEventListener('focus', function() {
                    document.body.classList.add('keyboard-open');

                    // تحديد مكان الحقل الجديد بناءً على الارتفاع المتاح
                    setTimeout(() => {
                        // التمرير إلى آخر رسالة
                        if (chatWindow) {
                            chatWindow.scrollTop = chatWindow.scrollHeight;
                        }

                        // إظهار شريط الإدخال فوق المحتوى
                        if (/iPhone|iPad|iPod/i.test(navigator.userAgent)) {
                            // خاص بـ iOS
                            const keyboardHeight = originalHeight - window.innerHeight;
                            const visualViewport = window.visualViewport || { height: window.innerHeight };
                            const adjustedHeight = Math.max(0, keyboardHeight - 20);

                            if (chatFooter) {
                                chatFooter.style.bottom = `${adjustedHeight}px`;
                            }
                        }
                    }, 300);
                });

                // حدث فقدان التركيز من حقل الكتابة (اختفاء لوحة المفاتيح)
                composeInput.addEventListener('blur', function() {
                    document.body.classList.remove('keyboard-open');

                    if (chatFooter) {
                        chatFooter.style.bottom = "0";
                    }

                    // إعادة ضبط التمرير
                    setTimeout(() => {
                        window.scrollTo(0, 0);
                    }, 100);
                });
            }

            // مراقبة تغيرات حجم النافذة (لمعرفة ظهور/اختفاء لوحة المفاتيح)
            const visualViewport = window.visualViewport;

            if (visualViewport) {
                // استخدام visualViewport API إذا كانت مدعومة (أكثر موثوقية)
                visualViewport.addEventListener('resize', function() {
                    if (visualViewport.height < originalHeight - 150) {
                        // لوحة المفاتيح مفتوحة
                        document.body.classList.add('keyboard-open');

                        // ضبط موضع حقل الكتابة بناءً على ارتفاع النافذة المتاح
                        const keyboardHeight = originalHeight - visualViewport.height;

                        if (chatFooter && document.activeElement === composeInput) {
                            chatFooter.style.bottom = `${keyboardHeight}px`;
                        }
                    } else {
                        // لوحة المفاتيح مغلقة
                        document.body.classList.remove('keyboard-open');

                        if (chatFooter) {
                            chatFooter.style.bottom = "0";
                        }

                        // إعادة ضبط التمرير
                        window.scrollTo(0, 0);
                    }
                });
            } else {
                // عودة للأسلوب التقليدي إذا لم تكن واجهة visualViewport مدعومة
                window.addEventListener('resize', function() {
                    if (window.innerHeight < originalHeight - 150) {
                        // لوحة المفاتيح مفتوحة
                        document.body.classList.add('keyboard-open');
                    } else {
                        // لوحة المفاتيح مغلقة
                        document.body.classList.remove('keyboard-open');

                        if (chatFooter) {
                            chatFooter.style.bottom = "0";
                        }

                        // إعادة ضبط التمرير
                        window.scrollTo(0, 0);
                    }
                });
            }
        }

        // معالجة زر إغلاق بطاقة الآية
        if (closeQuoteBtn && noChatContent) {
            closeQuoteBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                noChatContent.classList.add('hidden');

                // تخزين حالة الإغلاق في التخزين المحلي
                // حتى تبقى البطاقة مخفية حتى يتم تحديث الصفحة
                localStorage.setItem('quoteHidden', 'true');
            });

            // التحقق من حالة البطاقة في التخزين المحلي عند تحميل الصفحة
            const quoteHidden = localStorage.getItem('quoteHidden');
            if (quoteHidden === 'true') {
                // إذا كانت البطاقة مخفية سابقاً، نبقيها مخفية
                noChatContent.classList.add('hidden');
            } else {
                // إذا لم تكن البطاقة مخفية سابقاً، نظهرها
                noChatContent.classList.remove('hidden');
            }
        }

        // التأكد من إخفاء منطقة الإدخال في البداية
        if (inputArea) {
            inputArea.style.display = 'none';
        }

        // إضافة مستمع حدث للنقر على عناصر المحادثة
        chatItems.forEach(item => {
            item.addEventListener('click', function() {
                // إظهار منطقة الإدخال فقط عند اختيار محادثة
                if (inputArea) {
                    inputArea.style.display = 'flex';
                }

                // إخفاء رسالة "اختر محادثة"
                if (noChatSelected) {
                    noChatSelected.style.display = 'none';
                }

                // إضافة فئة للشريط الجانبي لإخفائه في الشاشات الصغيرة
                if (window.innerWidth <= 768 && sidebar) {
                    sidebar.classList.add('hidden');
                    // تحديث المتغير لتتبع أن المستخدم قد اختار محادثة في وضع الهاتف
                    chatSelectedInMobileMode = true;
                }
            });
        });

        // التحقق مما إذا كانت هناك محادثة نشطة بالفعل
        const activeChat = document.querySelector('.chat-item.active');
        if (activeChat && inputArea) {
            // إظهار منطقة الإدخال إذا كانت هناك محادثة نشطة
            inputArea.style.display = 'flex';

            // إخفاء رسالة "اختر محادثة"
            if (noChatSelected) {
                noChatSelected.style.display = 'none';
            }

            // إضافة فئة للشريط الجانبي لإخفائه في الشاشات الصغيرة
            if (window.innerWidth <= 768 && sidebar) {
                sidebar.classList.add('hidden');
            }
        } else {
            // إخفاء منطقة الإدخال إذا لم تكن هناك محادثة نشطة
            if (inputArea) {
                inputArea.style.display = 'none';
            }

            // إظهار رسالة "اختر محادثة"
            if (noChatSelected) {
                noChatSelected.style.display = 'flex';
            }

            // التأكد من إظهار الشريط الجانبي في الشاشات الصغيرة عند عدم اختيار محادثة
            if (window.innerWidth <= 768 && sidebar) {
                sidebar.classList.remove('hidden');
            }
        }

        // إضافة مستمع حدث للنقر على زر العودة للمحادثات
        const backButton = document.querySelector('.back-to-chats');
        if (backButton) {
            backButton.addEventListener('click', function() {
                // إخفاء منطقة الإدخال عند العودة لقائمة المحادثات
                if (inputArea) {
                    inputArea.style.display = 'none';
                }

                // إظهار رسالة "اختر محادثة" بشكل صحيح ولكن بدون بطاقة الآية
                if (noChatSelected) {
                    noChatSelected.style.display = 'flex';

                    // إخفاء بطاقة الآية عند الرجوع
                    if (noChatContent) {
                        noChatContent.classList.add('hidden');

                        // تخزين حالة الإخفاء في التخزين المحلي
                        localStorage.setItem('quoteHidden', 'true');

                        // إعادة تعيين التنسيقات لمنع ظهور المساحة البيضاء
                        noChatContent.style.position = '';
                        noChatContent.style.transform = '';
                        noChatContent.style.boxShadow = '';
                        noChatContent.style.borderTop = '';
                        noChatContent.style.padding = '';
                        noChatContent.style.backgroundColor = '';
                    }
                }

                // إظهار الشريط الجانبي عند العودة لقائمة المحادثات
                if (sidebar) {
                    sidebar.classList.remove('hidden');
                }

                // إعادة ضبط متغير تتبع اختيار المحادثة في وضع الهاتف
                chatSelectedInMobileMode = false;
            });
        }

        // إضافة مستمع لتغيير حجم النافذة
        window.addEventListener('resize', function() {
            // تحديث فئة العرض للهاتف المحمول
            if (window.innerWidth <= 768) {
                document.body.classList.add('mobile-view');

                // في وضع الهاتف، نتحقق مما إذا كان المستخدم قد اختار محادثة سابقاً
                if (chatSelectedInMobileMode && sidebar) {
                    // إذا كان المستخدم قد اختار محادثة سابقاً، نبقي الشريط الجانبي مخفياً
                    sidebar.classList.add('hidden');
                } else {
                    // إذا لم يكن المستخدم قد اختار محادثة سابقاً، نتحقق من وجود محادثة نشطة
                    const activeChat = document.querySelector('.chat-item.active');
                    if (activeChat && sidebar) {
                        // إذا كانت هناك محادثة نشطة، نبقي الشريط الجانبي مخفياً
                        sidebar.classList.add('hidden');
                        // تحديث المتغير
                        chatSelectedInMobileMode = true;
                    } else if (!activeChat && sidebar) {
                        // إذا لم تكن هناك محادثة نشطة، نظهر الشريط الجانبي
                        sidebar.classList.remove('hidden');
                    }
                }
            } else {
                // في وضع الشاشات الكبيرة، نزيل فئة العرض للهاتف المحمول ونظهر الشريط الجانبي دائماً
                document.body.classList.remove('mobile-view');
                if (sidebar) {
                    sidebar.classList.remove('hidden');
                }
            }
        });

        // إضافة مستمع حدث للتركيز على حقل الإدخال
        if (messageInput) {
            messageInput.addEventListener('focus', function() {
                // التأكد من أن الشريط الجانبي يبقى مخفياً عند التركيز على حقل الإدخال في الشاشات الصغيرة
                if (window.innerWidth <= 768 && sidebar) {
                    sidebar.classList.add('hidden');
                }

                // إخفاء شريط التنقل العلوي (navbar) عند التركيز على حقل الإدخال في وضع الهاتف
                if (window.innerWidth <= 768) {
                    // إضافة فئة لإخفاء شريط التنقل
                    document.body.classList.add('input-focused');

                    // الحصول على شريط التنقل
                    const navbar = document.querySelector('nav.navbar');
                    if (navbar) {
                        // حفظ حالة العرض الأصلية
                        navbar.dataset.originalDisplay = navbar.style.display || '';
                        // إخفاء شريط التنقل
                        navbar.style.display = 'none';
                    }

                    // الحصول على رأس الشريط الجانبي
                    const sidebarHeader = document.querySelector('.sidebar-header');
                    if (sidebarHeader) {
                        // تثبيت رأس الشريط الجانبي في الأعلى
                        sidebarHeader.classList.add('fixed-top');
                    }

                    // تعديل موضع منطقة الإدخال
                    if (inputArea) {
                        // تثبيت منطقة الإدخال في الأسفل
                        inputArea.classList.add('fixed-bottom');
                    }
                }
            });

            // إضافة مستمع لفقدان التركيز من حقل الإدخال
            messageInput.addEventListener('blur', function() {
                // إعادة الصفحة إلى وضعها الطبيعي عند إغلاق لوحة المفاتيح
                if (window.innerWidth <= 768) {
                    // إزالة فئة التركيز
                    document.body.classList.remove('input-focused');

                    // إعادة إظهار شريط التنقل
                    const navbar = document.querySelector('nav.navbar');
                    if (navbar && navbar.dataset.originalDisplay !== undefined) {
                        navbar.style.display = navbar.dataset.originalDisplay;
                    }

                    // إعادة رأس الشريط الجانبي إلى وضعه الطبيعي
                    const sidebarHeader = document.querySelector('.sidebar-header');
                    if (sidebarHeader) {
                        sidebarHeader.classList.remove('fixed-top');
                    }

                    // إعادة منطقة الإدخال إلى وضعها الطبيعي
                    if (inputArea) {
                        inputArea.classList.remove('fixed-bottom');
                    }
                }
            });
        }
    }
});
