/*
 * ملف CSS خاص بالشاشات الصغيرة (الهواتف المحمولة)
 * يحتوي على تعديلات لتحسين عرض صفحة الترحيب على الشاشات الصغيرة
 */

/* تعديلات عامة للشاشات الصغيرة */
@media (max-width: 768px) {
    /* تعديلات رأس الترحيب */
    .welcome-header {
        padding: 20px 15px !important;
        margin-top: 10px !important;
        margin-bottom: 30px !important;
    }

    /* تنسيقات حاوية الأزرار في صفحة admin */
    .card-header .d-flex.justify-content-center {
        flex-wrap: nowrap !important; /* منع التفاف الأزرار */
        width: 100% !important;
        padding: 0 5px !important;
    }

    .welcome-title {
        font-size: 1.8rem !important;
        margin-bottom: 10px !important;
    }

    .welcome-subtitle {
        font-size: 1rem !important;
        max-width: 100% !important;
        line-height: 1.5 !important;
    }

    .dynamic-icon {
        font-size: 2.5rem !important;
        margin-bottom: 10px !important;
    }

    /* تعديلات بطاقات الميزات */
    .feature-card {
        padding: 20px 15px !important;
        margin-bottom: 15px !important;
    }

    .feature-icon {
        width: 60px !important;
        height: 60px !important;
        margin-bottom: 15px !important;
    }

    .feature-title {
        font-size: 1.3rem !important;
        margin-bottom: 10px !important;
    }

    .feature-description {
        font-size: 0.9rem !important;
        margin-bottom: 15px !important;
    }

    .feature-link {
        font-size: 0.9rem !important;
        padding: 8px 15px !important;
    }

    /* تعديلات معلومات المستخدم */
    .user-info {
        top: 10px !important;
        left: 10px !important;
        padding: 3px 8px !important;
        position: fixed !important; /* ثابتة لا تتحرك مع الصفحة */
    }

    .user-avatar {
        width: 30px !important;
        height: 30px !important;
        font-size: 14px !important;
    }

    .user-name {
        font-size: 0.8rem !important;
    }

    /* تعديلات الأزرار */
    .btn-profile, .btn-logout, .btn-message {
        font-size: 0.9rem !important;
        padding: 8px 15px !important;
    }

    /* تعديلات حاوية الأزرار للهواتف - جعلها أفقية في شريط التنقل ومحاذية لليسار */
    .vertical-buttons-container {
        top: 25px !important; /* زيادة المسافة من الأعلى لتتناسب مع الزر الأخضر */
        left: 10px !important; /* محاذاة لليسار */
        gap: 10px !important;
        display: flex !important;
        flex-direction: row !important; /* جعلها أفقية في الهواتف */
        position: absolute !important; /* تأكيد على أن الأزرار متحركة مع الصفحة */
        z-index: 1050 !important; /* زيادة مستوى الطبقة لضمان ظهورها فوق العناصر الأخرى */
    }

    /* تقليل حجم الأزرار في الهواتف */
    .vertical-buttons-container .btn {
        width: 35px !important;
        height: 35px !important;
        font-size: 0.8rem !important;
    }

    /* تعديلات الإشعارات */
    .notification-badge {
        width: 16px !important;
        height: 16px !important;
        font-size: 0.6rem !important;
        top: -3px !important;
        right: -3px !important;
    }

    /* تعديل شارة الإشعارات في زر الرسائل */
    #nav-unread-count {
        width: 14px !important;
        height: 14px !important;
        font-size: 0.6rem !important;
        top: 0 !important;
        right: 0 !important;
    }

    /* تعديلات الصفوف والأعمدة */
    .row {
        margin-right: -10px !important;
        margin-left: -10px !important;
    }

    .col-md-6, .col-lg-4 {
        padding-right: 10px !important;
        padding-left: 10px !important;
    }
}

/* تعديلات للشعار في الشاشات الصغيرة */
@media (max-width: 768px) {
    .navbar-brand {
        margin-right: 0 !important;
    }

    /* تقليل حجم الأيقونة قليلاً في الشاشات الصغيرة */
    .brand-icon {
        width: 45px !important;
        height: 45px !important;
    }

    /* تنسيقات أزرار التنقل في صفحة admin للشاشات المتوسطة */
    .card-header .nav-button {
        padding: 6px 8px !important;
        font-size: 0.85rem !important;
        margin: 0 3px !important;
        min-width: 0 !important;
        flex: 1 !important; /* توزيع المساحة بالتساوي */
        max-width: calc(25% - 8px) !important; /* تحديد الحد الأقصى للعرض */
    }

    /* تنسيق الأيقونات داخل الأزرار */
    .card-header .nav-button i {
        font-size: 1rem !important;
    }
}

/* تعديلات إضافية للشاشات الصغيرة جداً */
@media (max-width: 576px) {
    .welcome-title {
        font-size: 1.5rem !important;
    }

    .welcome-container {
        padding: 10px !important;
    }

    .feature-card {
        padding: 15px 10px !important;
    }

    .feature-icon {
        width: 50px !important;
        height: 50px !important;
    }

    .feature-title {
        font-size: 1.2rem !important;
    }

    /* تعديل ترتيب العناصر للعرض الرأسي */
    .welcome-actions {
        flex-direction: column !important;
        gap: 10px !important;
    }

    .welcome-actions .btn {
        width: 100% !important;
    }

    /* تعديلات الدوائر الزخرفية للهواتف - فقط في شريط النافبار */
    .navbar .decoration-circle {
        opacity: 0.2 !important;
        transform: scale(0.7) !important;
    }

    /* تنسيقات أزرار التنقل في صفحة admin للهواتف */
    .card-header .nav-button {
        width: calc(25% - 10px) !important; /* جعل الأزرار متساوية العرض (4 أزرار) */
        height: 40px !important; /* توحيد ارتفاع الأزرار */
        padding: 4px 2px !important; /* تقليل المساحة الداخلية */
        font-size: 0.7rem !important; /* تصغير حجم الخط */
        margin: 0 2px !important; /* تقليل الهوامش الجانبية */
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        flex-direction: column !important;
    }

    /* تنسيق الأيقونات داخل الأزرار */
    .card-header .nav-button i {
        font-size: 0.9rem !important;
        margin-bottom: 2px !important;
        margin-right: 0 !important; /* إزالة الهامش الأيمن للأيقونة */
    }

    .navbar .circle-1 {
        width: 80px !important;
        height: 80px !important;
        top: -15px !important;
        right: -15px !important;
    }

    .navbar .circle-2 {
        width: 50px !important;
        height: 50px !important;
        bottom: 5px !important;
        right: 20% !important;
    }

    .navbar .circle-3 {
        width: 60px !important;
        height: 60px !important;
        bottom: -15px !important;
        left: 5% !important;
    }

    /* تعديلات خط حقوق النشر */
    .footer p {
        white-space: nowrap !important;
        font-size: 0.8rem !important;
    }
}

/* تنسيقات SweetAlert لتغيير نوع الإشراف */
.swal2-popup {
    font-size: 0.9rem !important;
    border-radius: 10px !important;
}

.swal2-title {
    font-size: 1.2rem !important;
    margin-bottom: 10px !important;
}

.swal2-html-container {
    margin: 0 !important;
    padding: 10px !important;
}

.swal2-select {
    font-size: 0.9rem !important;
    padding: 8px !important;
    border-radius: 5px !important;
    border: 1px solid #ddd !important;
}

.swal2-actions {
    margin-top: 15px !important;
}

.swal2-confirm, .swal2-cancel {
    font-size: 0.9rem !important;
    padding: 8px 15px !important;
    border-radius: 5px !important;
}

/* تعديلات خاصة بالهواتف الصغيرة جداً */
@media (max-width: 380px) {
    /* تصغير إضافي لمودال تغيير كلمة المرور ومودال نسيت كلمة المرور */
    #changePasswordModal .modal-dialog,
    #resetPasswordModal .modal-dialog,
    #addSupervisorModal .modal-dialog {
        max-width: 95% !important;
        margin: 5px auto !important;
    }

    /* تصغير إضافي لـ SweetAlert في الهواتف الصغيرة جداً */
    .swal2-popup {
        font-size: 0.8rem !important;
        padding: 15px 10px !important;
        width: 85% !important;
    }

    .swal2-title {
        font-size: 1rem !important;
    }

    .swal2-select {
        font-size: 0.8rem !important;
        padding: 6px !important;
    }

    .swal2-confirm, .swal2-cancel {
        font-size: 0.8rem !important;
        padding: 6px 12px !important;
    }

    /* تصغير إضافي لمودال إضافة مشرف جديد */
    #addSupervisorModal .modal-dialog {
        max-width: 90% !important;
        margin: 0 auto !important;
    }

    #addSupervisorModal .modal-content {
        border-radius: 8px !important;
    }

    #addSupervisorModal .modal-title {
        font-size: 0.95rem !important;
        text-align: center !important;
        width: 100% !important;
    }

    #addSupervisorModal .modal-header {
        padding: 8px 12px !important;
        justify-content: center !important;
        position: relative !important;
    }

    #addSupervisorModal .btn-close {
        position: absolute !important;
        right: 8px !important;
        top: 8px !important;
        padding: 4px !important;
        font-size: 0.8rem !important;
    }

    #addSupervisorModal .modal-body {
        padding: 10px 15px !important;
    }

    #addSupervisorModal .form-select {
        font-size: 0.8rem !important;
        padding: 6px 8px !important;
        text-align: right !important;
        direction: rtl !important;
    }

    #addSupervisorModal .modal-footer {
        padding: 8px 15px !important;
        justify-content: center !important;
    }

    #addSupervisorModal .btn {
        font-size: 0.8rem !important;
        padding: 4px 12px !important;
        min-width: 80px !important;
    }

    /* تنسيقات إضافية لقسم إدارة المشرفين للهواتف الصغيرة جداً */
    #supervisors-section .table {
        min-width: 600px !important; /* تقليل العرض الأدنى قليلاً */
        font-size: 0.75rem !important; /* تصغير حجم الخط أكثر */
    }

    /* تعديل أزرار الإجراءات للهواتف الصغيرة جداً */
    #supervisors-section .btn-group {
        display: flex !important;
        flex-direction: row !important; /* ترتيب الأزرار أفقياً */
        flex-wrap: nowrap !important; /* منع التفاف الأزرار */
        gap: 3px !important; /* تقليل المسافة بين الأزرار */
        justify-content: center !important; /* توسيط الأزرار */
    }

    #supervisors-section .btn-group .btn {
        width: auto !important; /* عرض تلقائي للأزرار */
        margin: 0 !important;
        padding: 3px 4px !important; /* تقليل المساحة الداخلية */
        font-size: 0.65rem !important;
        white-space: nowrap !important; /* منع التفاف النص */
    }

    /* تعديل عرض عمود الإجراءات */
    #supervisors-section .table th:nth-child(5),
    #supervisors-section .table td:nth-child(5) {
        width: 140px !important; /* زيادة عرض عمود الإجراءات قليلاً */
    }

    /* إخفاء كلمة "النوع" و "الإشراف" في الأزرار للهواتف الصغيرة جداً */
    #supervisors-section .btn-group .btn .btn-text-small {
        display: none !important; /* إخفاء الكلمات الصغيرة في الهواتف الصغيرة جداً */
    }

    /* تعديل حجم الخط وتباعد الأزرار */
    #supervisors-section .btn-group .btn {
        font-size: 0.7rem !important;
        padding: 3px 6px !important;
    }

    /* إضافة تلميح عند تحويم المؤشر فوق الأزرار */
    #supervisors-section .btn-group .btn:hover {
        opacity: 0.9 !important;
    }

    /* تصغير إضافي لمودال نسيت كلمة المرور */
    #resetPasswordModal .modal-title {
        font-size: 1rem !important;
    }

    /* تنسيقات إضافية لأزرار التنقل في صفحة admin للهواتف الصغيرة جداً */
    .card-header .nav-button {
        width: calc(25% - 6px) !important; /* تقليل المسافة بين الأزرار */
        height: 36px !important; /* تقليل ارتفاع الأزرار */
        padding: 2px 1px !important; /* تقليل المساحة الداخلية أكثر */
        font-size: 0.65rem !important; /* تصغير حجم الخط أكثر */
        margin: 0 1px !important; /* تقليل الهوامش الجانبية أكثر */
    }

    /* تنسيق الأيقونات داخل الأزرار للهواتف الصغيرة جداً */
    .card-header .nav-button i {
        font-size: 0.8rem !important;
        margin-bottom: 1px !important;
    }

    /* تعديل حاوية الأزرار للهواتف الصغيرة جداً */
    .card-header .d-flex.justify-content-center {
        padding: 0 !important;
    }

    #resetPasswordModal #reset-instructions {
        font-size: 0.8rem !important;
        padding: 8px !important;
    }

    #resetPasswordModal .form-control {
        font-size: 0.8rem !important;
        padding: 6px 8px !important;
    }

    #resetPasswordModal .btn {
        font-size: 0.8rem !important;
        padding: 4px 8px !important;
    }

    #changePasswordModal .modal-title {
        font-size: 1rem !important;
    }

    #changePasswordModal .form-label {
        font-size: 0.8rem !important;
    }

    #changePasswordModal .form-control {
        font-size: 0.8rem !important;
        padding: 6px 8px !important;
    }

    #changePasswordModal .btn {
        font-size: 0.8rem !important;
        padding: 4px 8px !important;
    }

    /* تصغير إضافي لبطاقة البروفايل المنسدلة */
    .dropdown-menu {
        min-width: 180px !important;
        max-width: 220px !important;
    }

    /* تصغير إضافي لصورة البروفايل في القائمة المنسدلة */
    .dropdown-header .dropdown-profile-img {
        width: 40px !important;
        height: 40px !important;
    }

    /* تصغير إضافي للخط في رأس القائمة المنسدلة */
    .dropdown-header h6 {
        font-size: 0.85rem !important;
    }

    .dropdown-header small {
        font-size: 0.7rem !important;
    }

    /* تقليل إضافي للتباعد في القائمة المنسدلة */
    .dropdown-item {
        padding: 5px 12px !important;
        font-size: 0.8rem !important;
    }

    .dropdown-item i {
        font-size: 0.9rem !important;
    }

    .welcome-title {
        font-size: 1.3rem !important;
    }

    .welcome-subtitle {
        font-size: 0.9rem !important;
    }

    .feature-title {
        font-size: 1.1rem !important;
    }

    .feature-description {
        font-size: 0.8rem !important;
    }

    /* تصغير حجم الخط في صفحة الملف الشخصي للهواتف الصغيرة جداً */
    .profile-card h4.fw-bold {
        font-size: 1.1rem !important;
    }

    .profile-card p.text-muted.small {
        font-size: 0.7rem !important;
    }

    .info-card .card-title,
    .admin-info-card .card-title,
    .bio-card .card-title {
        font-size: 0.9rem !important;
    }

    .info-label {
        font-size: 0.7rem !important;
    }

    .info-value {
        font-size: 0.8rem !important;
    }

    .card-body p.info-value {
        font-size: 0.8rem !important;
    }

    .default-image-options .btn-sm {
        font-size: 0.65rem !important;
        padding: 0.15rem 0.3rem !important;
    }

    /* تعديلات خاصة بصفحة الرسائل للهواتف الصغيرة جداً */
    .message-item {
        padding: 10px 8px !important;
    }

    .message-content {
        font-size: 0.85rem !important;
    }

    .card-header h5 {
        font-size: 1rem !important;
    }

    .attachment-name {
        max-width: 120px !important;
    }

    /* تحسين أزرار الإجراءات للشاشات الصغيرة جداً */
    .message-item .btn-link {
        min-width: 28px !important;
        min-height: 28px !important;
    }

    /* تقليل حجم الهوامش */
    .card-body {
        padding: 10px 8px !important;
    }
}

/* تنسيقات صفحة الرسائل للهواتف المحمولة */
@media (max-width: 768px) {
    /* تعديل هيكل الصفحة للهواتف */
    .messages-container {
        padding: 0 !important;
    }

    /* تعديل هوامش الحاوية الرئيسية */
    .container.mt-4 {
        padding: 10px !important;
        margin-top: 10px !important;
    }

    /* تعديل ترتيب العناصر في صفحة الرسائل */
    body .container.mt-4 .row {
        flex-direction: column !important;
    }

    /* جعل نموذج إرسال الرسالة في الأسفل */
    body .container.mt-4 .row > div.col-md-4 {
        order: 2 !important;
    }

    /* جعل صناديق الرسائل في الأعلى */
    body .container.mt-4 .row > div.col-md-8 {
        order: 1 !important;
    }

    /* تعديل نموذج إرسال الرسائل */
    #message-form .form-control,
    #message-form .form-select,
    #message-form .btn {
        font-size: 0.95rem !important;
        padding: 8px 12px !important;
    }

    #message-form textarea {
        min-height: 80px !important;
    }

    /* تحسين عرض عناصر الرسائل */
    .message-item {
        padding: 12px 10px !important;
        margin-bottom: 10px !important;
        border-radius: 8px !important;
    }

    /* تحسين عرض محتوى الرسالة */
    .message-content {
        margin-top: 10px !important;
        font-size: 0.95rem !important;
    }

    .message-content p {
        margin-bottom: 8px !important;
        line-height: 1.4 !important;
    }

    /* تحسين عرض المرفقات */
    .attachment-section {
        margin-top: 8px !important;
    }

    .attachment-section .d-flex {
        padding: 8px !important;
    }

    .attachment-name {
        font-size: 0.85rem !important;
        max-width: 180px !important;
        display: inline-block !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
    }

    /* تحسين أزرار التفاعل مع الرسائل */
    .btn-sm {
        padding: 4px 8px !important;
        font-size: 0.8rem !important;
    }

    /* تحسين عرض معلومات المرسل/المستلم */
    .teacher-name-link strong {
        font-size: 0.9rem !important;
    }

    .text-muted.d-block {
        font-size: 0.75rem !important;
    }

    /* تحسين عرض شارة "جديد" */
    .badge.bg-primary {
        font-size: 0.7rem !important;
        padding: 3px 6px !important;
    }

    /* تحسين أزرار الإجراءات */
    .message-item .btn-link {
        padding: 3px !important;
    }

    /* تحسين عرض الإشعارات */
    #unread-count {
        font-size: 0.75rem !important;
        padding: 3px 6px !important;
    }

    /* تحسين نموذج إرسال الرسائل */
    .col-md-4 .card {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    /* تحسين عرض معلومات الملفات المرفقة */
    #selected-file-info {
        font-size: 0.8rem !important;
    }

    #selected-file-info .d-flex {
        padding: 6px !important;
    }

    /* تحسين عرض رسائل الخطأ */
    .invalid-feedback {
        font-size: 0.8rem !important;
        padding: 6px !important;
    }

    /* تحسين عرض التنبيهات */
    .alert {
        padding: 8px 12px !important;
        font-size: 0.85rem !important;
        max-width: 90% !important;
        right: 5% !important;
    }
}

/* تنسيقات المودالات والقوائم المنسدلة للهواتف المحمولة */
@media (max-width: 768px) {
    /* تصغير حجم مودال تغيير كلمة المرور */
    #changePasswordModal .modal-dialog,
    #resetPasswordModal .modal-dialog,
    #addSupervisorModal .modal-dialog {
        max-width: 90% !important;
        margin: 10px auto !important;
    }

    /* تصغير إطار إضافة مشرف جديد */
    #addSupervisorModal .modal-content {
        border-radius: 10px !important;
    }

    #addSupervisorModal .modal-header {
        padding: 10px 15px !important;
    }

    #addSupervisorModal .modal-title {
        font-size: 1.1rem !important;
    }

    #addSupervisorModal .modal-body {
        padding: 15px !important;
    }

    #addSupervisorModal .form-label {
        font-size: 0.9rem !important;
        margin-bottom: 5px !important;
    }

    #addSupervisorModal .form-select {
        font-size: 0.9rem !important;
        padding: 8px 10px !important;
    }

    #addSupervisorModal .modal-footer {
        padding: 10px 15px !important;
    }

    #addSupervisorModal .btn {
        font-size: 0.9rem !important;
        padding: 5px 10px !important;
    }

    /* تصغير حجم مودال نسيت كلمة المرور */
    #resetPasswordModal .modal-content {
        border-radius: 10px !important;
    }

    #resetPasswordModal .modal-header {
        padding: 10px 15px !important;
    }

    #resetPasswordModal .modal-title {
        font-size: 1.1rem !important;
    }

    #resetPasswordModal .modal-body {
        padding: 15px !important;
    }

    #resetPasswordModal #reset-instructions {
        font-size: 0.85rem !important;
        padding: 10px !important;
        margin-bottom: 10px !important;
    }

    #resetPasswordModal .form-control {
        font-size: 0.9rem !important;
        padding: 8px 10px !important;
    }

    #resetPasswordModal .modal-footer {
        padding: 10px 15px !important;
    }

    #resetPasswordModal .btn {
        font-size: 0.9rem !important;
        padding: 5px 10px !important;
    }

    #changePasswordModal .modal-content {
        border-radius: 10px !important;
    }

    #changePasswordModal .modal-header {
        padding: 10px 15px !important;
    }

    #changePasswordModal .modal-title {
        font-size: 1.1rem !important;
    }

    #changePasswordModal .modal-body {
        padding: 15px !important;
    }

    #changePasswordModal .form-label {
        font-size: 0.9rem !important;
        margin-bottom: 5px !important;
    }

    #changePasswordModal .form-control {
        font-size: 0.9rem !important;
        padding: 8px 10px !important;
    }

    #changePasswordModal .modal-footer {
        padding: 10px 15px !important;
    }

    #changePasswordModal .btn {
        font-size: 0.9rem !important;
        padding: 5px 10px !important;
    }

    /* تصغير حجم بطاقة البروفايل المنسدلة */
    .dropdown-menu {
        min-width: 200px !important;
        max-width: 250px !important;
        margin-top: 5px !important;
    }

    /* تصغير حجم صورة البروفايل في القائمة المنسدلة */
    .dropdown-header .dropdown-profile-img {
        width: 50px !important;
        height: 50px !important;
    }

    /* تصغير حجم الخط في رأس القائمة المنسدلة */
    .dropdown-header h6 {
        font-size: 0.9rem !important;
        margin-top: 5px !important;
    }

    .dropdown-header small {
        font-size: 0.75rem !important;
    }

    /* تقليل التباعد في القائمة المنسدلة */
    .dropdown-header {
        padding: 10px !important;
    }

    .dropdown-item {
        padding: 6px 15px !important;
        font-size: 0.85rem !important;
    }

    /* محاذاة النص في القوائم المنسدلة إلى اليمين */
    select.form-select,
    select.form-control,
    .dropdown-menu .dropdown-item,
    .form-select option {
        text-align: right !important;
        direction: rtl !important;
    }

    /* تغيير موضع أيقونة السهم في القوائم المنسدلة */
    .form-select {
        background-position: left 0.75rem center !important;
        padding-left: 0.75rem !important;
        padding-right: 2.25rem !important;
    }

    /* تأكيد على محاذاة خيارات القائمة المنسدلة */
    option {
        text-align: right !important;
        direction: rtl !important;
        unicode-bidi: isolate !important;
    }

    /* تنسيق القوائم المنسدلة في المودالات */
    .modal-body .form-select,
    .modal-body select.form-control {
        text-align: right !important;
        direction: rtl !important;
    }

    /* تنسيق القوائم المنسدلة في النماذج */
    form .form-select,
    form select.form-control {
        text-align: right !important;
        direction: rtl !important;
    }

    /* تنسيق عناصر القائمة المنسدلة */
    .dropdown-item {
        text-align: right !important;
    }

    /* تنسيق القوائم المنسدلة في الصفحات المختلفة */
    .dropdown-menu {
        text-align: right !important;
    }

    /* تنسيق القوائم المنسدلة في صفحة الملف الشخصي */
    #personal-form select,
    #admin-form select,
    #bio-form select {
        text-align: right !important;
        direction: rtl !important;
    }

    /* تنسيق القوائم المنسدلة في صفحة الرسائل */
    #message-form select {
        text-align: right !important;
        direction: rtl !important;
    }

    /* تنسيق القوائم المنسدلة في المودالات */
    .modal select {
        text-align: right !important;
        direction: rtl !important;
    }
}

/* تنسيقات قسم إدارة المشرفين للهواتف المحمولة */
@media (max-width: 768px) {
    /* تصغير الخط في صفحة قسم الإشراف */
    #supervisors-section {
        font-size: 0.9rem !important;
    }

    #supervisors-section h6 {
        font-size: 1rem !important;
    }

    #supervisors-section .btn-sm {
        font-size: 0.8rem !important;
        padding: 0.25rem 0.5rem !important;
    }

    /* تحسين عرض جدول المشرفين في وضع الهاتف */
    #supervisors-section .table-responsive {
        overflow-x: auto !important;
    }

    /* تعديل عرض الجدول ليناسب محتوى الهاتف */
    #supervisors-section .table {
        min-width: 650px !important; /* عرض أدنى للجدول لضمان عدم التفاف النص */
        table-layout: fixed !important; /* تثبيت تخطيط الجدول */
    }

    /* تعديل عرض الأعمدة في جدول المشرفين */
    #supervisors-section .table th:nth-child(1),
    #supervisors-section .table td:nth-child(1) {
        width: 40px !important; /* عمود الرقم */
    }

    #supervisors-section .table th:nth-child(2),
    #supervisors-section .table td:nth-child(2) {
        width: 120px !important; /* عمود اسم المشرف */
    }

    #supervisors-section .table th:nth-child(3),
    #supervisors-section .table td:nth-child(3) {
        width: 150px !important; /* عمود البريد الإلكتروني */
    }

    #supervisors-section .table th:nth-child(4),
    #supervisors-section .table td:nth-child(4) {
        width: 100px !important; /* عمود نوع الإشراف */
    }

    #supervisors-section .table th:nth-child(5),
    #supervisors-section .table td:nth-child(5) {
        width: 240px !important; /* عمود الإجراءات */
    }

    /* تنسيق أزرار الإجراءات في جدول المشرفين */
    #supervisors-section .btn-group .btn {
        padding: 4px 8px !important;
        font-size: 0.75rem !important;
        white-space: nowrap !important;
    }

    /* تعديل نص الأزرار لجعلها أقصر */
    #supervisors-section .btn-group .btn i {
        margin-right: 2px !important;
    }

    /* إخفاء جزء من النص في الأزرار للشاشات الصغيرة */
    @media (max-width: 768px) {
        #supervisors-section .btn-group .btn-outline-primary span.btn-text {
            display: none !important;
        }

        #supervisors-section .btn-group .btn-outline-danger span.btn-text {
            display: none !important;
        }
    }

    /* تنسيق النص داخل الخلايا لمنع التفافه */
    #supervisors-section .table td {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }

    /* توسيط العبارات في رأس الجدول */
    #supervisors-section .table th {
        text-align: center !important;
        vertical-align: middle !important;
    }

    /* تنسيق البريد الإلكتروني لمنع التفافه */
    #supervisors-section .table td:nth-child(3) {
        direction: ltr !important; /* اتجاه من اليسار إلى اليمين للبريد الإلكتروني */
        text-align: left !important;
        font-size: 0.8rem !important;
    }

    /* جعل أزرار الإجراءات متجاورة في وضع الهاتف */
    #supervisors-section .btn-group {
        display: flex !important;
        flex-direction: row !important; /* ترتيب الأزرار أفقياً */
        flex-wrap: nowrap !important; /* منع التفاف الأزرار */
        gap: 5px !important; /* مسافة بين الأزرار */
        justify-content: center !important; /* توسيط الأزرار */
    }
}

/* تنسيقات إضافية لقسم إدارة المشرفين للهواتف الصغيرة جداً */
@media (max-width: 576px) {
    /* تقليل حجم الخط في جدول المشرفين */
    #supervisors-section .table {
        font-size: 0.8rem !important;
    }

    /* تقليل حجم الأزرار أكثر */
    #supervisors-section .btn-group .btn {
        padding: 3px 6px !important;
        font-size: 0.7rem !important;
    }

    /* تعديل عرض الأعمدة للهواتف الصغيرة جداً */
    #supervisors-section .table th:nth-child(2),
    #supervisors-section .table td:nth-child(2) {
        width: 100px !important;
    }

    #supervisors-section .table th:nth-child(3),
    #supervisors-section .table td:nth-child(3) {
        width: 130px !important;
    }

    #supervisors-section .table th:nth-child(4),
    #supervisors-section .table td:nth-child(4) {
        width: 90px !important;
    }

    #supervisors-section .table th:nth-child(5),
    #supervisors-section .table td:nth-child(5) {
        width: 220px !important;
    }
}

/* تنسيقات صفحة الملف الشخصي للهواتف المحمولة */
@media (max-width: 768px) {
    /* تصغير حجم الخط في صفحة الملف الشخصي */
    .profile-card h4 {
        font-size: 1.3rem !important;
    }

    .profile-card p {
        font-size: 0.85rem !important;
    }

    .info-card .card-title,
    .admin-info-card .card-title,
    .bio-card .card-title {
        font-size: 1.1rem !important;
    }

    .info-label {
        font-size: 0.85rem !important;
        margin-bottom: 3px !important;
    }

    .info-value {
        font-size: 0.9rem !important;
    }

    .info-item {
        margin-bottom: 12px !important;
    }

    /* تصغير حجم الأزرار */
    .edit-section-btn {
        font-size: 0.75rem !important;
        padding: 3px 8px !important;
    }

    /* تصغير حجم الصورة الشخصية */
    .img-profile {
        width: 90px !important;
        height: 90px !important;
        font-size: 35px !important;
    }

    /* تصغير حجم أزرار اختيار الصورة الافتراضية */
    .default-image-btn {
        font-size: 0.75rem !important;
        padding: 3px 6px !important;
    }
}

/* تنسيقات إضافية لصفحة الملف الشخصي للهواتف الصغيرة جداً */
@media (max-width: 576px) {
    /* تصغير حجم الخط أكثر في صفحة الملف الشخصي */
    .profile-card h4 {
        font-size: 1.1rem !important;
    }

    .profile-card p {
        font-size: 0.8rem !important;
    }

    .info-card .card-title,
    .admin-info-card .card-title,
    .bio-card .card-title {
        font-size: 1rem !important;
    }

    .info-label {
        font-size: 0.75rem !important;
    }

    .info-value {
        font-size: 0.8rem !important;
    }

    /* تصغير حجم الصورة الشخصية أكثر */
    .img-profile {
        width: 80px !important;
        height: 80px !important;
        font-size: 30px !important;
    }

    /* تعديل تباعد العناصر */
    .card-body {
        padding: 12px !important;
    }

    .card-header {
        padding: 10px 12px !important;
    }

    /* تصغير حجم نموذج التعديل */
    .form-label {
        font-size: 0.8rem !important;
        margin-bottom: 3px !important;
    }

    .form-control, .form-select {
        font-size: 0.8rem !important;
        padding: 6px 8px !important;
    }

    .btn {
        font-size: 0.8rem !important;
        padding: 5px 10px !important;
    }
}

/* تنسيقات قسم التدرج السنوي للهواتف المحمولة */
@media (max-width: 768px) {
    /* تصغير حجم الخط في صفحة قسم التدرج السنوي */
    #yearly-progress-section h6 {
        font-size: 1rem !important;
    }

    #yearly-progress-section .form-select {
        font-size: 0.85rem !important;
        padding: 4px 8px !important;
    }

    #yearly-progress-section span {
        font-size: 0.85rem !important;
    }

    #yearly-progress-section .btn {
        font-size: 0.85rem !important;
        padding: 5px 10px !important;
    }

    /* تنسيق جدول التدرج السنوي */
    #yearly-progress-section .table-responsive {
        max-height: 70vh !important; /* ارتفاع أقصى للجدول */
        overflow-y: auto !important; /* إضافة شريط تمرير عمودي */
        border: 1px solid #dee2e6 !important; /* إضافة حدود للجدول */
        border-radius: 5px !important;
    }

    #yearly-progress-section .yearly-progress-table {
        min-width: 650px !important; /* عرض أدنى للجدول */
        font-size: 0.85rem !important;
    }

    #yearly-progress-section .yearly-progress-table th {
        font-size: 0.85rem !important;
        padding: 8px 5px !important;
    }

    #yearly-progress-section .yearly-progress-table td {
        font-size: 0.85rem !important;
        padding: 8px 5px !important;
    }

    /* تنسيق خلايا الجدول القابلة للتحرير */
    #yearly-progress-section .yearly-progress-table td[contenteditable="true"] {
        min-height: 35px !important;
        max-height: 120px !important;
        font-size: 0.85rem !important;
    }

    /* تنسيق أزرار الإجراءات في الجدول */
    #yearly-progress-section .yearly-progress-table .btn-sm {
        padding: 3px 6px !important;
        font-size: 0.75rem !important;
    }

    /* تنسيق خلية الشهر */
    #yearly-progress-section .month-cell {
        font-size: 0.85rem !important;
        width: 50px !important;
    }
}

/* تنسيقات إضافية لقسم التدرج السنوي للهواتف الصغيرة جداً */
@media (max-width: 576px) {
    /* تصغير حجم الخط أكثر في صفحة قسم التدرج السنوي */
    #yearly-progress-section h6 {
        font-size: 0.9rem !important;
    }

    #yearly-progress-section .form-select {
        font-size: 0.8rem !important;
        padding: 3px 6px !important;
    }

    #yearly-progress-section span {
        font-size: 0.8rem !important;
    }

    #yearly-progress-section .btn {
        font-size: 0.8rem !important;
        padding: 4px 8px !important;
    }

    /* تنسيق جدول التدرج السنوي للهواتف الصغيرة جداً */
    #yearly-progress-section .yearly-progress-table {
        min-width: 600px !important;
        font-size: 0.8rem !important;
    }

    #yearly-progress-section .yearly-progress-table th {
        font-size: 0.75rem !important;
        padding: 6px 4px !important;
    }

    #yearly-progress-section .yearly-progress-table td {
        font-size: 0.75rem !important;
        padding: 6px 4px !important;
    }

    /* تنسيق خلايا الجدول القابلة للتحرير */
    #yearly-progress-section .yearly-progress-table td[contenteditable="true"] {
        min-height: 30px !important;
        max-height: 100px !important;
        font-size: 0.75rem !important;
    }

    /* تنسيق أزرار الإجراءات في الجدول */
    #yearly-progress-section .yearly-progress-table .btn-sm {
        padding: 2px 5px !important;
        font-size: 0.7rem !important;
    }

    /* تنسيق خلية الشهر */
    #yearly-progress-section .month-cell {
        font-size: 0.75rem !important;
        width: 40px !important;
    }

    /* تنسيق حقل عدد الأسابيع */
    #yearly-progress-section #september-weeks {
        width: 40px !important;
        font-size: 0.75rem !important;
    }

    /* تنسيق أزرار الحفظ والتفريغ */
    #yearly-progress-section #clear-lessons-btn,
    #yearly-progress-section #save-progress-btn {
        font-size: 0.75rem !important;
        padding: 4px 8px !important;
    }
}

/* تنسيقات قسم إدارة الأساتذة للهواتف المحمولة */
@media (max-width: 768px) {
    /* تصغير الخط في صفحة قسم إدارة الأساتذة */
    #teachers-section {
        font-size: 0.9rem !important;
    }

    #teachers-section h6 {
        font-size: 1rem !important;
    }

    #teachers-section .btn-sm {
        font-size: 0.8rem !important;
        padding: 0.25rem 0.5rem !important;
    }

    /* تحسين عرض جدول الأساتذة في وضع الهاتف */
    #teachers-section .table-responsive {
        overflow-x: auto !important;
    }

    /* تعديل عرض الجدول ليناسب محتوى الهاتف */
    #teachers-section .table {
        min-width: 650px !important; /* عرض أدنى للجدول لضمان عدم التفاف النص */
        table-layout: fixed !important; /* تثبيت تخطيط الجدول */
    }

    /* تعديل عرض الأعمدة في جدول الأساتذة */
    #teachers-section .table th:nth-child(1),
    #teachers-section .table td:nth-child(1) {
        width: 40px !important; /* عمود الرقم */
    }

    #teachers-section .table th:nth-child(2),
    #teachers-section .table td:nth-child(2) {
        width: 140px !important; /* عمود اسم الأستاذ - زيادة العرض */
    }

    #teachers-section .table th:nth-child(3),
    #teachers-section .table td:nth-child(3) {
        width: 140px !important; /* عمود مكان العمل - زيادة العرض */
    }

    #teachers-section .table th:nth-child(4),
    #teachers-section .table td:nth-child(4) {
        width: 130px !important; /* عمود البريد الإلكتروني */
    }

    #teachers-section .table th:nth-child(5),
    #teachers-section .table td:nth-child(5) {
        width: 90px !important; /* عمود رقم الهاتف */
    }

    #teachers-section .table th:nth-child(6),
    #teachers-section .table td:nth-child(6) {
        width: 110px !important; /* عمود الإجراءات */
    }

    /* تنسيق أزرار الإجراءات في جدول الأساتذة */
    #teachers-section .btn-group {
        display: flex !important;
        flex-direction: row !important;
        flex-wrap: nowrap !important;
        gap: 5px !important;
        justify-content: center !important;
        width: 100% !important;
    }

    #teachers-section .btn-group .btn {
        padding: 6px 10px !important;
        font-size: 0.8rem !important;
        white-space: nowrap !important;
        flex: 1 !important;
        min-width: 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* تحسين ظهور الأيقونات في الأزرار */
    #teachers-section .btn-group .btn i {
        margin-right: 4px !important;
    }

    /* تنسيق النص داخل الخلايا */
    #teachers-section .table td {
        padding: 8px 5px !important;
    }

    /* السماح بالتفاف النص في خلايا اسم الأستاذ ومكان العمل */
    #teachers-section .table td:nth-child(2),
    #teachers-section .table td:nth-child(3) {
        white-space: normal !important;
        word-wrap: break-word !important;
        min-width: 120px !important;
    }

    /* منع التفاف النص في باقي الخلايا */
    #teachers-section .table td:not(:nth-child(2)):not(:nth-child(3)) {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }

    /* توسيط العبارات في رأس الجدول */
    #teachers-section .table th {
        text-align: center !important;
        vertical-align: middle !important;
    }

    /* تنسيق البريد الإلكتروني لمنع التفافه */
    #teachers-section .table td:nth-child(4) {
        direction: ltr !important; /* اتجاه من اليسار إلى اليمين للبريد الإلكتروني */
        text-align: left !important;
        font-size: 0.8rem !important;
    }

    /* جعل أزرار الإجراءات متجاورة في وضع الهاتف */
    #teachers-section .btn-group {
        display: flex !important;
        flex-direction: row !important; /* ترتيب الأزرار أفقياً */
        flex-wrap: nowrap !important; /* منع التفاف الأزرار */
        gap: 5px !important; /* مسافة بين الأزرار */
        justify-content: center !important; /* توسيط الأزرار */
    }
}

/* تنسيقات إضافية لقسم إدارة الأساتذة للهواتف الصغيرة جداً */
@media (max-width: 576px) {
    /* تعديل حجم الخط في جدول الأساتذة */
    #teachers-section .table {
        font-size: 0.8rem !important; /* حجم خط مناسب للقراءة */
    }

    /* تحسين حجم خط اسم الأستاذ ومكان العمل */
    #teachers-section .table td:nth-child(2) a,
    #teachers-section .table td:nth-child(3) span {
        font-size: 0.85rem !important; /* حجم خط أكبر قليلاً للمعلومات المهمة */
        font-weight: 500 !important; /* خط أكثر سماكة للوضوح */
    }

    /* تحسين عرض أزرار الإجراءات للهواتف الصغيرة جداً */
    #teachers-section .btn-group {
        width: 100% !important;
        gap: 3px !important;
        display: flex !important;
        flex-direction: row !important; /* ترتيب الأزرار أفقياً */
        justify-content: center !important;
    }

    #teachers-section .btn-group .btn {
        padding: 6px 8px !important;
        font-size: 0.75rem !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        min-width: 0 !important;
        flex: 1 !important;
    }

    /* تعديل عرض عمود الإجراءات للهواتف الصغيرة جداً */
    #teachers-section .table th:nth-child(6),
    #teachers-section .table td:nth-child(6) {
        width: 120px !important;
        padding: 8px 3px !important;
    }

    /* تحسين ظهور الأيقونات في الأزرار */
    #teachers-section .btn-group .btn i {
        margin-right: 3px !important;
        font-size: 0.8rem !important;
    }

    /* إخفاء أزرار الحفظ والإلغاء في وضع التعديل على الهواتف */
    #teachers-section .edit-mode.btn-group {
        display: none !important;
    }

    /* تعديل عرض الأعمدة للهواتف الصغيرة جداً */
    #teachers-section .table th:nth-child(2),
    #teachers-section .table td:nth-child(2) {
        width: 130px !important; /* زيادة عرض عمود اسم الأستاذ */
    }

    #teachers-section .table th:nth-child(3),
    #teachers-section .table td:nth-child(3) {
        width: 130px !important; /* زيادة عرض عمود مكان العمل */
    }

    #teachers-section .table th:nth-child(4),
    #teachers-section .table td:nth-child(4) {
        width: 120px !important;
    }

    #teachers-section .table th:nth-child(5),
    #teachers-section .table td:nth-child(5) {
        width: 80px !important;
    }

    /* تحسين عرض النص في خلايا اسم الأستاذ ومكان العمل */
    #teachers-section .table td:nth-child(2) a,
    #teachers-section .table td:nth-child(3) span {
        display: inline-block !important;
        line-height: 1.3 !important;
        max-width: 100% !important;
    }

    /* تصغير حقول البحث */
    #teachers-section .input-group {
        margin-bottom: 10px !important;
    }

    #teachers-section .input-group-text {
        padding: 6px 10px !important;
        font-size: 0.8rem !important;
    }

    #teachers-section .form-control {
        font-size: 0.8rem !important;
        padding: 6px 10px !important;
    }
}

/* تنسيقات صفحة الملف الشخصي للهواتف المحمولة */
@media (max-width: 576px) {
    /* تصغير حجم الخط في صفحة الملف الشخصي */
    .profile-card h4.fw-bold {
        font-size: 1.2rem !important;
    }

    .profile-card p.text-muted.small {
        font-size: 0.75rem !important;
    }

    .info-card .card-title,
    .admin-info-card .card-title,
    .bio-card .card-title {
        font-size: 1rem !important;
    }

    .info-label {
        font-size: 0.75rem !important;
    }

    .info-value {
        font-size: 0.85rem !important;
    }

    .card-body p.info-value {
        font-size: 0.85rem !important;
        line-height: 1.4 !important;
    }

    .btn-sm {
        font-size: 0.75rem !important;
        padding: 0.25rem 0.5rem !important;
    }

    .default-image-options .btn-sm {
        font-size: 0.7rem !important;
        padding: 0.2rem 0.4rem !important;
    }
}

/* تنسيقات صفحة جداول الأساتذة للهواتف المحمولة */
@media (max-width: 768px) {
    /* تصغير الخط في صفحة جداول الأساتذة */
    #teachers-table tbody td {
        font-size: 0.85rem !important;
        padding: 0.25rem 0.4rem !important;
    }

    #teachers-table thead th {
        font-size: 0.8rem !important;
        padding: 0.4rem 0.3rem !important;
    }

    /* تصغير حجم مودال معاينة الجدول */
    #scheduleModal .modal-dialog {
        max-width: 95% !important;
        margin: 0.5rem auto !important;
    }

    #scheduleModal .modal-content {
        border-radius: 8px !important;
    }

    #scheduleModal .modal-header {
        padding: 8px 12px !important;
    }

    #scheduleModal .modal-body {
        padding: 10px !important;
    }

    #scheduleModal .modal-footer {
        padding: 8px 12px !important;
    }

    #scheduleModalLabel {
        font-size: 0.85rem !important;
    }

    /* تصغير حجم جدول التوقيت في المودال */
    #schedule-table-modal {
        font-size: 0.6rem !important;
    }

    #schedule-table-modal th,
    #schedule-table-modal td {
        padding: 0.12rem 0.08rem !important;
        height: 22px !important;
    }

    #schedule-table-modal .time-cell {
        width: 65px !important;
        font-size: 0.6rem !important;
    }

    #schedule-table-modal th {
        font-size: 0.7rem !important;
        padding: 6px 3px !important;
    }
}

/* تنسيقات إضافية لصفحة جداول الأساتذة للهواتف الصغيرة جداً */
@media (max-width: 576px) {
    /* تصغير الخط أكثر في صفحة جداول الأساتذة */
    #teachers-table tbody td {
        font-size: 0.8rem !important;
        padding: 0.2rem 0.3rem !important;
    }

    #teachers-table thead th {
        font-size: 0.75rem !important;
        padding: 0.3rem 0.25rem !important;
    }

    /* تصغير حجم مودال معاينة الجدول أكثر */
    #scheduleModal .modal-dialog {
        max-width: 98% !important;
        margin: 0.3rem auto !important;
    }

    #scheduleModal .modal-header {
        padding: 6px 10px !important;
    }

    #scheduleModal .modal-body {
        padding: 8px !important;
    }

    #scheduleModal .modal-footer {
        padding: 6px 10px !important;
    }

    #scheduleModalLabel {
        font-size: 0.8rem !important;
    }

    /* تصغير حجم جدول التوقيت في المودال أكثر */
    #schedule-table-modal {
        font-size: 0.55rem !important;
    }

    #schedule-table-modal th,
    #schedule-table-modal td {
        padding: 0.1rem 0.05rem !important;
        height: 20px !important;
    }

    #schedule-table-modal .time-cell {
        width: 60px !important;
        font-size: 0.55rem !important;
    }

    #schedule-table-modal th {
        font-size: 0.65rem !important;
        padding: 5px 2px !important;
    }

    /* تصغير حجم زر المعاينة */
    .view-schedule-btn {
        width: 22px !important;
        height: 22px !important;
    }

    .view-schedule-btn i {
        font-size: 0.7rem !important;
    }
}

/* تنسيقات صفحة progress-dashboard للهواتف المحمولة */
@media (max-width: 576px) {
    /* تصغير حجم الخط في جدول مدى التقدم */
    #progress-dashboard-table {
        font-size: 0.75rem !important;
    }

    #progress-dashboard-table thead th {
        font-size: 0.75rem !important;
        padding: 6px 4px !important;
    }

    #progress-dashboard-table tbody td {
        padding: 6px 4px !important;
        font-size: 0.75rem !important;
        /* جعل العبارات في سطر واحد بدون نقاط */
        white-space: nowrap !important;
        overflow: visible !important;
        text-overflow: clip !important;
    }

    /* تصغير حجم الخط في عنوان الصفحة */
    .container-fluid.py-4 .card-header h5 {
        font-size: 0.95rem !important;
    }

    /* تصغير حجم الخط في الأسبوع الحالي */
    #current-week-display {
        font-size: 1.2rem !important;
        padding: 4px 8px !important;
    }

    /* تصغير حجم الخط في بطاقات الحالة */
    .status-badge {
        font-size: 0.7rem !important;
        padding: 3px 6px !important;
    }

    /* تصغير حجم رقم الأسبوع */
    .week-number {
        width: 24px !important;
        height: 24px !important;
        line-height: 24px !important;
        font-size: 0.7rem !important;
    }

    /* تصغير حجم رقم السطر */
    .row-number {
        width: 20px !important;
        height: 20px !important;
        line-height: 20px !important;
        font-size: 0.65rem !important;
    }

    /* تصغير حجم الخط في أدوات البحث والفلترة */
    #search-teacher, #search-workplace, #filter-weeks, #refresh-data {
        font-size: 0.75rem !important;
    }

    /* تصغير حجم الخط في التنبيه */
    .alert-info {
        font-size: 0.8rem !important;
        padding: 8px !important;
    }

    /* تعديل عرض عمود عنوان آخر حصة تعليمية */
    #progress-dashboard-table td:nth-child(5) {
        max-width: none !important;
        white-space: nowrap !important;
        overflow: visible !important;
        text-overflow: clip !important;
    }

    /* تعديل عرض عمود مكان العمل */
    #progress-dashboard-table td:nth-child(3) {
        max-width: none !important;
        white-space: nowrap !important;
        overflow: visible !important;
        text-overflow: clip !important;
    }
}

/* تنسيقات صفحة التقدم للهواتف المحمولة */
@media (max-width: 576px) {
    /* تصغير حجم الخط في جدول التقدم */
    #progress-table {
        font-size: 0.75rem !important;
    }

    /* تصغير حجم الخط في عناوين البطاقات */
    .container-fluid.py-4 .card-header h5 {
        font-size: 0.95rem !important;
    }

    /* تصغير حجم الخط في القائمة الجانبية */
    .container-fluid.py-4 .col-md-4 h6 {
        font-size: 0.9rem !important;
    }

    /* تصغير حجم الخط في عرض الأسبوع الحالي */
    #current-week-display {
        font-size: 1.5rem !important;
    }

    #current-month-week {
        font-size: 0.75rem !important;
    }

    #progress-table th {
        font-size: 0.75rem !important;
        padding: 6px 4px !important;
    }

    #progress-table td {
        padding: 6px 4px !important;
    }

    /* تعديل عمود الإجراءات ليظهر الأيقونات فقط */
    #progress-table .btn-group .btn {
        padding: 4px 6px !important;
        min-width: 32px !important;
    }

    /* إخفاء النص في أزرار الإجراءات وإظهار الأيقونات فقط */
    #progress-table .btn-group .btn-edit-progress::after,
    #progress-table .btn-group .btn-delete-progress::after {
        display: none !important;
    }

    /* إخفاء النص "تعديل" و "حذف" */
    #progress-table .btn-edit-progress span,
    #progress-table .btn-delete-progress span {
        display: none !important;
    }

    /* تعديل حجم الأيقونات */
    #progress-table .btn-group .btn i {
        font-size: 0.9rem !important;
        margin: 0 !important;
    }

    /* تصغير عرض عمود الإجراءات */
    #progress-table th:last-child,
    #progress-table td:last-child {
        width: 80px !important;
    }
}

/* تنسيقات إضافية للهواتف الصغيرة جداً */
@media (max-width: 576px) {
    /* تغيير ترتيب العناصر في صفحة الرسائل - نموذج الإرسال في الأسفل */
    body .container.mt-4 .row {
        display: flex !important;
        flex-direction: column !important;
    }

    /* تغيير ترتيب العناصر داخل الصف - مهم جداً لجعل صناديق الرسائل في الأعلى */
    body .container.mt-4 .row > div.col-md-4 {
        order: 2 !important; /* جعل نموذج إرسال الرسالة في الأسفل */
        width: 100% !important;
        max-width: 100% !important;
    }

    body .container.mt-4 .row > div.col-md-8 {
        order: 1 !important; /* جعل صناديق الرسائل في الأعلى */
        margin-bottom: 20px !important; /* إضافة مسافة أسفل صناديق الرسائل */
        width: 100% !important;
        max-width: 100% !important;
    }

    /* تنسيقات صفحة تقدم البرنامج للهواتف */
    body .container-fluid.py-4 .row {
        display: flex !important;
        flex-direction: column !important;
    }

    /* جعل بطاقة آخر حصة تعليمية في الأعلى */
    body .container-fluid.py-4 .row > div.col-md-8 {
        order: 1 !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    /* جعل بطاقة ملخص التقدم أسفل بطاقة آخر حصة تعليمية */
    body .container-fluid.py-4 .row > div.col-md-4 {
        order: 2 !important;
        width: 100% !important;
        max-width: 100% !important;
        margin-top: 15px !important;
    }

    /* تعديل عرض الأزرار في صفحة تقدم البرنامج */
    body .container-fluid.py-4 .card-header.bg-primary {
        padding-bottom: 15px !important;
    }

    /* إخفاء الأزرار من الشريط الأزرق */
    body .container-fluid.py-4 .card-header.bg-primary .btn {
        display: none !important;
    }

    /* إضافة حاوية جديدة للأزرار أسفل الشريط الأزرق */
    body .container-fluid.py-4 .col-md-8 .card-body::before {
        content: "";
        display: flex !important;
        justify-content: space-between !important;
        margin-bottom: 15px !important;
        gap: 10px !important;
    }

    /* إضافة الأزرار الجديدة بعد الشريط الأزرق */
    body .container-fluid.py-4 .col-md-8 .card::after {
        content: "";
        display: flex !important;
        justify-content: space-between !important;
        padding: 0 15px !important;
        margin-top: -15px !important;
        margin-bottom: 15px !important;
    }

    /* تعديل عنوان آخر حصة تعليمية */
    body .container-fluid.py-4 .card-header.bg-primary h5 {
        width: 100% !important;
        margin-bottom: 0 !important;
    }

    /* إضافة أزرار جديدة بعد الشريط الأزرق */
    body .container-fluid.py-4 .col-md-8 .card .card-header + .card-body {
        position: relative !important;
        padding-top: 60px !important; /* إضافة مساحة للأزرار */
    }

    /* إضافة أزرار جديدة بعد الشريط الأزرق */
    body .container-fluid.py-4 .col-md-8 .card .card-body {
        position: relative !important;
    }

    /* إضافة حاوية للأزرار */
    body .container-fluid.py-4 .col-md-8 .card-header + .card-body::before {
        content: "" !important;
        position: absolute !important;
        top: 0 !important;
        right: 0 !important;
        left: 0 !important;
        height: 60px !important;
        background-color: #f8f9fa !important;
        border-bottom: 1px solid #eee !important;
        z-index: 5 !important;
    }

    /* إضافة الأزرار الجديدة */
    body .container-fluid.py-4 .col-md-8 .card-body {
        padding-top: 70px !important; /* إضافة مساحة للأزرار */
    }

    /* إضافة JavaScript لإنشاء الأزرار وربطها بالأحداث */
    @media (max-width: 576px) {
        body.mobile-buttons-added .container-fluid.py-4 .col-md-8 .card-header + .card-body {
            padding-top: 70px !important;
        }
    }

    /* تنسيقات CSS للأزرار الجديدة التي سيتم إنشاؤها بواسطة JavaScript */
    .mobile-progress-button {
        position: absolute !important;
        top: 15px !important;
        width: calc(50% - 20px) !important;
        background-color: #28a745 !important; /* خلفية خضراء */
        color: white !important;
        padding: 10px 5px !important;
        border-radius: 5px !important;
        text-align: center !important;
        font-size: 0.9rem !important;
        font-weight: 500 !important;
        cursor: pointer !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        z-index: 10 !important;
        border: none !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    }

    .mobile-progress-button.yearly-btn {
        right: 15px !important;
    }

    .mobile-progress-button.add-btn {
        left: 15px !important;
    }

    /* تصغير حجم خط المودالات في صفحة تقدم البرنامج - وضع الجوال فقط */
    @media (max-width: 576px) {
        /* تصغير حجم خط عنوان المودال */
        #progressModal .modal-title,
        #yearlyProgressModal .modal-title {
            font-size: 0.95rem !important;
        }

        /* تصغير حجم خط التسميات */
        #progressModal .form-label,
        #yearlyProgressModal .form-label {
            font-size: 0.8rem !important;
        }

        /* تصغير حجم خط الحقول والقوائم المنسدلة */
        #progressModal .form-select,
        #progressModal .form-control,
        #yearlyProgressModal .form-select,
        #yearlyProgressModal .form-control {
            font-size: 0.8rem !important;
            padding: 5px 8px !important;
        }

        /* تصغير حجم خط الأزرار */
        #progressModal .btn,
        #yearlyProgressModal .btn {
            font-size: 0.8rem !important;
            padding: 4px 8px !important;
        }

        /* تصغير وتوسيط مودال إضافة سجل تقدم */
        #progressModal .modal-dialog {
            max-width: 90% !important;
            margin: 10px auto !important;
        }

        #progressModal .modal-content {
            border-radius: 8px !important;
        }

        #progressModal .modal-header {
            padding: 8px 12px !important;
        }

        #progressModal .modal-body {
            padding: 12px !important;
        }

        #progressModal .modal-footer {
            padding: 8px 12px !important;
            justify-content: center !important;
        }

        /* تصغير حجم خط جدول التدرج السنوي */
        #yearlyProgressModal .table {
            font-size: 0.8rem !important;
        }

        #yearlyProgressModal .table th {
            font-size: 0.85rem !important;
        }
    }
}

    /* تعديل عرض بطاقات الرسائل */
    .card {
        margin-bottom: 15px !important;
        border-radius: 10px !important;
    }

    /* تحسين مظهر صناديق الرسائل */
    .col-md-8 .card {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    /* إضافة مسافة بين صندوق الرسائل المستلمة وصندوق الرسائل المرسلة */
    .col-md-8 .card.mb-4 {
        margin-bottom: 15px !important;
    }

    .card-header {
        padding: 10px 12px !important;
        border-radius: 10px 10px 0 0 !important;
    }

    .card-body {
        padding: 12px 10px !important;
    }

    /* تحسين عرض المرفقات للشاشات الصغيرة جداً */
    .attachment-name {
        max-width: 140px !important;
    }

    /* تقليل حجم الخط في الرسائل */
    .message-content p {
        font-size: 0.9rem !important;
    }

    /* تحسين أزرار الإجراءات للمس */
    .message-item .btn-link {
        min-width: 30px !important;
        min-height: 30px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        margin: 0 2px !important;
    }

    /* تحسين زر تحميل المرفقات */
    .attachment-section .btn-sm {
        min-width: 32px !important;
        min-height: 32px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* تحسين زر تم القراءة */
    .mark-as-read {
        padding: 6px 10px !important;
        font-size: 0.8rem !important;
    }
}
