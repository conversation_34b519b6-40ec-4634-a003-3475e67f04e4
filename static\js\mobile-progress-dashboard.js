/**
 * ملف JavaScript لتحسين عرض صفحة مدى التقدم (progress-dashboard) على الهواتف المحمولة
 */

document.addEventListener('DOMContentLoaded', function() {
    // التحقق من أن الصفحة هي صفحة مدى التقدم
    if (window.location.pathname.includes('/progress-dashboard')) {
        // التحقق من أن الشاشة صغيرة (هاتف محمول)
        if (window.innerWidth <= 576) {
            setupMobileProgressDashboard();
        }

        // إعادة تهيئة الصفحة عند تغيير حجم الشاشة
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 576) {
                setupMobileProgressDashboard();
            }
        });
    }
});

/**
 * إعداد صفحة مدى التقدم للهواتف المحمولة
 */
function setupMobileProgressDashboard() {
    // التحقق من أن التعديلات لم يتم تطبيقها بالفعل
    if (document.body.classList.contains('mobile-progress-dashboard-setup')) {
        return;
    }

    // إضافة فئة للجسم للإشارة إلى أن التعديلات تمت
    document.body.classList.add('mobile-progress-dashboard-setup');

    // إضافة مراقب للتغييرات في جدول مدى التقدم
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // تطبيق التعديلات على الخلايا الجديدة
                applyMobileStyles();
            }
        });
    });

    // بدء مراقبة التغييرات في جدول مدى التقدم
    const progressTableBody = document.getElementById('progress-dashboard-body');
    if (progressTableBody) {
        observer.observe(progressTableBody, { childList: true, subtree: true });
    }

    // تطبيق التعديلات على الخلايا الحالية
    applyMobileStyles();

    console.log('تم إعداد صفحة مدى التقدم للهواتف المحمولة');
}

/**
 * تطبيق تنسيقات الهواتف المحمولة على خلايا الجدول
 */
function applyMobileStyles() {
    // إزالة خاصية title من خلايا عنوان آخر حصة تعليمية
    // لأننا نريد عرض النص كاملاً بدلاً من الاعتماد على تلميح العنوان
    const lessonTitleCells = document.querySelectorAll('#progress-dashboard-table td:nth-child(5)');
    lessonTitleCells.forEach(function(cell) {
        // حفظ النص الأصلي
        const originalText = cell.textContent;

        // إزالة خاصية title
        cell.removeAttribute('title');

        // التأكد من أن النص يظهر في سطر واحد بدون نقاط
        cell.style.whiteSpace = 'nowrap';
        cell.style.overflow = 'visible';
        cell.style.textOverflow = 'clip';
    });

    // تطبيق نفس التعديلات على خلايا مكان العمل
    const workplaceCells = document.querySelectorAll('#progress-dashboard-table td:nth-child(3)');
    workplaceCells.forEach(function(cell) {
        cell.removeAttribute('title');
        cell.style.whiteSpace = 'nowrap';
        cell.style.overflow = 'visible';
        cell.style.textOverflow = 'clip';
    });
}
