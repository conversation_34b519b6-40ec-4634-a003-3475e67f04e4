// دالة لإعادة ترقيم الأسابيع بشكل متسلسل حسب الشهور
function renumberWeeks() {
    const table = document.getElementById('yearly-progress-table');
    if (!table) return;
    
    const allRows = table.querySelectorAll('tr:not(.add-row-container)');
    
    // ترتيب الأشهر
    const monthOrder = [
        'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر',
        'جانفي', 'فيفري', 'مارس', 'أفريل', 'ماي'
    ];
    
    // ترتيب الصفوف حسب الشهر
    const sortedRows = Array.from(allRows).sort((a, b) => {
        const monthA = a.getAttribute('data-month');
        const monthB = b.getAttribute('data-month');
        
        const monthIndexA = monthOrder.indexOf(monthA);
        const monthIndexB = monthOrder.indexOf(monthB);
        
        if (monthIndexA !== monthIndexB) {
            return monthIndexA - monthIndexB;
        }
        
        // إذا كان نفس الشهر، نرتب حسب موقع الصف في DOM
        return Array.from(table.rows).indexOf(a) - Array.from(table.rows).indexOf(b);
    });
    
    // إعادة ترقيم الأسابيع
    let currentWeek = 1;
    
    sortedRows.forEach(row => {
        const weekCell = row.querySelector('.week-number');
        
        if (weekCell) {
            weekCell.textContent = currentWeek.toString();
            row.setAttribute('data-week', currentWeek.toString());
            currentWeek++;
        }
    });
}

// دالة لحذف صف
function deleteRow(button) {
    const row = button.closest('tr');
    const table = document.getElementById('yearly-progress-table');
    const month = row.getAttribute('data-month');
    
    // الحصول على جميع الصفوف التي تنتمي لنفس الشهر
    const monthRows = table.querySelectorAll(`tr[data-month="${month}"]:not(.add-row-container)`);
    
    // إذا كان هذا هو الصف الوحيد المتبقي للشهر، لا نفعل شيئًا ولا نظهر أي رسالة
    if (monthRows.length <= 1) {
        return;
    }
    
    // حذف الصف مباشرة دون رسالة تأكيد
    row.remove();
    
    // تحديث rowspan لخلية الشهر
    updateMonthRowspans();
    
    // إعادة ترقيم الأسابيع بشكل متسلسل
    renumberWeeks();
    
    // حفظ التغييرات بعد الحذف
    saveAfterDelete();
}

// وظيفة لحفظ التغييرات بعد حذف الأسبوع
function saveAfterDelete() {
    // الحصول على السنة المحددة
    const selectedYear = parseInt(document.getElementById('year-selector').value);
    
    // إعادة ترقيم الأسابيع للتأكد من أنها متسلسلة قبل الحفظ
    renumberWeeks();
    
    // الحصول على بيانات الجدول
    const table = document.getElementById('yearly-progress-table');
    const rows = table.querySelectorAll('tr:not(.add-row-container)');
    
    // التحقق من وجود صفوف في الجدول
    if (rows.length === 0) {
        return;
    }
    
    const progressData = [];
    
    // حفظ معلومات هيكل الجدول
    const tableStructure = {
        deletedWeeks: [],
        maxWeek: 0
    };
    
    // تحديد أقصى رقم أسبوع
    rows.forEach(row => {
        const weekNumber = parseInt(row.getAttribute('data-week'));
        if (weekNumber > tableStructure.maxWeek) {
            tableStructure.maxWeek = weekNumber;
        }
    });
    
    // تحديد الأسابيع المحذوفة (الأرقام المفقودة)
    for (let i = 1; i <= tableStructure.maxWeek; i++) {
        const weekExists = Array.from(rows).some(row => parseInt(row.getAttribute('data-week')) === i);
        if (!weekExists) {
            tableStructure.deletedWeeks.push(i);
        }
    }
    
    // معالجة كل صف في الجدول
    rows.forEach(row => {
        const weekNumber = parseInt(row.getAttribute('data-week'));
        const month = row.getAttribute('data-month');
        
        // الحصول على خلايا المحتوى (الحصص)
        const contentCells = row.querySelectorAll('[contenteditable="true"]');
        
        // التحقق مما إذا كانت الخلايا مدمجة
        const isMerged = contentCells[0].hasAttribute('colspan');
        
        let firstLesson = contentCells[0].textContent.trim();
        let secondLesson = isMerged ? "" : contentCells[1].textContent.trim();
        
        // إضافة البيانات إلى المصفوفة
        progressData.push({
            week: weekNumber,
            month: month,
            firstLesson: firstLesson,
            secondLesson: secondLesson,
            isMerged: isMerged
        });
    });
    
    // تأكد من ترتيب البيانات حسب رقم الأسبوع
    progressData.sort((a, b) => a.week - b.week);
    
    // التحقق من وجود رمز CSRF في الصفحة
    const csrfToken = document.querySelector('input[name="csrf_token"]').value;
    
    // إرسال البيانات إلى الخادم باستخدام طلب Fetch API
    fetch('/save-yearly-progress', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            year: selectedYear,
            data: progressData,
            tableStructure: tableStructure
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // تخزين السنة الحالية لإعادة تحميلها بعد الحفظ
            const currentYear = selectedYear;
            
            // حفظ مؤقت للبيانات المحلية - سيتم استبدالها عند إعادة تحميل البيانات
            window.latestYearlyProgressData = {
                year: currentYear,
                data: progressData
            };
            
            // إعادة تحميل البيانات مباشرة بعد الحفظ للتأكد من التزامن الفوري
            if (typeof loadYearlyProgressData === 'function') {
                loadYearlyProgressData(currentYear);
            }
        }
    })
    .catch(error => {
        console.error('Error saving changes after delete:', error);
    });
}

// وظيفة حفظ التدرجات السنوية المحسنة
function saveYearlyProgress() {
    // إعادة ترقيم الأسابيع للتأكد من أنها متسلسلة قبل الحفظ
    renumberWeeks();
    
    // تغيير حالة الزر لإظهار أن الحفظ قيد التقدم
    const saveButton = document.getElementById('save-progress-btn');
    const originalButtonText = saveButton.innerHTML;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري الحفظ...';
    saveButton.disabled = true;
    
    // الحصول على السنة المحددة من القائمة المنسدلة
    const selectedYear = parseInt(document.getElementById('year-selector').value);
    
    // الحصول على بيانات الجدول
    const table = document.getElementById('yearly-progress-table');
    const rows = table.querySelectorAll('tr:not(.add-row-container)');
    
    const progressData = [];
    
    // حفظ معلومات هيكل الجدول
    const tableStructure = {
        deletedWeeks: [],
        maxWeek: 0
    };
    
    // تحديد أقصى رقم أسبوع
    rows.forEach(row => {
        const weekNumber = parseInt(row.getAttribute('data-week'));
        if (weekNumber > tableStructure.maxWeek) {
            tableStructure.maxWeek = weekNumber;
        }
    });
    
    // تحديد الأسابيع المحذوفة (الأرقام المفقودة)
    for (let i = 1; i <= tableStructure.maxWeek; i++) {
        const weekExists = Array.from(rows).some(row => parseInt(row.getAttribute('data-week')) === i);
        if (!weekExists) {
            tableStructure.deletedWeeks.push(i);
        }
    }
    
    // معالجة كل صف في الجدول
    rows.forEach(row => {
        // استرجاع البيانات من الصف
        const weekCell = row.querySelector('.week-number');
        const month = row.getAttribute('data-month');
        const weekNumber = parseInt(weekCell.textContent); // استخدام النص المعروض لرقم الأسبوع
        const contentCells = row.querySelectorAll('[contenteditable="true"]');
        
        // التحقق مما إذا كانت الخلايا مدمجة
        const isMerged = contentCells[0].hasAttribute('colspan');
        
        let firstLesson = contentCells[0].textContent.trim();
        let secondLesson = isMerged ? "" : contentCells[1].textContent.trim();
        
        // إضافة البيانات إلى المصفوفة
        progressData.push({
            week: weekNumber,
            month: month,
            firstLesson: firstLesson,
            secondLesson: secondLesson,
            isMerged: isMerged
        });
    });
    
    // تأكد من ترتيب البيانات حسب رقم الأسبوع
    progressData.sort((a, b) => a.week - b.week);
    
    // التحقق من وجود رمز CSRF في الصفحة
    const csrfToken = document.querySelector('input[name="csrf_token"]').value;
    
    // إرسال البيانات إلى الخادم باستخدام طلب Fetch API
    fetch('/save-yearly-progress', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            year: selectedYear, // استخدام السنة المحددة
            data: progressData,
            tableStructure: tableStructure // إضافة معلومات هيكل الجدول
        })
    })
    .then(response => response.json())
    .then(data => {
        // إعادة الزر إلى حالته الأصلية بعد نجاح الحفظ
        saveButton.innerHTML = '<i class="fas fa-check me-1"></i> تم الحفظ';
        
        // إعادة تحميل البيانات بعد الحفظ للتأكد من التزامن
        if (typeof loadYearlyProgressData === 'function') {
            loadYearlyProgressData(selectedYear);
        }
        
        // تغيير الزر مرة أخرى بعد ثانيتين
        setTimeout(() => {
            saveButton.innerHTML = originalButtonText;
            saveButton.disabled = false;
        }, 2000);
    })
    .catch(error => {
        console.error('Error saving yearly progress:', error);
        saveButton.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i> فشل الحفظ';
        
        // تغيير الزر مرة أخرى بعد ثانيتين
        setTimeout(() => {
            saveButton.innerHTML = originalButtonText;
            saveButton.disabled = false;
        }, 2000);
    });
}

// عند تحميل المستند، نضيف مستمعي الأحداث
document.addEventListener('DOMContentLoaded', function() {
    // تأكد من تهيئة الجدول
    if (document.getElementById('yearly-progress-table')) {
        // حذف جميع صفوف أزرار الإضافة (add-row-container)
        const addRowContainers = document.querySelectorAll('.add-row-container');
        addRowContainers.forEach(function(container) {
            container.remove();
        });
        
        // إضافة مستمع حدث لزر الحفظ
        const saveButton = document.getElementById('save-progress-btn');
        if (saveButton) {
            saveButton.addEventListener('click', saveYearlyProgress);
        }
        
        // إعادة ترقيم الأسابيع عند تحميل الصفحة
        renumberWeeks();
    }
}); 